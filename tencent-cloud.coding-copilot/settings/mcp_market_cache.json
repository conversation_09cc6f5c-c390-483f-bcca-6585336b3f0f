{"total_count": 26, "mcp_servers": [{"id": "mcp010001", "mcpid": "cnb.cool/cnb/tools/cnb-mcp-server", "name": "CNB MCP Server", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/cnb.jpg", "description_en": "A comprehensive MCP server that provides seamless integration to the CNB's API(https://cnb.cool), offering a wide range of tools for repository management, pipelines operations and collaboration features.", "description_zh": "一个全面的MCP服务器，提供与CNB的API（https://cnb.cool）的无缝集成，提供广泛的工具，用于仓库管理、管道操作和协作功能。", "official_url": "https://cnb.cool/cnb/tools/cnb-mcp-server", "readme_url": "https://cnb.cool/cnb/tools/cnb-mcp-server/-/git/raw/main/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "-p", "@cnbcool/mcp-server", "cnb-mcp-stdio"], "env": {"API_TOKEN": "<API_TOKEN_HERE>"}}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp010002", "mcpid": "cnb.cool/tapd_mcp/mcp-server-tapd", "name": "TAPD MCP Server", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/tapd.jpg", "description_en": "TAPD is Tencent's agile R&D management platform, covering the entire life cycle of demand, planning, R&D, testing, and release. Support dialogue with TAPD in natural language to achieve management of requirements, defects, tasks, iterations, etc. Seamless integration with TAPD API for improved development efficiency", "description_zh": "TAPD 是腾讯敏捷研发管理平台，覆盖需求、计划、研发、测试、发布研发全生命周期。支持用自然语言与 TAPD 对话，实现需求、缺陷、任务、迭代等管理, 与 TAPD API 无缝集成，提升开发效率", "official_url": "https://cnb.cool/tapd_mcp/mcp-server-tapd", "readme_url": "https://cnb.cool/tapd_mcp/mcp-server-tapd/-/blob/main/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "uvx", "args": ["mcp-server-tapd"], "env": {"BOT_URL": "[Optional] Webhook URL for WeCom Bot", "TAPD_API_BASE_URL": "https://api.tapd.cn", "TAPD_API_PASSWORD": "<TAPD_API_PASSWORD>", "TAPD_API_USER": "<TAPD_API_USER_NAME>", "TAPD_BASE_URL": "https://www.tapd.cn"}}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp010003", "mcpid": "cnb.cool/tencent/cloud/cos/cos-mcp", "name": "Tencent Cloud COS MCP Server", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/cos.png", "description_en": "Tencent Cloud COS MCP Server based on the MCP protocol allows large models to quickly access Tencent Cloud Storage (COS) and Data Vientiane (CI) capabilities without coding.", "description_zh": "基于MCP协议的腾讯云COS MCP Server，无需编码即可让大模型快速接入腾讯云存储(COS)和数据万象(CI)能力。", "official_url": "https://cnb.cool/tencent/cloud/cos/cos-mcp", "readme_url": "https://cnb.cool/tencent/cloud/cos/cos-mcp/-/blob/master/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["cos-mcp"], "env": {"Bucket": "<yourBucket>", "DatasetName": "<yourDatasetname>", "Region": "<yourRegion>", "SecretId": "<yourSecretId>", "SecretKey": "<yourSecret<PERSON>ey>"}}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp010004", "mcpid": "github.com/TencentEdgeOne/edgeone-pages-mcp-pages-mcp", "name": "EdgeOne Pages MCP Server", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/edgeone.png", "description_en": "An MCP service for deploying HTML content to EdgeOne Pages and obtaining a publicly accessible URL.", "description_zh": "一个将HTML内容部署到EdgeOne Pages并获取公开可访问URL的MCP服务。", "official_url": "https://github.com/TencentEdgeOne/edgeone-pages-mcp", "readme_url": "https://github.com/TencentEdgeOne/edgeone-pages-mcp/blob/main/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["edgeone-pages-mcp"]}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp010005", "mcpid": "cs_sandbox", "name": "Cloud Studio Sandbox", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/cssandbox.png", "description_en": "A sandbox to build, test, and execute code.", "description_zh": "一个用于构建、测试和执行代码的沙盒(内测中，需要申请可联系我们)", "official_url": "https://cloudstudio.net", "readme_url": "https://cloudstudio.net", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "uvx", "args": ["mcp-deploy"], "env": {"API_TOKEN": "<API_TOKEN>"}}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp030001", "mcpid": "cnb.cool/shouqianceshi/coding_devops_mcp_server", "name": "CODING DevOps MCP Server", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/coding.png", "description_en": "CODING DevOps MCP Server is a Model Context Protocol (MCP) server implementation that provides standardized interfaces for interacting with the CODING DevOps platform. It enables efficient management of projects, work items, and code through a unified protocol.", "description_zh": "CODING DevOps MCP Server 是一个基于模型上下文协议（MCP）的服务器实现，为 CODING DevOps 平台提供标准化的交互接口。它通过统一的协议实现项目、工作项和代码的高效管理。", "official_url": "https://cnb.cool/shouqianceshi/coding_devops_mcp_server", "readme_url": "https://cnb.cool/shouqianceshi/coding_devops_mcp_server/-/blob/main/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "-p", "coding-mcp-server", "mcp-stdio"], "env": {"CODING_TOKEN": "<CODING_TOKEN>"}}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp030002", "mcpid": "github.com/DemoWayOfficial/mcp-server", "name": "DemoWay AI-Design", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/400w.png", "description_en": "Empower AI to modify pages in your existing web product and generate new designs or frontend code", "description_zh": "让 AI 可以用现有产品的单个页面或一个流程里的所有页面为基础，继续修改生成新的设计或前端代码", "official_url": "https://github.com/DemoWayOfficial/mcp-server", "readme_url": "https://github.com/DemoWayOfficial/mcp-server/blob/main/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "@demoway/mcp-server@latest"], "env": {"DEMOWAY_API_KEY": "<YOUR_API_KEY>"}}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050001", "mcpid": "github.com/modelcontextprotocol/servers/tree/main/src/gitlab", "name": "GitLab", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/gitlab.jpg", "description_en": "MCP Server for the GitLab API, enabling project management, file operations, and more.", "description_zh": "一个GitLab API的MCP服务器，支持项目管理、文件操作等。", "official_url": "https://github.com/modelcontextprotocol/servers/tree/main/src/gitlab", "readme_url": "https://raw.githubusercontent.com/modelcontextprotocol/servers/refs/heads/main/src/gitlab/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-gitlab"], "env": {"GITLAB_API_URL": "https://gitlab.com/api/v4", "GITLAB_PERSONAL_ACCESS_TOKEN": "<TOKEN>"}}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050002", "mcpid": "github.com/modelcontextprotocol/servers/tree/main/src/github", "name": "GitHub", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/github.jpg", "description_en": "MCP Server for the GitHub API, enabling file operations, repository management, search functionality, and more.", "description_zh": "一个GitHub API的MCP服务器，支持文件操作、仓库管理、搜索功能等。", "official_url": "https://github.com/modelcontextprotocol/servers/tree/main/src/github", "readme_url": "https://raw.githubusercontent.com/modelcontextprotocol/servers/refs/heads/main/src/github/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "<YOUR_TOKEN>"}}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050003", "mcpid": "github.com/executeautomation/mcp-playwright", "name": "Playwright MCP Server", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/playwright.jpg", "description_en": "A Model Context Protocol server that provides browser automation capabilities using Playwright. This server enables LLMs to interact with web pages, take screenshots, and execute JavaScript in a real browser environment.", "description_zh": "一个使用Playwright提供浏览器自动化能力的MCP服务器。该服务器使大模型能够在真实浏览器环境中与网页进行交互、进行截图和执行JavaScript。", "official_url": "https://github.com/executeautomation/mcp-playwright", "readme_url": "https://raw.githubusercontent.com/executeautomation/mcp-playwright/refs/heads/main/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"]}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050004", "mcpid": "github.com/r-huijts/xcode-mcp-server", "name": "Xcode", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/Xcode.jpg", "description_en": "A Model Context Protocol (MCP) server that brings the power of AI to your Xcode projects. This server acts as a bridge between CodeBuddy and your local Xcode development environment, enabling intelligent code assistance, project management, and automated development tasks.", "description_zh": "一个将AI能力引入Xcode项目的MCP服务器，作为CodeBuddy与本地Xcode开发环境的桥梁，提供智能代码辅助、项目管理和自动化开发任务。", "official_url": "https://github.com/r-huijts/xcode-mcp-server", "readme_url": "https://github.com/r-huijts/xcode-mcp-server/blob/master/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "xcode-mcp-server"], "env": {"PROJECTS_BASE_DIR": "<path/to/your/xcode/projects>"}}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050005", "mcpid": "github.com/modelcontextprotocol/servers/tree/main/src/brave-search", "name": "Brave Search", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/brave-search.jpg", "description_en": "An MCP server implementation that integrates the Brave Search API, providing both web and local search capabilities.", "description_zh": "Brave Search 是一个 MCP Server，它利用 Brave Search API 提供网络和本地搜索功能。", "official_url": "https://github.com/modelcontextprotocol/servers/tree/main/src/brave-search", "readme_url": "https://github.com/modelcontextprotocol/servers/blob/main/src/brave-search/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "<API_KEY>"}}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050006", "mcpid": "github.com/mendableai/firecrawl-mcp-server", "name": "Firecrawl MCP Server", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/firecrawl.jpg", "description_en": "Advanced web scraping and crawling server with JavaScript rendering, batch processing, smart content filtering, and structured data extraction capabilities.", "description_zh": "高级的Web抓取和爬行MCP服务器，具有JavaScript渲染、批处理、智能内容过滤和结构化数据提取功能。", "official_url": "https://github.com/mendableai/firecrawl-mcp-server", "readme_url": "https://raw.githubusercontent.com/mendableai/firecrawl-mcp-server/refs/heads/main/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "<API_KEY>"}}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050007", "mcpid": "github.com/abhiz123/todoist-mcp-server", "name": "Todoist", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/Todoist.jpg", "description_en": "An MCP (Model Context Protocol) server implementation that integrates CodeBuddy with Todoist, enabling natural language task management. This server allows CodeBuddy to interact with your Todoist tasks using everyday language.", "description_zh": "一个集成Todoist 的 MCP 服务器，支持自然语言任务管理，让用户能用日常语言与 Todoist 任务交互。", "official_url": "https://github.com/abhiz123/todoist-mcp-server", "readme_url": "https://github.com/abhiz123/todoist-mcp-server/blob/main/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "@abhiz123/todoist-mcp-server"], "env": {"TODOIST_API_TOKEN": "<your_api_token_here>"}}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050008", "mcpid": "github.com/tokenizin-agency/mcp-npx-fetch", "name": "MCP NPX Fetch", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/NPXFetch.jpg", "description_en": "MCP NPX Fetch is a powerful MCP server for fetching and transforming web content into various formats (HTML, JSON, Markdown, Plain Text) with ease.", "description_zh": "MCP NPX Fetch 是一款功能强大的 MCP 服务器，可轻松获取网页内容并将其转换为各种格式（HTML、JSON、Markdown、纯文本）。", "official_url": "https://github.com/tokenizin-agency/mcp-npx-fetch", "readme_url": "https://github.com/tokenizin-agency/mcp-npx-fetch/blob/main/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "@tokenizin/mcp-npx-fetch"]}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050009", "mcpid": "github.com/Hawstein/mcp-webresearch", "name": "MCP Web Research Server", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/WebResearch.jpg", "description_en": "WebResearch is a Model Context Protocol (MCP) server for web research.", "description_zh": "WebResearch 是用于网络研究的模型上下文协议（MCP）服务器。", "official_url": "https://github.com/Ha<PERSON>tein/mcp-webresearch", "readme_url": "https://github.com/Hawstein/mcp-webresearch/blob/main/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "@mzxrai/mcp-webresearch@latest"]}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050010", "mcpid": "github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking", "name": "SequentialThinking", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/sequentialthinking.jpg", "description_en": "Sequential thinking is an MCP server implementation that provides a tool for dynamic and reflective problem-solving through a structured thinking process.", "description_zh": "Sequential thinking  MCP 服务器通过结构化的思维过程，为动态和反思性地解决问题提供了一种工具。", "official_url": "https://github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking", "readme_url": "https://github.com/modelcontextprotocol/servers/blob/main/src/sequentialthinking/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050011", "mcpid": "github.com/21st-dev/magic-mcp", "name": "Magic MCP", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/21st_magic.jpg", "description_en": "Create modern Ul components instantly through natural language descriptions, with IDE integrations and access to a vast library of pre-built, customizable components.", "description_zh": "通过自然语言描述立即创建现代Ul组件，具有IDE集成和访问大量预构建、可自定义组件的库。", "official_url": "https://github.com/21st-dev/magic-mcp", "readme_url": "https://raw.githubusercontent.com/21st-dev/magic-mcp/refs/heads/main/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "@21st-dev/magic@latest", "API_KEY=\"your-api-key\""]}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050012", "mcpid": "github.com/GLips/Figma-Context-MCP", "name": "Framelink Figma MCP Server", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/framelink.jpg", "description_en": "Offers tools to view, comment on, and analyze Figma designs, ensuring precise implementation with relevant design context and simplified API responses for smooth design-to-code execution.", "description_zh": "提供查看、评论和分析Figma设计的工具，确保与相关设计上下文的精确实现，并为平滑的设计到代码执行提供简化的API响应。", "official_url": "https://github.com/GLips/Figma-Context-MCP", "readme_url": "https://github.com/GLips/Figma-Context-MCP/blob/main/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "figma-developer-mcp", "--figma-api-key=YOUR-KEY", "--st<PERSON>"]}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050013", "mcpid": "github.com/modelcontextprotocol/servers/tree/main/src/slack", "name": "Slack MCP Server", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/slack.jpg", "description_en": "Enables Al assistants to interact with Slack workspaces, providing tools for messaging, channel management, reactions, user profiles, and thread management.", "description_zh": "使Al助理与Slack工作区进行交互，提供消息、频道管理、反应、用户资料和线程管理的工具。", "official_url": "https://github.com/modelcontextprotocol/servers/tree/main/src/slack", "readme_url": "https://github.com/modelcontextprotocol/servers/blob/main/src/slack/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-slack"], "env": {"SLACK_BOT_TOKEN": "<TOKEN>", "SLACK_CHANNEL_IDS": "<CHANNEL_IDS>", "SLACK_TEAM_ID": "<TEAM_ID>"}}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050014", "mcpid": "github.com/ahujasid/blender-mcp", "name": "BlenderMCP", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/blender.jpg", "description_en": "BlenderMCP connects Blender to AI through the Model Context Protocol (MCP), allowing AI to directly interact with and control Blender. This integration enables prompt assisted 3D modeling, scene creation, and manipulation.", "description_zh": "BlenderMCP通过模型上下文协议（MCP）连接Blender与AI，允许AI直接与Blender进行交互和控制。此集成使AI可以进行提示辅助的3D建模、场景创建和操作。", "official_url": "https://github.com/ahujasid/blender-mcp", "readme_url": "https://github.com/ahujasid/blender-mcp/blob/main/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "uvx", "args": ["blender-mcp"]}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050015", "mcpid": "github.com/modelcontextprotocol/servers/tree/main/src/fetch", "name": "<PERSON>tch", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/fetch.png", "description_en": "A Model Context Protocol server that provides web content fetching capabilities. This server enables LLMs to retrieve and process content from web pages, converting HTML to markdown for easier consumption..", "description_zh": "一个提供网页内容获取能力的模型上下文协议服务器。此服务器使LLMs能够检索和处理网页内容，将HTML转换为markdown，以便更容易消费。", "official_url": "https://github.com/modelcontextprotocol/servers/tree/main/src/fetch", "readme_url": "https://github.com/modelcontextprotocol/servers/blob/main/src/fetch/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "uvx", "args": ["mcp-server-fetch"]}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050016", "mcpid": "github.com/modelcontextprotocol/servers/tree/main/src/puppeteer", "name": "<PERSON><PERSON><PERSON><PERSON>", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/puppeteer.png", "description_en": "Provides browser automation capabilities using Puppeteer, enabling web page interaction, screenshots, and JavaScript execution in a real browser environment.", "description_zh": "使用 Puppeteer 提供浏览器自动化功能，实现在真实浏览器环境中的网页交互、截图和 JavaScript 执行。", "official_url": "https://github.com/modelcontextprotocol/servers/tree/main/src/puppeteer", "readme_url": "https://github.com/modelcontextprotocol/servers/tree/main/src/puppeteer/raw/main/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050017", "mcpid": "github.com/upstash/context7-mcp", "name": "Context7", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/upstash.jpg", "description_en": "Provides up-to-date library documentation and code examples directly in LLM prompts, ensuring accurate and current programming assistance.", "description_zh": "在 LLM 提示中直接提供最新的库文档和代码示例，确保准确和及时的编程辅助。", "official_url": "https://github.com/upstash/context7-mcp", "readme_url": "https://github.com/upstash/context7-mcp/raw/main/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050018", "mcpid": "github.com/jsonallen/perplexity-mcp", "name": "Perplexity", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/perplexity.jpg", "description_en": "A Model Context Protocol (MCP) server that provides web search functionality using Perplexity AI's API", "description_zh": "一个使用 Perplexity AI API 提供网络搜索功能的模型上下文协议（MCP）服务器", "official_url": "https://github.com/jsonallen/perplexity-mcp", "readme_url": "https://github.com/jsonallen/perplexity-mcp/blob/main/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "uvx", "args": ["perplexity-mcp"], "env": {"PERPLEXITY_API_KEY": "XXXXXXXXXXXXXXXXXXXX", "PERPLEXITY_MODEL": "sonar"}}, "categories": [], "preinstalled": false, "tags": []}, {"id": "mcp050019", "mcpid": "github.com/supabase-community/supabase-mcp", "name": "Supabase MCP", "logo_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/mcp_server_logos/supabase.jpg", "description_en": "The Model Context Protocol (MCP) standardizes how Large Language Models (LLMs) talk to external services like Supabase. It connects AI assistants directly with your Supabase project and allows them to perform tasks like managing tables, fetching config, and querying data", "description_zh": "Model Context Protocol (MCP) 标准化了大型语言模型（LLM）如何与 Supabase 等外部服务进行通信。它使 AI 助手能够直接与您的 Supabase 项目连接，并允许它们执行诸如管理表、获取配置和查询数据等任务", "official_url": "https://github.com/supabase-community/supabase-mcp", "readme_url": "https://github.com/supabase-community/supabase-mcp/blob/main/README.md", "install_prompt_en": "", "install_prompt_zh": "", "install_commands": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest"], "env": {"SUPABASE_ACCESS_TOKEN": "<personal-access-token>"}}, "categories": [], "preinstalled": false, "tags": []}], "mcp_market_github": "https://cnb.cool/codebuddy/mcp-market", "mcp_install_prompt_en": "I want to build a MCP plugin that can solve complex math problems, perform unit conversions, and handle scientific notation.\n", "mcp_install_prompt_zh": "我想构建一个MCP插件，它可以解决复杂的数学问题，执行单位转换和处理科学记数法。\n", "mac_npx_install_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/install_scripts/mac_npx_install.sh", "mac_uvx_install_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/install_scripts/mac_uvx_install.sh", "windows_npx_install_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/install_scripts/windows_npx_install.bat", "windows_uvx_install_url": "https://acc-1258344699.cos.accelerate.myqcloud.com/web/mcp_market/install_scripts/windows_uvx_install.bat"}