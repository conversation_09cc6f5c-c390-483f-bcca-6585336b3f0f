@echo off
setlocal enabledelayedexpansion

:: npx installation script - Windows version
:: This script ensures npx is available, while trying to preserve the user's existing Node.js installation

:: Check for administrator privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    goto :Admin
) else (
    echo Administrator privileges required
    echo Please run this script as administrator
    pause
    exit /b
)

:Admin
echo Starting npx installation...

:: Create temporary directory
set "TEMP_DIR=%TEMP%\npx_install"
if exist "%TEMP_DIR%" rd /s /q "%TEMP_DIR%"
mkdir "%TEMP_DIR%"
cd /d "%TEMP_DIR%"

:: 1. First check if npx is already installed
echo Checking if npx is already installed...
where npx >nul 2>&1
if %errorLevel% equ 0 (
    echo npx is already installed, version:
    npx -v
    goto :Cleanup
)

:: 2. Check if Node.js is installed
echo Checking Node.js installation status...
where node >nul 2>&1
if %errorLevel% equ 0 (
    echo Node.js is already installed, version:
    node -v
    
    :: Try to install npx directly
    echo Attempting to install npx via npm...
    npm install -g npx
    where npx >nul 2>&1
    if !errorLevel! equ 0 (
        echo npx installation successful, version:
        npx -v
        goto :Cleanup
    )
    echo Unable to install npx with existing Node.js, will use nvm to install new Node.js version
) else (
    echo Node.js not detected, will install Node.js to get npx
)

:: 3. Download and set up nvm using the nvm-noinstall package
echo Downloading nvm-windows (no-install version)...

:: Use a specific recent version of nvm-windows
set "NVM_VERSION=1.2.2"
set "NVM_ZIP=nvm-noinstall.zip"
set "NVM_URL=https://github.com/coreybutler/nvm-windows/releases/download/%NVM_VERSION%/%NVM_ZIP%"

:: Download the zip file
curl -L -o "%NVM_ZIP%" "%NVM_URL%"

:: Check if the file exists and has a size greater than 0
if not exist "%NVM_ZIP%" (
    echo Failed to download nvm-windows: File not found
    goto :Cleanup
)

for %%A in ("%NVM_ZIP%") do if %%~zA==0 (
    echo Failed to download nvm-windows: File is empty
    goto :Cleanup
)

echo Download successful!

:: Create a permanent NVM_HOME directory
set "NVM_HOME=%ProgramFiles%\nvm"
if exist "%NVM_HOME%" rd /s /q "%NVM_HOME%"
mkdir "%NVM_HOME%" 2>nul

:: Extract the zip file using VBScript (no PowerShell required)
echo Extracting nvm-windows...

:: Create a VBScript to extract the ZIP file
echo Set objShell = CreateObject("Shell.Application") > "%TEMP_DIR%\unzip.vbs"
echo objShell.NameSpace("%NVM_HOME%").CopyHere objShell.NameSpace("%CD%\%NVM_ZIP%").Items >> "%TEMP_DIR%\unzip.vbs"
echo WScript.Sleep 2000 >> "%TEMP_DIR%\unzip.vbs"

:: Run the VBScript
cscript //nologo "%TEMP_DIR%\unzip.vbs"

:: Check if extraction was successful
if not exist "%NVM_HOME%\nvm.exe" (
    echo Failed to extract nvm-windows
    goto :Cleanup
)

:: Set NVM_SYMLINK to the standard location
set "NVM_SYMLINK=%ProgramFiles%\nodejs"
if exist "%NVM_SYMLINK%" rd /s /q "%NVM_SYMLINK%"

:: Configure environment variables
echo Configuring environment variables...
setx NVM_HOME "%NVM_HOME%" /M
setx NVM_SYMLINK "%NVM_SYMLINK%" /M

:: Update PATH environment variable
for /f "skip=2 tokens=2,*" %%A in ('reg query "HKLM\System\CurrentControlSet\Control\Session Manager\Environment" /v Path 2^>nul') do (
  setx /M PATH "%%B;%NVM_HOME%;%NVM_SYMLINK%"
)

:: Determine system architecture
if exist "%SYSTEMDRIVE%\Program Files (x86)\" (
    set SYS_ARCH=64
) else (
    set SYS_ARCH=32
)

:: Create settings.txt file
echo Creating nvm settings file...
(echo root: %NVM_HOME% && echo path: %NVM_SYMLINK% && echo arch: %SYS_ARCH% && echo proxy: none) > "%NVM_HOME%\settings.txt"

:: Update PATH for current session
set "PATH=%NVM_HOME%;%NVM_SYMLINK%;%PATH%"

echo nvm setup complete

:: 4. Use nvm to install Node.js (only when needed)
echo Installing Node.js version that supports npx...

:: Use the full path to nvm.exe
"%NVM_HOME%\nvm.exe" install lts
if %errorLevel% neq 0 (
    echo Failed to install Node.js
    goto :Cleanup
)

"%NVM_HOME%\nvm.exe" use lts
if %errorLevel% neq 0 (
    echo Failed to activate Node.js
    goto :Cleanup
)

echo Node.js installation complete, version:
node -v

:: 5. Verify npx is available
echo Verifying npx...
where npx >nul 2>&1
if %errorLevel% neq 0 (
    echo npx not found, please ensure Node.js is installed correctly
    goto :Cleanup
)

:: Display installation information
echo Installation successful!
echo Node.js version:
node -v
echo npm version:
npm -v
echo npx version:
npx -v
echo.
echo Please reopen Command Prompt or PowerShell to ensure all environment variables take effect

:Cleanup
:: Clean up temporary files
cd /d "%USERPROFILE%"
if exist "%TEMP_DIR%" rd /s /q "%TEMP_DIR%"

endlocal
