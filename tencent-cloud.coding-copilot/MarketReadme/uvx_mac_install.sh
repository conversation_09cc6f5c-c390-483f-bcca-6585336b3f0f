#!/bin/bash
#
# uvx安装脚本 - 安装uvx并配置环境变量
#

# 设置错误处理
set -e
trap 'echo "错误：安装失败，请查看上面的错误信息"; exit 1' ERR

# 彩色输出函数
info() { printf "\033[0;36m%s\033[0m\n" "$1"; }
success() { printf "\033[0;32m%s\033[0m\n" "$1"; }
warning() { printf "\033[0;33m%s\033[0m\n" "$1"; }
error() { printf "\033[0;31m%s\033[0m\n" "$1"; }

info "正在启动uvx安装..."

# 检查是否已安装uvx
if command -v uvx &> /dev/null; then
    success "uvx已经安装。跳过安装过程。"
    exit 0
fi

# 安装uvx
info "正在安装uvx..."
if ! curl -LsSf https://astral.sh/uv/install.sh | sh; then
    error "uvx安装失败"
    exit 1
fi

# 设置环境变量
info "正在配置环境变量..."

# 定义uvx路径
UVX_PATH="$HOME/.local/bin"

# 检查uvx是否已安装到指定路径
if [ ! -f "$UVX_PATH/uvx" ]; then
    warning "警告：在$UVX_PATH中未找到uvx"
    # 尝试查找实际安装位置
    UVX_LOCATION=$(find $HOME -name uvx -type f -executable 2>/dev/null | head -1)
    if [ -n "$UVX_LOCATION" ]; then
        UVX_PATH=$(dirname "$UVX_LOCATION")
        info "找到uvx安装在: $UVX_PATH"
    else
        error "无法找到uvx可执行文件"
        exit 1
    fi
fi

# 确定shell配置文件
SHELL_FILES=()
if [ -n "$SHELL" ]; then
    case "$SHELL" in
        */zsh)
            SHELL_FILES=("$HOME/.zshrc")
            [ -f "$HOME/.zprofile" ] && SHELL_FILES+=("$HOME/.zprofile")
            ;;
        */bash)
            SHELL_FILES=("$HOME/.bashrc")
            [ -f "$HOME/.bash_profile" ] && SHELL_FILES+=("$HOME/.bash_profile")
            ;;
        *)
            SHELL_FILES=("$HOME/.profile")
            ;;
    esac
else
    # 如果$SHELL未定义，尝试检测常见的配置文件
    [ -f "$HOME/.bashrc" ] && SHELL_FILES+=("$HOME/.bashrc")
    [ -f "$HOME/.zshrc" ] && SHELL_FILES+=("$HOME/.zshrc")
    [ -f "$HOME/.profile" ] && SHELL_FILES+=("$HOME/.profile")
fi

# 如果没有找到任何配置文件，创建一个默认的
if [ ${#SHELL_FILES[@]} -eq 0 ]; then
    SHELL_FILES=("$HOME/.profile")
    touch "$HOME/.profile"
fi

# 添加到PATH
PATH_ENTRY="export PATH=\"$UVX_PATH:\$PATH\""
PATH_UPDATED=false

for SHELL_FILE in "${SHELL_FILES[@]}"; do
    info "检查配置文件: $SHELL_FILE"
    
    # 创建备份
    if [ -f "$SHELL_FILE" ]; then
        cp "$SHELL_FILE" "${SHELL_FILE}.bak.$(date +%Y%m%d%H%M%S)"
        info "已创建备份: ${SHELL_FILE}.bak.$(date +%Y%m%d%H%M%S)"
    fi
    
    # 检查PATH是否已存在
    if [ -f "$SHELL_FILE" ] && grep -q "$UVX_PATH" "$SHELL_FILE"; then
        info "PATH已经包含$UVX_PATH在文件$SHELL_FILE中"
    else
        # 添加PATH条目
        printf "\n# 由uvx安装脚本添加 - %s\n" "$(date)" >> "$SHELL_FILE"
        printf "%s\n" "$PATH_ENTRY" >> "$SHELL_FILE"
        info "已将uvx路径添加到$SHELL_FILE"
        PATH_UPDATED=true
    fi
done

# 验证安装
if [ -x "$UVX_PATH/uvx" ]; then
    success "uvx安装成功！"
    
    if [ "$PATH_UPDATED" = true ]; then
        info "请运行以下命令使环境变量生效，或重新打开终端:"
        for SHELL_FILE in "${SHELL_FILES[@]}"; do
            echo "  source $SHELL_FILE"
        done
    fi
    
    # 显示版本信息
    export PATH="$UVX_PATH:$PATH"
    info "uvx版本: $($UVX_PATH/uvx --version 2>/dev/null || echo '无法获取版本信息')"
else
    error "uvx安装验证失败"
    exit 1
fi
