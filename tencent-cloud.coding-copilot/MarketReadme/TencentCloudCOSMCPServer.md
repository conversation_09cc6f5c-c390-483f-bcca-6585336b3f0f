<!DOCTYPE html><html lang="zh"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1,minimum-scale=1, maximum-scale=1, user-scalable=no"/><title>README.md at master · tencent/cloud/cos/cos-mcp</title><meta name="description" content="腾讯云COS mcp服务器"/><meta property="og:site_name" content="Cloud Native Build"/><meta property="og:title" content="README.md at master · tencent/cloud/cos/cos-mcp"/><meta property="og:type" content="object"/><meta property="og:url" content="https://cnb.cool/tencent/cloud/cos/cos-mcp/-/blob/master/README.md"/><meta property="og:image" content="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/images/favicon.png"/><meta property="og:image:alt" content="腾讯云COS mcp服务器"/><meta property="og:description" content="腾讯云COS mcp服务器"/><meta name="next-head-count" content="11"/><meta property="og:type" content="website"/><meta property="og:site_name" content="https://cnb.cool"/><meta name="apple-mobile-web-app-capable" content="yes"/><meta name="mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-status-bar-style" content="black"/><meta name="google" content="notranslate"/><link rel="icon" type="image/svg+xml" href="/images/favicon.svg"/><link rel="icon" type="image/png" href="/images/favicon.png"/><script id="cnb-env-script-1" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" data-nscript="beforeInteractive" crossorigin="anonymous">if (!window.process) {window.process = {};}</script><script id="cnb-env-script-2" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" data-nscript="beforeInteractive" crossorigin="anonymous">if (!window.process.env) {window.process.env = {};}</script><script id="cnb-env-script-3" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" data-nscript="beforeInteractive" crossorigin="anonymous">window.process.env = JSON.parse('{"API_LINK":"https://api.cnb.cool","API_PREFIX":"/","APP_MAX_ORG_LEVEL":"10","CNB_DEFAULT_DEV_DOCKER_IMAGE":"cnbcool/default-dev-env:latest","CNB_DEFAULT_DOCKER_IMAGE":"cnbcool/default-build-env:latest","CNB_DEFAULT_LANGUAGE":"__CNB_DEFAULT_LANGUAGE__","CNB_DISTRIBUTION":"SAAS","CNB_DOCKER_REGISTRY":"docker.cnb.cool","CNB_HELM_REGISTRY":"helm.cnb.cool","CNB_LANGUAGE":"_cnb_language","CNB_LINK":"https://cnb.cool","CNB_SAAS_DOMAIN_CNB_COOL":"cnb.cool","DISABLE_SECRET_REPO":"false","DOCS_LINK":"https://docs.cnb.cool","DOMAIN":"cnb.cool","FEEDBACK_LINK":"https://cnb.cool/cnb/feedback","FRONTEND_WEB_FINGERPRINT_ID":"12216581d5b61465","GIT_SIZE_LIMIT_FOR_VSCODE":"","GIT_SSH_ENABLED":false,"LOCALE_RUNTIME":"zh","LOGIN_PATH":"","MOCK_LOGIN":"0","MODULE_SWITCH":"group-artifactory,tapd,tapd-use-wx,cnb-knowledge-base,contributors,slugSecurity","NEXT_APP_VERISON":"6fdd67e1","ORIGIN":"https://cnb.cool","PROTOCOL":"https","SHOWCASE_LINK":"https://cnb.cool/examples/showcase","STUDY_CENTER_LINK":"https://https://cnb.cool/examples/showcase","TENCENT_CLOUD_RUM_ENABLED":true,"TENCENT_CLOUD_RUM_ID":"p0w1GTkrl6pmRj7yVZ","WEB_TRIGGER_DEFAULT_NAME":"web_trigger","WECHATBASEURL":"https://open.weixin.qq.com/qr/code?username=","WECHAT_WEBAPP_SERVICE_APPID":"wx8003a7793647fe7d"}');</script><script id="cnb-trace-script" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" data-nscript="beforeInteractive" crossorigin="anonymous">window.traceID = 'fc4bede730e436726a8a1f3b780fa76e';</script><link nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" rel="preload" href="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/css/2b3a100792e397ad.css" as="style" crossorigin="anonymous"/><link nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" rel="stylesheet" href="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/css/2b3a100792e397ad.css" crossorigin="anonymous" data-n-g=""/><link nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" rel="preload" href="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/css/716ee3f70122cd24.css" as="style" crossorigin="anonymous"/><link nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" rel="stylesheet" href="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/css/716ee3f70122cd24.css" crossorigin="anonymous" data-n-p=""/><noscript data-n-css="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm"></noscript><script defer="" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" crossorigin="anonymous" nomodule="" src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/chunks/polyfills-42372ed130431b0a.js"></script><script defer="" src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/chunks/4102.js" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" crossorigin="anonymous"></script><script src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/chunks/webpack.js" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" defer="" crossorigin="anonymous"></script><script src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/chunks/framework.js" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" defer="" crossorigin="anonymous"></script><script src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/chunks/runtime.js" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" defer="" crossorigin="anonymous"></script><script src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/chunks/main.js" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" defer="" crossorigin="anonymous"></script><script src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/chunks/vendors.js" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" defer="" crossorigin="anonymous"></script><script src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/chunks/pages/_app.js" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" defer="" crossorigin="anonymous"></script><script src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/chunks/design-768144a1.js" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" defer="" crossorigin="anonymous"></script><script src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/chunks/design-e3027acb.js" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" defer="" crossorigin="anonymous"></script><script src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/chunks/design-0ae76235.js" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" defer="" crossorigin="anonymous"></script><script src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/chunks/5650.js" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" defer="" crossorigin="anonymous"></script><script src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/chunks/2803.js" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" defer="" crossorigin="anonymous"></script><script src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/chunks/commons.js" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" defer="" crossorigin="anonymous"></script><script src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/chunks/pages/%5B...slug%5D.js" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" defer="" crossorigin="anonymous"></script><script src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/12216581d5b61465/_buildManifest.js" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" defer="" crossorigin="anonymous"></script><script src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/_next/static/12216581d5b61465/_ssgManifest.js" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" defer="" crossorigin="anonymous"></script><style id="__jsx-94813216">.container.jsx-94813216{opacity:0;pointerevents:none;-webkit-transition:opacity 200ms linear;-moz-transition:opacity 200ms linear;-o-transition:opacity 200ms linear;transition:opacity 200ms linear}.bar.jsx-94813216{background:#29d;height:2px;left:0;margin-left:-100%;position:fixed;top:0;-webkit-transition:margin-left 200ms linear;-moz-transition:margin-left 200ms linear;-o-transition:margin-left 200ms linear;transition:margin-left 200ms linear;width:100%;z-index:1031}.spinner.jsx-94813216{-webkit-box-shadow:0 0 10px#29d,0 0 5px#29d;-moz-box-shadow:0 0 10px#29d,0 0 5px#29d;box-shadow:0 0 10px#29d,0 0 5px#29d;display:block;height:100%;opacity:1;position:absolute;right:0;-webkit-transform:rotate(3deg)translate(0px,-4px);-moz-transform:rotate(3deg)translate(0px,-4px);-ms-transform:rotate(3deg)translate(0px,-4px);-o-transform:rotate(3deg)translate(0px,-4px);transform:rotate(3deg)translate(0px,-4px);width:100px}</style></head><body class="pc"><img alt="logo" loading="lazy" width="0" height="0" decoding="async" data-nimg="1" style="color:transparent" src="/images/favicon.png"/><div id="__next"><script src="https://tam.cdn-go.cn/aegis-sdk/latest/aegis.min.js?max_age=3600"></script><script nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm">
              window.aegis = new Aegis({
                id: 'p0w1GTkrl6pmRj7yVZ', // 应用ID，即上报ID
                uin: 'fc4bede730e436726a8a1f3b780fa76e', // 用户唯一 ID（可选）
                reportApiSpeed: true, // 接口测速
                reportAssetSpeed: true, // 静态资源测速
                hostUrl: 'https://rumt-zh.com', // 上报域名，中国大陆 rumt-zh.com
                spa: true, // spa 应用页面跳转的时候开启 pv 计算
                pagePerformance: {
                  // 动态路由需要聚合页面，便于统计页面实际性能
                  urlHandler: e=>{try{let t="https?://",a={issueDetail:RegExp(`${t}\\S+/-/issues`,"g"),pullDetail:RegExp(`${t}\\S+/-/pulls`,"g"),tagDetail:RegExp(`${t}\\S+/-/releases/tag`,"g"),repoDetail:RegExp(`${t}\\S+/-/tree`,"g"),buildLogDetail:RegExp(`${t}\\S+/-/build/logs`,"g"),invitationsDetail:RegExp(`${t}\\S+/invitations`,"g"),userPage:RegExp(`${t}\\S+/u/[a-zA-Z0-9]+`,"g")},s=Object.keys(a)||[],r=e||window.location.href;for(let e=0;e<s.length;e++){let t=s[e];if(a?.[t]?.test(r))return`${location.origin}/${t}`}return r}catch(e){}}
                },
                delay: 3000,
                repeat: 5
              });
            </script><script nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm">!function(){try{var d=document.documentElement,c=d.classList;c.remove('light','dark');var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';c.add('dark')}else{d.style.colorScheme = 'light';c.add('light')}}else if(e){c.add(e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><div class="flex flex-col flex-1 cnb-layout-container"><div class="jsx-94813216 container"><div class="jsx-94813216 bar"><div class="jsx-94813216 spinner"></div></div></div><div style="height:64px" class="w-full flex items-center bg-cover bg-header JS-cnb-header"><div class="flex justify-between lg:px-8 px-4 w-full h-8"><div class="flex overflow-hidden items-center mr-10 max-lg:mr-2"><a href="/" target="_self"><svg id="logo-monochrome" style="width:32px;height:32px" class="ruyi-icon mr-2 transition-transform rotate-0 hover:rotate-180 duration-300 ease-[cubic-bezier(.56,-0.53,.79,1.39)] text-white bg-transparent cursor-pointer ruyi-icon-logo-monochrome" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M11.5286 1.87149C11.5769 1.73005 11.5356 1.5733 11.4233 1.47452C11.0472 1.14247 10.0965 0.443125 8.66911 0.339708C7.07054 0.223769 6.08089 0.652279 5.58096 0.969951C5.36531 1.10676 5.35326 1.41748 5.55499 1.57422L9.62723 4.73936C9.98617 5.01807 10.5125 4.8604 10.6591 4.43003L11.5286 1.87149Z" fill="currentColor"></path><path d="M1.49017 11.2664C1.32368 11.3781 1.24855 11.584 1.30235 11.7774C1.45724 12.3339 1.91868 13.4919 3.22833 14.5456C4.53797 15.5992 6.08738 15.7128 6.74962 15.6966C6.94764 15.692 7.12016 15.5617 7.17998 15.3724L9.79046 7.11064C9.97875 6.51425 9.31048 6.01386 8.79154 6.3626L1.49017 11.2664Z" fill="currentColor"></path><path d="M3.39813 2.54827C3.27013 2.49773 3.12683 2.50607 3.00579 2.57193C2.52256 2.83488 1.28526 3.64506 0.647135 5.30947C0.154627 6.59222 0.328071 8.01085 0.463488 8.70463C0.508009 8.9314 0.747306 9.06218 0.962489 8.97824L8.79485 5.92024C9.35414 5.70181 9.35646 4.91111 8.7981 4.6899L3.39813 2.54827Z" fill="currentColor"></path><path d="M15.0167 8.46843C15.243 8.62194 15.5528 8.48652 15.5922 8.21569C15.6961 7.49872 15.7861 6.25076 15.371 5.30933C14.8177 4.05487 13.8786 3.28133 13.433 2.9669C13.292 2.86766 13.1019 2.87786 12.9725 2.99241L10.9959 4.74541C10.6732 5.03154 10.7066 5.54492 11.0636 5.78746L15.0167 8.46936V8.46843Z" fill="currentColor"></path><path d="M9.49413 15.1604C9.47372 15.3937 9.67128 15.5866 9.90409 15.5616C10.6531 15.4813 12.1918 15.1841 13.3447 14.0827C14.467 13.0109 14.832 11.7384 14.9382 11.2319C14.9669 11.0951 14.9326 10.9528 14.8445 10.8442L11.3886 6.57909C11.0143 6.11719 10.2681 6.34535 10.2162 6.93757L9.49366 15.1604H9.49413Z" fill="currentColor"></path></g></svg></a><div class="text-xl max-lg:text-base text-white truncate flex-1" style="direction:rtl"><span style="direction:ltr;unicode-bidi:embed" class="italic leading-normal font-Hack"><a href="/tencent" target="_self"><span class="font-normal opacity-70"><span class="hover:underline underline-offset-2">tencent</span><span class="mx-1">/</span></span></a><a href="/tencent/cloud" target="_self"><span class="font-normal opacity-70"><span class="hover:underline underline-offset-2">cloud</span><span class="mx-1">/</span></span></a><a href="/tencent/cloud/cos" target="_self"><span class="font-normal opacity-70"><span class="hover:underline underline-offset-2">cos</span><span class="mx-1">/</span></span></a><a href="/tencent/cloud/cos/cos-mcp" target="_self"><span class="mr-1 font-bold"><span class="hover:underline underline-offset-2">cos-mcp</span></span></a></span></div><span class="text-white px-1 h-6 ml-1 max-xl:hidden opacity-70 border-[0.5px] text-sm leading-6 font-semibold rounded text-center">Public</span></div><div class="flex gap-2 text-white"><div class="px-2 py-[5px] rounded-[3px] bg-white/[0.15] hover:bg-white/25 cursor-pointer"><form class="items-center h-full" role="search" aria-label="Site" data-unscoped-search-url="/search" accept-charset="UTF-8" method="get"><div class="flex pr-4 max-md:pr-0 h-full items-center tracking-[1px] text-base text-white/50"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"><path d="M10 10L14 14M11.5 6.5C11.5 9.26142 9.26142 11.5 6.5 11.5C3.73858 11.5 1.5 9.26142 1.5 6.5C1.5 3.73858 3.73858 1.5 6.5 1.5C9.26142 1.5 11.5 3.73858 11.5 6.5Z" stroke="white" stroke-opacity="0.5"></path></svg><div class="flex items-center max-laptop:hidden whitespace-nowrap"><span> <!-- -->Type<!-- --> </span><svg xmlns="http://www.w3.org/2000/svg" width="12" height="14" viewBox="0 0 12 14" fill="none" class="mx-1"><rect x="0.25" y="0.25" width="11.5" height="13.5" rx="1.75" stroke="white" stroke-opacity="0.5" stroke-width="0.5"></rect><path d="M3 12L9 2" stroke="white" stroke-opacity="0.5"></path></svg><span> <!-- -->to search</span></div></div></form></div><div class="px-2 text-[11px] cursor-pointer text-white/90 font-semibold flex items-center rounded-[3px] hover:bg-white/25 max-xl:hidden"><div class="flex items-center min-w-7"><svg id="fork" style="width:16px;height:16px" class="ruyi-icon ruyi-icon-fork" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M4 5.5C4.82843 5.5 5.5 4.82843 5.5 4C5.5 3.17157 4.82843 2.5 4 2.5C3.17157 2.5 2.5 3.17157 2.5 4C2.5 4.82843 3.17157 5.5 4 5.5ZM4 5.5V7.5C4 7.77614 4.22386 8 4.5 8H8M12 5.5C12.8284 5.5 13.5 4.82843 13.5 4C13.5 3.17157 12.8284 2.5 12 2.5C11.1716 2.5 10.5 3.17157 10.5 4C10.5 4.82843 11.1716 5.5 12 5.5ZM12 5.5V7.5C12 7.77614 11.7761 8 11.5 8H8M8 10.5C7.17157 10.5 6.5 11.1716 6.5 12C6.5 12.8284 7.17157 13.5 8 13.5C8.82843 13.5 9.5 12.8284 9.5 12C9.5 11.1716 8.82843 10.5 8 10.5ZM8 10.5V8" stroke="currentColor"></path></g></svg><span class="ml-1">0</span></div></div><div class="px-2 text-[11px] cursor-pointer text-white/90 font-semibold flex items-center rounded-[3px] hover:bg-white/25"><div class="flex items-center min-w-7"><svg id="star" style="width:16px;height:16px" class="ruyi-icon ruyi-icon-star" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M9.54158 6.38026L8.00145 3.25962L6.46132 6.38026L3.01749 6.88068L5.50947 9.30976L4.9212 12.7397L8.00145 11.1203L11.0817 12.7397L10.4934 9.30976L12.9854 6.88068L9.54158 6.38026ZM14.5328 6.09503C14.7789 6.13078 14.8771 6.43317 14.6991 6.60674L11.5679 9.65888L12.3071 13.9686C12.3491 14.2136 12.0919 14.4005 11.8718 14.2848L8.00145 12.2501L4.13111 14.2848C3.91102 14.4005 3.65379 14.2136 3.69582 13.9686L4.43499 9.65888L1.30382 6.60674C1.12576 6.43318 1.22401 6.13078 1.47008 6.09503L5.79726 5.46625L7.73243 1.54516C7.84248 1.32219 8.16043 1.32219 8.27047 1.54516L10.2056 5.46625L14.5328 6.09503Z" fill="currentColor"></path></g></svg><span class="ml-1">0</span></div></div><div data-select-id="layout-header-new-button-popup" class="w-8 h-8 flex items-center justify-center overflow-hidden
         rounded-[3px] cursor-pointer hover:bg-white/[0.25] max-xl:hidden"><svg id="super-add" style="width:16px;height:16px" class="ruyi-icon ruyi-icon-super-add" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M2.5 8H13.5M8 2.5V13.5" stroke="currentColor" stroke-width="1.3"></path></g></svg></div><div class="px-3 py-1.25 rounded text-white bg-white/[0.15] flex items-center cursor-pointer whitespace-nowrap hover:bg-white/[0.25]"><span class="text-base mr-1.5 leading-4"><svg id="wechat" style="width:16px;height:16px" class="ruyi-icon ruyi-icon-wechat" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M10.8279 5.95162C10.8435 5.95247 10.861 5.95343 10.8824 5.95447C10.515 3.99697 8.56361 2.5 6.12982 2.5C3.43556 2.5 1.25146 4.33455 1.25146 6.59736C1.25146 7.83216 1.90865 8.94352 2.93702 9.69457C3.01964 9.75397 3.07367 9.85138 3.07367 9.96175C3.07367 9.99805 3.06597 10.0312 3.05643 10.0661L3.01853 10.2086C2.93913 10.5072 2.84191 10.8728 2.83668 10.8926C2.83416 10.9021 2.8313 10.9117 2.82843 10.9214C2.81957 10.9513 2.81052 10.9817 2.81052 11.0124C2.81052 11.1029 2.88324 11.1761 2.97308 11.1761C3.00852 11.1761 3.03724 11.1631 3.06694 11.1457L4.13516 10.5242C4.21534 10.4776 4.30028 10.4489 4.39402 10.4489C4.44401 10.4489 4.49205 10.4564 4.53739 10.4703C5.03569 10.6148 5.5731 10.695 6.12982 10.695C6.21977 10.695 6.48571 10.6989 6.57445 10.695M13.3438 12.2652C14.2008 11.6392 14.7483 10.7131 14.7483 9.68424C14.7483 7.79836 12.9283 6.26986 10.683 6.26986C8.43791 6.26986 6.61766 7.79836 6.61766 9.68424C6.61766 11.5701 8.43791 13.0989 10.683 13.0989C11.1468 13.0989 11.5948 13.032 12.0101 12.9117C12.0478 12.9001 12.0879 12.8937 12.1296 12.8937C12.2076 12.8937 12.2785 12.9176 12.3455 12.9566L13.2352 13.4746C13.2602 13.4888 13.2842 13.5 13.3137 13.5C13.3886 13.5 13.4492 13.4388 13.4492 13.3635C13.4492 13.3382 13.4418 13.3131 13.4345 13.2885C13.432 13.2801 13.4295 13.2718 13.4274 13.2635C13.4222 13.2442 13.3127 12.8321 13.2441 12.5748C13.2363 12.5458 13.2299 12.5179 13.2299 12.4878C13.2299 12.3961 13.2748 12.3147 13.3438 12.2652Z" stroke="currentColor" stroke-linejoin="round"></path><path d="M8.85107 8.61883C8.85107 8.91181 9.08688 9.14926 9.37758 9.14926C9.66841 9.14926 9.90409 8.91181 9.90409 8.61883C9.90409 8.32572 9.66841 8.08813 9.37758 8.08813C9.08688 8.08813 8.85107 8.32572 8.85107 8.61883ZM11.6626 8.61883C11.6626 8.91181 11.898 9.14926 12.1889 9.14926C12.4797 9.14926 12.7154 8.91181 12.7154 8.61883C12.7154 8.32572 12.4797 8.08813 12.1889 8.08813C11.898 8.08813 11.6626 8.32572 11.6626 8.61883Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path><path d="M3.77539 5.19011C3.77539 5.52303 4.04327 5.79307 4.37391 5.79307C4.70404 5.79307 4.97192 5.52303 4.97192 5.19011C4.97192 4.85707 4.70404 4.58716 4.37391 4.58716C4.04327 4.58716 3.77539 4.85707 3.77539 5.19011Z" fill="currentColor"></path><path d="M7.14895 5.19011C7.14895 5.52303 7.41683 5.79294 7.74721 5.79294C8.07759 5.79294 8.34547 5.52303 8.34547 5.19011C8.34547 4.85707 8.07759 4.58716 7.74721 4.58716C7.41683 4.58716 7.14895 4.85707 7.14895 5.19011Z" fill="currentColor"></path></g></svg></span>Login</div></div></div></div><div class="w-full h-full flex-1 relative"><div class="w-2xl h-full mx-auto max-w-full"><div class="flex flex-col w-full h-full 2xl:max-2.5xl:px-16 laptop:max-2xl:px-8 max-laptop:px-4"><div class="max-xl:h-auto w-full z-[1] relative overflow-hidden" style="height:75px"><div class="flex mx-auto pt-2 mb-6 max-xl:mb-4 border-b border-b-gray-300 max-w-screen-content w-2xl max-2.5xl:w-full max-2.5xl:max-w-2xl"><a class="group flex relative shrink-0 items-center px-4 max-xl:px-[10px] py-2 cursor-pointer text-black-600 hover:text-brand hover:stroke-semibold hover:safari-no-stroke-semibold" href="/tencent/cloud/cos/cos-mcp" target="_self"><svg id="code" class="ruyi-icon mr-1 ruyi-icon-code" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M8.94058 2.55811L6.09357 13.1833L7.0595 13.4421L9.90651 2.81692L8.94058 2.55811Z" fill="currentColor"></path><path d="M2.15006 8.00014L5.57519 11.4253L4.86809 12.1324L1.16011 8.4244C0.925795 8.19008 0.925797 7.81018 1.16011 7.57587L4.86809 3.8679L5.57519 4.575L2.15006 8.00014Z" fill="currentColor"></path><path d="M13.8463 8.00013L10.4057 11.4017L11.1087 12.1128L14.8371 8.42681C15.0746 8.19195 15.0746 7.80831 14.8371 7.57345L11.1087 3.88744L10.4057 4.59857L13.8463 8.00013Z" fill="currentColor"></path></g></svg><span class="text-base">Code</span></a><a class="group flex relative shrink-0 items-center px-4 max-xl:px-[10px] py-2 cursor-pointer text-black-600 hover:text-brand hover:stroke-semibold hover:safari-no-stroke-semibold" href="/tencent/cloud/cos/cos-mcp/-/issues" target="_self"><svg id="issue" class="ruyi-icon mr-1 ruyi-icon-issue" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M6.5 2.5H3.5C3.22386 2.5 3 2.72386 3 3V13C3 13.2761 3.22386 13.5 3.5 13.5H12.5C12.7761 13.5 13 13.2761 13 13V8.5M5.99999 11H7.99999H9.99999M6 11H8H10" stroke="currentColor"></path><path d="M10.7487 6.5777L13.0344 3.37772C13.039 3.37125 13.0391 3.3632 13.0335 3.35757C12.9238 3.24771 11.837 2.15954 11.6688 1.99277C11.6631 1.9872 11.6551 1.98725 11.6486 1.99185L8.4486 4.27759M10.7487 6.5777L9.59866 5.42764M10.7487 6.5777L11.6688 7.49774M8.4486 4.27759L7.52856 3.35755M8.4486 4.27759L9.59866 5.42764M9.59866 5.42764L7.13086 7.95992" stroke="currentColor"></path></g></svg><span class="text-base w-[42px] text-center">Issues</span></a><a class="group flex relative shrink-0 items-center px-4 max-xl:px-[10px] py-2 cursor-pointer text-black-600 hover:text-brand hover:stroke-semibold hover:safari-no-stroke-semibold" href="/tencent/cloud/cos/cos-mcp/-/pulls" target="_self"><svg id="merge" class="ruyi-icon mr-1 ruyi-icon-merge" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M7 4L6.64645 3.64645C6.45118 3.84171 6.45118 4.15829 6.64645 4.35355L7 4ZM4.5 10.5V5.5H3.5V10.5H4.5ZM12.5 10.5V4.5H11.5V10.5H12.5ZM11.5 3.5H7V4.5H11.5V3.5ZM8.14645 2.14645L6.64645 3.64645L7.35355 4.35355L8.85355 2.85355L8.14645 2.14645ZM6.64645 4.35355L8.14645 5.85355L8.85355 5.14645L7.35355 3.64645L6.64645 4.35355ZM5 4C5 4.55228 4.55228 5 4 5V6C5.10457 6 6 5.10457 6 4H5ZM4 5C3.44772 5 3 4.55228 3 4H2C2 5.10457 2.89543 6 4 6V5ZM3 4C3 3.44772 3.44772 3 4 3V2C2.89543 2 2 2.89543 2 4H3ZM4 3C4.55228 3 5 3.44772 5 4H6C6 2.89543 5.10457 2 4 2V3ZM5 12C5 12.5523 4.55228 13 4 13V14C5.10457 14 6 13.1046 6 12H5ZM4 13C3.44772 13 3 12.5523 3 12H2C2 13.1046 2.89543 14 4 14V13ZM3 12C3 11.4477 3.44772 11 4 11V10C2.89543 10 2 10.8954 2 12H3ZM4 11C4.55228 11 5 11.4477 5 12H6C6 10.8954 5.10457 10 4 10V11ZM13 12C13 12.5523 12.5523 13 12 13V14C13.1046 14 14 13.1046 14 12H13ZM12 13C11.4477 13 11 12.5523 11 12H10C10 13.1046 10.8954 14 12 14V13ZM11 12C11 11.4477 11.4477 11 12 11V10C10.8954 10 10 10.8954 10 12H11ZM12 11C12.5523 11 13 11.4477 13 12H14C14 10.8954 13.1046 10 12 10V11ZM12.5 4.5C12.5 3.94772 12.0523 3.5 11.5 3.5V4.5L11.5 4.5H12.5Z" fill="currentColor"></path></g></svg><span class="text-base">Pull requests</span></a><a class="group flex relative shrink-0 items-center px-4 max-xl:px-[10px] py-2 cursor-pointer text-black-600 hover:text-brand hover:stroke-semibold hover:safari-no-stroke-semibold" href="/tencent/cloud/cos/cos-mcp/-/build/logs" target="_self"><svg id="logo-outline" class="ruyi-icon mr-1 ruyi-icon-logo-outline" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M8.54272 1.95011L8.54265 1.95011C7.97344 1.90887 7.5174 1.95599 7.16177 2.03716L9.68908 3.99922L10.1938 2.51587C9.84335 2.28104 9.28281 2.00367 8.54272 1.95011ZM9.48351 5.02318L5.77175 2.14159C5.58787 1.99888 5.59886 1.716 5.79542 1.59144C6.25109 1.30223 7.15314 0.912105 8.6102 1.01766C9.91128 1.11181 10.7778 1.74851 11.1206 2.05081C11.2229 2.14074 11.2606 2.28345 11.2166 2.41222L10.424 4.74156C10.2904 5.13338 9.81068 5.27693 9.48351 5.02318Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path><path d="M2.06675 10.9654C1.915 11.0672 1.84652 11.2547 1.89555 11.4307C2.03674 11.9374 2.45733 12.9916 3.65104 13.9509C4.84476 14.9102 6.25701 15.0136 6.86063 14.9988C7.04112 14.9946 7.19837 14.876 7.2529 14.7037L9.6323 7.18201C9.80391 6.63904 9.1948 6.18347 8.72179 6.50098L2.06675 10.9654ZM6.47746 14.0553L8.45428 7.80621L2.91532 11.522C3.09612 11.9586 3.47135 12.6072 4.23666 13.2222C4.99926 13.835 5.89482 14.015 6.47746 14.0553Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path><path d="M2.17114 5.87737L2.17098 5.87779C1.93535 6.49078 1.90916 7.17553 1.95175 7.73628L7.59553 5.53533L3.69523 3.99027C3.27841 4.25933 2.56746 4.84486 2.17114 5.87737ZM3.80581 3.0285C3.68915 2.98248 3.55853 2.99008 3.44821 3.05004C3.00775 3.28943 1.87998 4.02703 1.29834 5.54235C0.849426 6.71019 1.00752 8.00173 1.13095 8.63336C1.17153 8.83982 1.38964 8.95889 1.58578 8.88247L8.72481 6.0984C9.2346 5.89954 9.23671 5.17967 8.72777 4.97828L3.80581 3.0285Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path><path d="M14.396 8.418C14.6022 8.55775 14.8846 8.43446 14.9205 8.18789C15.0152 7.53515 15.0972 6.39898 14.7189 5.54189C14.2146 4.39981 13.3587 3.69556 12.9524 3.4093C12.8239 3.31895 12.6506 3.32824 12.5327 3.43252L10.7311 5.02848C10.4369 5.28899 10.4674 5.75637 10.7928 5.97719L14.396 8.418ZM12.779 4.46332L11.6723 5.44372L14.0647 7.06435C14.0597 6.65338 14.0058 6.2416 13.8637 5.91952M12.779 4.46332C13.1309 4.77427 13.5727 5.2606 13.8637 5.91952Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path><path d="M10.3613 13.818C10.9525 13.6717 11.6644 13.3896 12.2265 12.8532C12.9188 12.1928 13.2248 11.4231 13.3523 10.9723L10.8797 7.92423L10.3825 13.5759L10.3613 13.818ZM9.36174 14.5107C9.36175 14.5107 9.36174 14.5108 9.36174 14.5107C9.36159 14.5125 9.36187 14.5143 9.36174 14.5161C9.36053 14.5329 9.36055 14.5494 9.36174 14.5657C9.36187 14.5674 9.36201 14.5692 9.36216 14.5709C9.37851 14.7551 9.54405 14.8966 9.73583 14.876C10.4185 14.803 11.821 14.5323 12.8719 13.5296C13.8948 12.5538 14.2275 11.3953 14.3243 10.9342C14.3505 10.8097 14.3192 10.6801 14.2389 10.5813L11.0889 6.69818C10.7478 6.27766 10.0677 6.48538 10.0203 7.02455L9.36174 14.5107Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></g></svg><span class="text-base">Events</span></a><a class="group flex relative shrink-0 items-center px-4 max-xl:px-[10px] py-2 cursor-pointer text-black-600 hover:text-brand hover:stroke-semibold hover:safari-no-stroke-semibold" href="/tencent/cloud/cos/cos-mcp/-/packages" target="_self"><svg id="cube" class="ruyi-icon mr-1 ruyi-icon-cube" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M14.4953 4.24999L8.46882 0.770621C8.17876 0.603152 7.82139 0.603152 7.53132 0.770621L1.50488 4.24999V11.2087C1.50488 11.5437 1.68357 11.8532 1.97363 12.0206L8.00007 15.5L14.0265 12.0206C14.3166 11.8532 14.4953 11.5437 14.4953 11.2087V4.24999ZM7.99907 7.42199L3.00419 4.53826L8.00007 1.65399L12.9954 4.53845L7.99907 7.42199ZM8.50007 8.28866L13.4951 5.4048V11.172L8.50007 14.0554V8.28866ZM7.50007 8.28866V14.0554L2.50407 11.172V5.40422L7.50007 8.28866Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></g></svg><span class="text-base">Packages</span></a><a class="group flex relative shrink-0 items-center px-4 max-xl:px-[10px] py-2 cursor-pointer text-black-600 hover:text-brand hover:stroke-semibold hover:safari-no-stroke-semibold" href="/tencent/cloud/cos/cos-mcp/-/contributors" target="_self"><svg id="chart" class="ruyi-icon mr-1 ruyi-icon-chart" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M4.49999 12L4.5 7.49994L5.5 7.49994L5.49999 12H4.49999Z" fill="currentColor"></path><path d="M7.5 4.5L7.5 12H8.5L8.5 4.5H7.5Z" fill="currentColor"></path><path d="M10.5 12L10.5 9L11.5 9L11.5 12H10.5Z" fill="currentColor"></path><path d="M2 3C2 2.44771 2.44772 2 3 2H13C13.5523 2 14 2.44772 14 3L14 13C14 13.5523 13.5523 14 13 14L3 14C2.44771 14 2 13.5523 2 13V3ZM3 3L3 13L13 13L13 3L3 3Z" fill="currentColor"></path></g></svg><span class="text-base">Insights</span></a></div></div><div class="w-[inherit] flex-auto"><div class="flex justify-center h-full" style="padding-bottom:40px"><div class="flex-1 h-full min-w-0"><div class="flex flex-col h-full"><div class="flex items-center w-full"><div class="flex items-center w-4 h-4 relative"><span class="t-trigger"><svg id="sidebar-right" style="width:16px;height:16px" class="ruyi-icon cursor-pointer ruyi-icon-sidebar-right" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M13 14H3C2.44772 14 2 13.5523 2 13V3C2 2.44772 2.44772 2 3 2H13M13 14C13.5523 14 14 13.5523 14 13V3C14 2.44772 13.5523 2 13 2M13 14H10.5V2H13" stroke="currentColor" stroke-opacity="0.9"></path><path d="M5.04753 5.53027L7.5065 7.98923L5.02604 10.4703" stroke="currentColor" stroke-opacity="0.9"></path></g></svg><div class="w-2 h-2 bg-brand absolute top-[-2px] right-[-2px] rounded-full hidden max-laptop:block"></div></span></div><div class="flex overflow-x-hidden items-center ml-1"><div class="text-base italic font-normal truncate font-Hack text-black-900" style="direction:rtl"><a style="direction:ltr;unicode-bidi:embed" class="cursor-pointer hover:text-blue-600 hover:underline underline-offset-2" href="/tencent/cloud/cos/cos-mcp/-/tree/master" target="_self">cos-mcp</a><span class="mx-1">/</span><a href="/tencent/cloud/cos/cos-mcp/-/blob/master/README.md" class="w-auto align-bottom cursor-pointer hover:text-blue-600 hover:underline underline-offset-2" target="_self">README.md</a></div><div class="inline-flex items-center justify-center cursor-pointer text-black-750 ml-1 w-4 h-4 "><div class="flex w-full h-full items-center justify-center"><svg fill="none" viewBox="0 0 24 24" width="1em" height="1em" class="t-icon t-icon-file-copy"><path fill="currentColor" d="M6 1h9.41L21 6.59V19H6V1zm2 2v14h11V9h-6V3H8zm7 .41V7h3.59L15 3.41zM4 5v16h11v2H2V5h2z"></path></svg></div></div><div class="ml-3 bg-gray-100 px-2 py-0.5 flex-shrink-0 text-gray-800 border-[0.5px] border-gray-200 rounded-[3px] text-sm overflow-hidden"><svg id="branch" style="width:16px;height:16px" class="ruyi-icon ruyi-icon-branch" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M12 7.5L12.0921 7.99144C12.3286 7.94709 12.5 7.74061 12.5 7.5H12ZM4.5 10.5V9H3.5V10.5H4.5ZM4.5 9V5.5H3.5V9H4.5ZM4.09214 9.49144L12.0921 7.99144L11.9079 7.00856L3.90786 8.50856L4.09214 9.49144ZM12.5 7.5V5.5H11.5V7.5H12.5ZM5 4C5 4.55228 4.55228 5 4 5V6C5.10457 6 6 5.10457 6 4H5ZM4 5C3.44772 5 3 4.55228 3 4H2C2 5.10457 2.89543 6 4 6V5ZM3 4C3 3.44772 3.44772 3 4 3V2C2.89543 2 2 2.89543 2 4H3ZM4 3C4.55228 3 5 3.44772 5 4H6C6 2.89543 5.10457 2 4 2V3ZM13 4C13 4.55228 12.5523 5 12 5V6C13.1046 6 14 5.10457 14 4H13ZM12 5C11.4477 5 11 4.55228 11 4H10C10 5.10457 10.8954 6 12 6V5ZM11 4C11 3.44772 11.4477 3 12 3V2C10.8954 2 10 2.89543 10 4H11ZM12 3C12.5523 3 13 3.44772 13 4H14C14 2.89543 13.1046 2 12 2V3ZM5 12C5 12.5523 4.55228 13 4 13V14C5.10457 14 6 13.1046 6 12H5ZM4 13C3.44772 13 3 12.5523 3 12H2C2 13.1046 2.89543 14 4 14V13ZM3 12C3 11.4477 3.44772 11 4 11V10C2.89543 10 2 10.8954 2 12H3ZM4 11C4.55228 11 5 11.4477 5 12H6C6 10.8954 5.10457 10 4 10V11Z" fill="currentColor"></path></g></svg><span class="ml-1">master</span></div></div></div><div class="flex justify-between mt-4"><div class="t-select__wrap cnb-refs text-color-black-900 inline-block w-40 max-lg:sm:w-[120px] max-sm:w-[100px]"><div class="t-select t-select-input t-select-input--empty"><div class="t-input__wrap" value=" " spellcheck="false"><div class="t-input t-is-readonly t-align-left t-input--prefix t-input--suffix"><div class="t-input__prefix"><span class="truncate"><svg id="branch" class="ruyi-icon mr-1 ruyi-icon-branch" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M12 7.5L12.0921 7.99144C12.3286 7.94709 12.5 7.74061 12.5 7.5H12ZM4.5 10.5V9H3.5V10.5H4.5ZM4.5 9V5.5H3.5V9H4.5ZM4.09214 9.49144L12.0921 7.99144L11.9079 7.00856L3.90786 8.50856L4.09214 9.49144ZM12.5 7.5V5.5H11.5V7.5H12.5ZM5 4C5 4.55228 4.55228 5 4 5V6C5.10457 6 6 5.10457 6 4H5ZM4 5C3.44772 5 3 4.55228 3 4H2C2 5.10457 2.89543 6 4 6V5ZM3 4C3 3.44772 3.44772 3 4 3V2C2.89543 2 2 2.89543 2 4H3ZM4 3C4.55228 3 5 3.44772 5 4H6C6 2.89543 5.10457 2 4 2V3ZM13 4C13 4.55228 12.5523 5 12 5V6C13.1046 6 14 5.10457 14 4H13ZM12 5C11.4477 5 11 4.55228 11 4H10C10 5.10457 10.8954 6 12 6V5ZM11 4C11 3.44772 11.4477 3 12 3V2C10.8954 2 10 2.89543 10 4H11ZM12 3C12.5523 3 13 3.44772 13 4H14C14 2.89543 13.1046 2 12 2V3ZM5 12C5 12.5523 4.55228 13 4 13V14C5.10457 14 6 13.1046 6 12H5ZM4 13C3.44772 13 3 12.5523 3 12H2C2 13.1046 2.89543 14 4 14V13ZM3 12C3 11.4477 3.44772 11 4 11V10C2.89543 10 2 10.8954 2 12H3ZM4 11C4.55228 11 5 11.4477 5 12H6C6 10.8954 5.10457 10 4 10V11Z" fill="currentColor"></path></g></svg><span data-title="master">master</span></span></div><input placeholder="" type="text" class="t-input__inner" readonly="" value=" "/><span class="t-input__suffix t-input__suffix-icon"><svg class="t-fake-arrow t-select__right-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.75 5.7998L7.99274 10.0425L12.2361 5.79921" stroke="black" stroke-opacity="0.9" stroke-width="1.3"></path></svg></span></div></div></div></div><div class="flex items-center overflow-x-hidden ml-3"><button type="button" class="ml-3 t-button t-button--theme-default t-button--variant-outline"><span class="t-button__text"><svg id="ellipsis" class="ruyi-icon ruyi-icon-ellipsis" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M3 9C2.44775 9 2 8.55228 2 8C2 7.44772 2.44775 7 3 7C3.55225 7 4 7.44772 4 8C4 8.55228 3.55225 9 3 9Z" fill="currentColor"></path><path d="M7 8C7 8.55228 7.44775 9 8 9C8.55225 9 9 8.55228 9 8C9 7.44772 8.55225 7 8 7C7.44775 7 7 7.44772 7 8Z" fill="currentColor"></path><path d="M12 8C12 8.55228 12.4478 9 13 9C13.5522 9 14 8.55228 14 8C14 7.44772 13.5522 7 13 7C12.4478 7 12 7.44772 12 8Z" fill="currentColor"></path></g></svg></span></button></div></div><div class="flex flex-col flex-1"><div class="mt-4"><div class="px-4 py-3 min-w-0 bg-gray-50 rounded border border-gray-300 max-xl:p-3  "><div class="flex justify-between "><div class="mr-10 italic font-bold truncate cursor-pointer text-black-900"><span props="[object Object]" class="overflow-hidden block pr-1 truncate" style="width:100%;direction:ltr"><span><span class="break-all"><span>add changelog</span></span></span></span></div><div class="flex items-center flex-shrink-0"><a class="italic font-bold cursor-pointer text-black-900 hover:underline" href="/tencent/cloud/cos/cos-mcp/-/commit/fea4008f0fa78e617f7332387dc73032ee554d0b" target="_self">fea4008f</a></div></div><div class="flex justify-between mt-2 text-base text-black-750"><div class="flex items-center flex-1 min-w-0"><div class="flex items-center justify-center cursor-pointer min-w-0"><div class="cnb-avatar overflow-hidden flex-shrink-0 mr-1 rounded-full" style="width:16px;height:16px"><img alt="" loading="lazy" width="16" height="16" decoding="async" data-nimg="1" class="" style="color:transparent;height:16px;width:16px" src="/users/cnb.acooLZzSAHA/avatar/s"/></div><a class="hover:underline flex min-w-0 overflow-x-hidden text-ellipsis" href="/u/cnb.acooLZzSAHA" target="_blank">休戚与共🙊</a></div></div><a class="flex items-center hover:underline" href="/tencent/cloud/cos/cos-mcp/-/commits/master/README.md" target="_self"><svg id="history" class="ruyi-icon mr-1 text-m max-md:mr-0 ruyi-icon-history" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M7.90582 2.79363C10.8499 2.79364 13.2095 5.14029 13.2095 8.00195C13.2095 10.8636 10.8499 13.2103 7.90581 13.2103C5.44426 13.2103 3.39128 11.5698 2.78447 9.35939L1.75098 9.52803C2.44243 12.2422 4.93584 14.252 7.90581 14.252C11.4103 14.252 14.2512 11.4537 14.2512 8.00196C14.2512 4.55018 11.4103 1.75195 7.90582 1.75195C5.80622 1.75195 3.9448 2.75638 2.78981 4.304L2.78981 2.6451H1.75098V5.7916C1.75098 6.06774 1.97483 6.2916 2.25098 6.2916L5.37759 6.2916V5.24707H3.40454C4.33896 3.77709 5.99992 2.79363 7.90582 2.79363Z" fill="currentColor"></path><path d="M6.99951 5.50024V8.38917L9.64596 11.0356L10.3531 10.3285L7.99951 7.97496V5.50024H6.99951Z" fill="currentColor"></path></g></svg><span class="max-md:hidden"></span><span class="font-bold whitespace-pre"> 12 </span><span>commits</span></a></div></div></div><div class="flex-1 mt-4"><div class="rounded-[3px] border-gray-300 h-full flex flex-col"><div class="sticky top-0 z-10 flex justify-between items-center py-2 max-xl:p-2 px-6 bg-gray-50 border border-gray-300 border-b-0" style="height:52px"><div class="p-0.5"><div class="t-radio-group t-size-m cnb-radio t-radio-group--filled"><label tabindex="0" class="t-radio-button max-md:px-3"><input type="radio" class="t-radio-button__former" tabindex="-1" data-value="&#x27;preview&#x27;" value="preview"/><span class="t-radio-button__input"></span><span class="t-radio-button__label"><span class="">Preview</span></span></label><label tabindex="0" class="t-radio-button max-md:px-3 t-is-checked" checked=""><input type="radio" class="t-radio-button__former" tabindex="-1" data-value="&#x27;demo&#x27;" checked="" value="demo"/><span class="t-radio-button__input"></span><span class="t-radio-button__label"><span class="font-semibold">Code view</span></span></label><div class="t-radio-group__bg-block"></div></div></div><div class="flex items-center"><a target="__blank" data-popup="t-popup--0.4335519063" href="/tencent/cloud/cos/cos-mcp/-/git/raw/master/README.md" type="button" class="rounded-r-none hover:z-10 text-black-750 hover:text-brand-500 px-2 t-button t-button--theme-default t-button--variant-outline"><span class="t-button__text"><span class="">Raw</span></span></a><div class="inline-flex items-center justify-center cursor-pointer text-black-750 text-black-750 "><div class="flex w-full h-full items-center justify-center"><button type="button" class="rounded-l-none -ml-[1px] hover:z-10 px-2 text-black-750 hover:text-brand-500 t-button t-button--theme-default t-button--variant-outline"><span class="t-button__text"><svg id="file-copy" style="width:16px;height:16px" class="ruyi-icon ruyi-icon-file-copy" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M3.99995 1.92191C3.99995 1.33727 4.52001 1 4.9953 1H9.36584C9.63437 1 9.89162 1.108 10.0797 1.29969L13.7139 5.00415C13.8972 5.19093 13.9999 5.44209 14.0001 5.70375L14.0046 12.0777C14.0046 12.6624 13.4846 13 13.0093 13H4.99995C4.52472 13 4.00474 12.6628 4.00461 12.0783L3.99995 1.92191ZM4.99999 2L5.00457 12H13.0046L13.0003 6.01275H9.00004V2H4.99999ZM10 2.64645V5.01275H12.3215L10 2.64645Z" fill="currentColor"></path><path d="M2 5.00001V14.0128C2 14.565 2.44772 15.0128 3 15.0128H11V14.0128L3 14.0128V5.00001H2Z" fill="currentColor"></path></g></svg></span></button></div></div><a target="__blank" data-popup="t-popup--0.7444170331" href="/tencent/cloud/cos/cos-mcp/-/git/raw/master/README.md?download=true" type="button" class="ml-2 px-2 text-black-750 hover:text-brand-500 t-button t-button--theme-default t-button--variant-outline"><span class="t-button__text"><svg id="download" style="width:16px;height:16px" class="ruyi-icon ruyi-icon-download" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M12.2636 5.81387L8.5 9.57746L8.49998 0.5L7.49998 0.500002L7.5 9.57746L3.73641 5.81387L3.02931 6.52098L7.64645 11.1381C7.84171 11.3334 8.15829 11.3334 8.35355 11.1381L12.9707 6.52098L12.2636 5.81387Z" fill="currentColor"></path><path d="M2 11V13C2 13.5523 2.44772 14 3 14H13C13.5523 14 14 13.5523 14 13V11H13V13H3V11H2Z" fill="currentColor"></path></g></svg></span></a></div></div><div class="flex-1"><div class="text-base font-normal border border-gray-300"><div class="cnb-full-height-loading"><div class="t-loading__parent"><div style="height:200px"><div class="cnb-full-height-loading"><div class="t-loading t-loading--center t-size-m z-[2] cancel-mobile-transform w-full h-full"><svg class="t-loading__gradient t-icon-loading" viewBox="0 0 12 12" version="1.1" width="1em" height="1em" xmlns="http://www.w3.org/2000/svg"><foreignObject x="0" y="0" width="12" height="12"><div class="t-loading__gradient-conic"></div></foreignObject></svg><div class="t-loading__text">Loading...</div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div> </div><div id="cnb-global-footer" class="JS-cnb-footer overflow-y-hidden relative z-[5] 2xl:max-2.5xl:px-16 laptop:max-2xl:px-8 max-laptop:px-4 bg-white text-black-750"><div class="relative flex items-center mx-auto w-full h-full max-w-2xl border-t border-t-gray-300 py-6 max-xl:py-4 "><div class="text-size-sm w-full overflow-hidden max-xl:flex flex-col items-center justify-start" style="line-height:20px"><div class="flex items-start justify-between"><div class="flex items-center gap-2 "><a href="/" target="_self"><svg id="logo-monochrome" class="ruyi-icon hover:cursor-pointer align-top w-4 h-4 max-xl:w-4 max-xl:h-4 transition-transform rotate-0 hover:rotate-180 duration-300 ease-[cubic-bezier(.56,-0.53,.79,1.39)] ruyi-icon-logo-monochrome" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path d="M11.5286 1.87149C11.5769 1.73005 11.5356 1.5733 11.4233 1.47452C11.0472 1.14247 10.0965 0.443125 8.66911 0.339708C7.07054 0.223769 6.08089 0.652279 5.58096 0.969951C5.36531 1.10676 5.35326 1.41748 5.55499 1.57422L9.62723 4.73936C9.98617 5.01807 10.5125 4.8604 10.6591 4.43003L11.5286 1.87149Z" fill="currentColor"></path><path d="M1.49017 11.2664C1.32368 11.3781 1.24855 11.584 1.30235 11.7774C1.45724 12.3339 1.91868 13.4919 3.22833 14.5456C4.53797 15.5992 6.08738 15.7128 6.74962 15.6966C6.94764 15.692 7.12016 15.5617 7.17998 15.3724L9.79046 7.11064C9.97875 6.51425 9.31048 6.01386 8.79154 6.3626L1.49017 11.2664Z" fill="currentColor"></path><path d="M3.39813 2.54827C3.27013 2.49773 3.12683 2.50607 3.00579 2.57193C2.52256 2.83488 1.28526 3.64506 0.647135 5.30947C0.154627 6.59222 0.328071 8.01085 0.463488 8.70463C0.508009 8.9314 0.747306 9.06218 0.962489 8.97824L8.79485 5.92024C9.35414 5.70181 9.35646 4.91111 8.7981 4.6899L3.39813 2.54827Z" fill="currentColor"></path><path d="M15.0167 8.46843C15.243 8.62194 15.5528 8.48652 15.5922 8.21569C15.6961 7.49872 15.7861 6.25076 15.371 5.30933C14.8177 4.05487 13.8786 3.28133 13.433 2.9669C13.292 2.86766 13.1019 2.87786 12.9725 2.99241L10.9959 4.74541C10.6732 5.03154 10.7066 5.54492 11.0636 5.78746L15.0167 8.46936V8.46843Z" fill="currentColor"></path><path d="M9.49413 15.1604C9.47372 15.3937 9.67128 15.5866 9.90409 15.5616C10.6531 15.4813 12.1918 15.1841 13.3447 14.0827C14.467 13.0109 14.832 11.7384 14.9382 11.2319C14.9669 11.0951 14.9326 10.9528 14.8445 10.8442L11.3886 6.57909C11.0143 6.11719 10.2681 6.34535 10.2162 6.93757L9.49366 15.1604H9.49413Z" fill="currentColor"></path></g></svg></a><span>© 2025 Tencent, Inc.</span></div><div class="flex  gap-x-4 items-center max-xl:hidden text-blue-600"><div class="flex items-center"><a class="hover:cursor-pointer hover:underline " target="__blank" href="https://docs.cnb.cool/en/saas/about.html">About Us</a></div><div class="flex items-center"><a class="hover:cursor-pointer hover:underline " target="__blank" href="https://docs.cnb.cool/en/saas/privacy.html">Privacy Policy</a></div><div class="flex items-center"><a class="hover:cursor-pointer hover:underline " target="__blank" href="https://docs.cnb.cool/en/saas/terms.html">Terms of Service</a></div><div class="flex items-center"><a class="hover:cursor-pointer hover:underline " target="__blank" href="https://api.cnb.cool">API</a></div><div class="flex items-center"><a class="hover:cursor-pointer hover:underline " target="__blank" href="https://docs.cnb.cool/en/">Docs</a></div><div class="flex items-center"><a class="hover:cursor-pointer hover:underline " target="__blank" href="https://cnb.cool/examples/showcase">Showcase</a></div><div class="flex items-center"><a class="hover:cursor-pointer hover:underline " target="__blank" href="https://cnb.cool/cnb/feedback">Feedback</a></div></div></div><div class="flex items-start justify-between" style="margin-top:4px"><div class="flex flex-wrap justify-center max-xl:hidden gap-x-4"><span>35/F,Tencent Building,Kejizhongyi Avenue,Nanshan District,Shenzhen</span></div><div class="flex items-center max-xl:flex-col"><div class="xl:ml-4"><div class="flex items-center"><a class="hover:cursor-pointer hover:underline " target="__blank" href="https://cnb.cool/goto?url=https%3A%2F%2Fbeian.miit.gov.cn%2F%23%2FIntegrated%2Findex">京ICP备11018762号-111</a></div></div><div class="xl:ml-4"><div class="flex items-center"><img alt="粤公网安备44030002006058号" loading="lazy" width="16" height="17" decoding="async" data-nimg="1" class="mr-1" style="color:transparent" src="https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465/images/security-icon.png"/><a class="hover:cursor-pointer hover:underline " target="__blank" href="https://cnb.cool/goto?url=https%3A%2F%2Fbeian.mps.gov.cn%2F%23%2Fquery%2FwebSearch%3Fcode%3D44030002006058">粤公网安备44030002006058号</a></div></div></div></div></div></div></div></div></div><script id="__NEXT_DATA__" type="application/json" nonce="NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm" crossorigin="anonymous">{"props":{"pageProps":{"csrftoken":"0b5a736393a525ae2f900df45846bffc1a17c7de","authInfo":{"userNickName":"","userName":"","userVerified":false,"userFreeze":false,"userEmail":"","slugName":"tencent/cloud/cos/cos-mcp","slugType":1,"slugStatus":0,"slugFreeze":false,"slugVisibility":"Public","slugRole":"Unknown","language":"en-US","ban":false,"meta":{"FE_has_fork_form":false,"FE_root_organization_setting":{"group_protection":1,"email_verification":"tencent.com"}}},"lang":"en-US","nonce":"NDY5OTI4YjctZDIzNS00M2RkLTk0ZmItMWIwODY0OWE2MzRm","wxJSSDKConfig":null,"userAgent":"axios/1.7.7","_nextI18Next":{"initialI18nStore":{"en-US":{"fork-dialog":{},"validate-repo-name":{},"common":{},"convert-time":{},"report":{},"slug-tab-common":{},"description":{},"slug-repo-common":{},"slug-repo-not-inited":{},"refs-select":{},"form":{},"file-table":{},"slug-repo-blob":{},"last-commit":{}},"en":{"fork-dialog":{"BELONG_TO_GROUP":"Belong to","REPOSITORY_NAME_TIP":"Repository name, e.g.: cloud-native-build","REPO_DES":"Repository description","ADD_REPO_DES":"Add repository description","ENTER_AND_PREVIEW_URL":"After entering the repository name, you can preview your repository URL below","URL_PREVIEW":"URL preview","REPOSITORY_NAME":"Repository name","MAIN_BRANCH":"the default branch","ENTIRE_REPO":"the entire repository","NEED_BELONG_TO_GROUP":"Belong to must be selected"},"validate-repo-name":{"REPO_NAME_NOT_EMPTY":"Repository name can not be empty","REPO_NAME_ASCII_ONLY":"Repository name uses ASCII only","REPO_NAME_NOT_START_OR_END_WITH_SPECIAL_CHAR":"Repository name cannot start or end with special characters","REPO_NAME_NOT_HAS_BLANK":"Repository name cannot have blank","REPO_NAME_LETTER_LIMIT_ONLY":"Repository name can only contain ASCII letters, digits, and the characters ., -, and _","REPO_NAME_NOT_END_WITH_GIT_OR_SVN":"Repository name cannot ends with .git or .svn","REPO_NAME_LENGTH_NOT_EXCEED":"Repository name length cannot exceeds {{max}}","REPO_NAME_LENGTH_MIN":"Repository name length at least {{min}}"},"common":{"WELCOME":"weclome","GET_STARTED":"Get started","NETWORK_RETRY":"Network error, please retry","LOAD_MORE":"load more","VIEW_ALL":"view all","REPO_CAPS":"Repository","GROUP":"group","GROUP_CAPS":"Group","ISSUE":"ISSUE","PULL_REQUEST":"pull request","PULL_REQUEST_CAPS":"Pull request","ALL":"All","MORE":"More","CONTINUE":"Continue","CREATE":"Create","CANCEL":"Cancel","COMPLETE":"Complete","QUOTE":"Quote","EDIT":"Edit","AI_TRANSLATE":"Chinese to Chinese","APPLY":"Apply","PREVIEW":"Preview","SAVE":"Save","DELETE":"Delete","COPY_LINK":"Copy link","LOCK":"Lock","UNLOCK":"Unlock","FILTER":"Filter","CLEAR_FILTER":"Clear","DISCUSSION":"Discussion","EXPIRATION_DATE":"Expiration date","STATUS":"Status","OPERATION":"Operation","DETAIL":"Detail","TRENDS":"Trends","COMMENT":"Comment","COMMENTED":"commented","BACKGROUND_COLOR":"Background","EXPAND_ALL":"Expand all","COLLAPSE_ALL":"Collapse all","MODIFY_STATUS":"Status","SELECTED_COUNT":"\u003c0\u003e{{count}}\u003c/0\u003e selected","LIMITED_ASSIGNEE_SELECTION":"{{count}} selected, {{remains}} left","LIMITED_LABEL_SELECTION":"{{count}} selected, {{remains}} left","LIST_ITEMS_COUNT":"{{count}} matched","CREATION_DATE":"Created at {{date}}","OVERVIEW":"Overview","COMMITS_HISTORY":"Commit history","FILES_CHANGED":"Files changed","META_DATA":"Meta data","MERGED":"Merged","OWNER":"Owner","MASTER":"Administrator","DEVELOPER":"Developer","REPORTER":"Reporter","GUEST":"Guest","UNKNOWN":"Unknown","REPO_ROLE":"Repository {{role}}","NO_DESCRIPTION_PROVIDED":"No description provided","PASTE_DROP_CLICK_TO_UPLOAD_FILE":"Paste, drop, or click to add files","HEADER_USER_POPUP_TODO":"Your todo","HEADER_USER_POPUP_RELATED_ME":"Your related","HEADER_USER_POPUP_USER_OVERVIEW":"Your profile","HEADER_USER_POPUP_USER_REPO":"Your repositories","HEADER_USER_POPUP_USER_OCI":"Your workspaces","HEADER_USER_POPUP_USER_ARTIFACTORY":"Your registries","HEADER_USER_POPUP_USER_ORGANIZATION":"Your groups","HEADER_USER_POPUP_USER_MISSIONS":"Your missions","HEADER_USER_POPUP_USER_FOLLOW":"Your stars","HEADER_USER_POPUP_USER_SETTING":"Settings","HEADER_HELP_DOCS":"Docs","HEADER_SHOWCASE":"Showcase","HEADER_FEEDBACK":"Feedback","HEADER_LOGOUT":"Logout","PAGE_500_DESC":"Internal Server Error.","PAGE_502_DESC":"Bad Gateway.","PAGE_504_DESC":"Gateway Timeout Error.","PAGE_404_DESC":"The current page does not exist or access is denied.","LOGIN":"Login","HOME_PAGE_HEADER_USAGE":"Usage","HOME_PAGE_HEADER_DOCS":"Docs","HOME_PAGE_HEADER_PRICING":"Pricing","HOME_PAGE_HEADER_ENTERPRISE":"Enterprise Edition","HOME_PAGE_HEADER_SEARCH":"to search","HOME_PAGE_HEADER_TYPE":"Type","REPORT_ENTRY":"Report","REPORT_CODE":"Report Abuse","COPY":"Copy","COPIED":"Copied","COPY_SUCCESS":"Copied successfully","COPY_FAILED":"Copied failed","GET_TOKEN":"Get token","GET_PUBLIC_KEY":"Get public key","NOTOKEN_TIP_ACCESS_TOKEN":"access token","NOTOKEN_TIP_SSH_PUBLIC_KEY":"SSH public key","DO_NOT_HAVE_TOKEN_FOR_GIT_TIP":"You do not have a {{tokenName}} for Git authentication","COMMA":",","COMMA_":",","Colon":":","SPACE_IN_ENGLISH":" ","DATA_TYPE":"Data type","NOT_LOGIN_TIPS":"The current user is not logged in, please login","INSUFFICIENT_PERMISSION":"The current user does not have sufficient permissions","REPO_PUBLIC":"Public","REPO_PRIVATE":"Private","REPO_SECRET":"Secret","SYSTEM_ERROR":"System error","SYSTEM_ERROR_AND_PLEASE_RELOAD_OR_RETRY_LATTER":"System error, please reload page or check the system and try again","REPO_HOMEPAGE":"Homepage","LOADING":"Loading...","TITLE":"Title","ASSIGNEE":"Assignee","PRIORITY":"Prority","LABEL":"Label","CREATOR":"Creator","CREATED_DATE":"Create date","UPDATED_DATE":"Update date","CREATED":"Created","UPDATED":"Updated","STATE":"State","REPO":"Repository","REVIEWER":"Reviewer","CREATE_REPO":"New repository","CREATE_REGISTRY":"New {{type}} registry","CREATE_GROUP":"New group","CREATE_SUB_GROUP":"New sub group","CREATE_MISSION":"New mission","DASHBOARD":"Dashboard","EVERYTHING_AS_CODE":"Coding Anywhere","SPONSOR":"Sponsor","CONTRIBUTORS":"Contributors","FOOTER_PRAVICY":"Privacy Policy","FOOTER_TERM_OF_SERVICE":"Terms of Service","FOOTER_ABOUT_US":"About Us","SHOWCASE":"Showcase","FOLLOW":"Follow","FOLLOWED":"Followed","FOLLOWERS":"Followers","FOLLOWINGS":"Followings","UNFOLLOW_SUCCESS":"Unfollowed successfully","FOLLOW_SUCCESS":"Followed successfully","DEFAULT_USER_DESC":" Everything as Code.","OPERATE_SUCCESSFULLY":"Operate successfully","SCAN_QRCODE_FOLLOW_WXOA":"Scan the WeChat code to follow","WECHAT_PUBLIC_ACCOUT_QR_CODE":"WeChat Official Account QR Code","WECHAT_OFFICIAL_ACCOUT":"WeChat Official Account","WECHAT_OFFICIAL_ACCOUT_ID_PLACEHOLDER":"Please enter the ID of the WeChat Official Account","WECHAT_OFFICIAL_ACCOUT_ID_TIPS1":"Log in to the WeChat official accounts platform","WECHAT_OFFICIAL_ACCOUT_ID_TIPS2":", Settings and Development-Account Info-Registration information-Weixin ID or Original ID(e.g. cnb-_-cool)","WECHAT_OFFICIAL_ACCOUT_ID_ERROR":"Please enter a valid WeChat official account ID","MISSIONS":"missions","LEARN_MORE":"Learn more","APPRECIATION":"Appreciation","APPRECIATED_MEMBERS":"Appreciated members","DONATION_LINK":"Donation Link","APPRECIATE_BY_WECHAT":"Appreciate by WeChat","LOGIN_FAILURE_TIPS_DIALOG_TITLE":"Login Expired","LOGIN_FAILURE_TIPS_DIALOG_TEXT":"The current user session has expired. Please log in again.","RELOGIN_BTN":"Login","USER_CHANGE_DIALOG_TITLE":"User Change Notification","USER_CHANGE_DIALOG_TEXT":"The currently logged-in user has changed. Please refresh the page to continue.","USER_CHANGE_DIALOG_RELOAD_BTN":"Reload","VISITOR":"Visitor","WELCOME_LOGIN":"Welcome Login to Cloud Native Build","QR_CODE_EXPIRED":"QR code has expired","CLICK_TO_REFRESH":"Click to refresh","TIP":"Tip","CLOSE_PAGE":"Close page","LOGIN_SUCCESS":"Login successful","SCAN_SUCCESS":"Scan successful","CONFIRM_AUTHORIZATION":"Please confirm if authorized","DISAGREE_AUTHORIZATION_BUTTON":"Disagree","AUTHORIZED_LOGIN_NOTICE":"You have authorized to log in to cnb.cool, follow the public account to receive notifications","CONFIRM_AUTHORIZATION_BUTTON":"Agree and Sign In","SELECT_IDENTITY_BUTTON":"Select identity to login","SELECT_AVATAR_BUTTON":"Select avatar and nickname","CANCEL_AUTHORIZATION_BUTTON":"Cancel authorization","FOLLOW_PUBLIC_ACCOUNT":"Follow the public account","AUTHORIZATION_FAILED_RESCAN":"Authorization failed, please rescan","QR_CODE_EXPIRED_RESCAN":"QR code has expired, please rescan","PARAMETER_ERROR":"Parameter error","PLEASE_USE_WECHAT":"Please use WeChat","PLEASE_SCAN_TO_LOGIN":"Please scan to log in","COMPLETE_OPERATION_IN_WECHAT_APP":"Please complete the operation in the WeChat App","PAGE_WILL_REDIRECT":"The page will redirect soon","WX_SCAN_QRCODE_LOGIN_TOGGLE_BTN":"Scan Login","WX_FAST_LOGIN_TOGGLE_BTN":"Fast Login","WX_LAST_LOGIN_TOGGLE_MESSAGE":"Last Login","USE_WECHAT_APP_TO_SCAN_LOGIN":"Please use the WeChat App scan code login","USE_WECHAT_FAST_LOGIN_TIPS_1":"If device not support WeChat fast login,","USE_WECHAT_FAST_LOGIN_TIPS_2":"Please use the Wechat App scan code login.","OR":"Or","REFRESH":"Refresh","RESCAN_TO_LOGIN":"Rescan to log in","ACCOUNT_BANNED":"Account has been banned","ACCOUNT_BANNED_FOR_VIOLATION":"Your account has been banned due to violation of the \u003c0\u003e\"Service Agreement\"\u003c/0\u003e and cannot be logged in","SCAN_FOCUS_1":"Subscribe to our account for Issue and PR alerts.","SCAN_FOCUS_2":"Unsubscribed users can still use the service.","LOGINING_IN":"Logging in to {{origin}}","READ_POLICY":"Please read \u003c0\u003ethe Terms of Service\u003c/0\u003e and \u003c1\u003ePrivacy Policy\u003c/1\u003e","UNIT_GE":"","UNFOLLOW":"Unfollow","BANNED_USER":"Banned User","BANNED_GROUP":"Banned Group","BANNED_REPOSITORY":"Banned Repository","BANNED_TIP":"\u003c0\u003eThis {{type}} has been banned for violating \u003c/0\u003e\u003c1\u003ecommunity guidelines\u003c/1\u003e.","BANNED_REPO":"The repository has been banned","BANNED_ORGANIZE":"The group has been banned","BANNED_USERS":"The user has been banned","USER":"user","BANNED":"Banned","SIGN_IN_TO_COMMENT":"Sign in to comment","SEARCH_DIALOG_INPUT_PLACEHOLDER":"Search/Query...","SEARCH_DIALOG_MY_REPO":"Your repositories/Your stars","SEARCH_DIALOG_MY_GROUPS":"Your groups","SEARCH_DIALOG_STAR_REPO":"Your stars","SEARCH_DIALOG_PUBLIC_REPO":"Public repositories","SEARCH_DIALOG_MY_MISSIONS":"Your missions","SEARCH_DIALOG_MY_REGISTRY":"Your registries","SEARCH_DIALOG_SELECT":"to navigate","SEARCH_DIALOG_ENTER":"to select","TYPE":"Type","TO_SEARCH":"to search","SEARCH_DIALOG_NO_RESULT":"Your search did not match any results","OFFICAL_EXAMPLES":"Official examples","REQUIRED":"Required","OWNER_BANNED_REASON":"The current account has been prohibited from logging in due to \u003c0\u003e{{reason}}\u003c/0\u003e.","OWNER_BANNED_NORMAL":"The current account has been banned.","USER_BAN_DESC":"It is recommended to read the \u003c0\u003eCommunity Guidelines\u003c/0\u003e to avoid violating the rules again.","ORDER_APPEAL":"If you have any objections, you can appeal through a support ticket.","USED":"Used","CAPACITY_LIMIT":"Limit","COMPLETED_REAL_NAME_AUTH":"Completed real name authentication","AFTER_REAL_NAME_CAN_OPERATE":"Can not be operated until real name authentication has been completed","CONFIRM":"Confirm","COMPLETED_AUTH":"Completed","REAL_NAME_PHONE":"Real name authentication phone number: {{phone}}","CONFIRM_AND_OPERATION":"Confirm and continue the original operation","WECHAT_SCAN_CODE_TO_AUTH":"WeChat scan code, complete real name authentication","NOT_DETECTED_REAL_NAME_INFO_SCAN_AGAIN":"No real name authentication information detected, please re-scan the code","LONG_PRESS_TO_AUTH":"Long press the QR code, go to real name authentication","CONFIRM_TO_LEAVE_PAGE":"Confirm to leave the current page","CONTENT_MODIFIED_WILL_NOT_BE_SAVE":"The current modified content has not been submitted, and the modified content will not be saved","LEAVE":"Leave","WX_CONFIRM_DIALOG_TIPS":"To ensure the security of your account, please verify using WeChat QR code scan.","WX_CONFIRM_DIALOG_VISIT_TIPS":"Please complete the subsequent operations in WeChat or refresh to scan the QR code again.","WX_CONFIRM_DIALOG_IN_WX_SUCCESS_TIPS":"You are currently in the WeChat environment, no need to scan the QR code again.","WX_CONFIRM_DIALOG_SUCCESS_TIPS":"You have completed the confirmation.","WX_CONFIRM_DIALOG_EXPIRED_TIPS":"Please close the pop-up and try again.","WX_CONFIRM_PAGE_TITLE":"Confirming a sensitive or high-risk operation soon.","WX_CONFIRMED_PAGE_TITLE":"Confirmation of sensitive or high-risk operation completed.","WX_CONFIRM_PAGE_BUTTON_TEXT":"Confrim","WX_CONFIRM_MISS_TICKET":"Missing secondary confirmation ticket.","FILE_HAS_NOT_BEEN_CHANGED":"The current file has not been modified","UNABLE_RENDER_MERMAID":"Unable to render the Mermaid","BANNED_NO_COMMENT":"Muted, comments or reviews are not allowed","SITE_ERROR_MESSAGE_REQUIRE_PROTOCOL":"The protocol needs to be included, e.g. https://","SITE_PLACEHOLDER_EXAMPLE":", e.g. {{example}}","AI_AUTO_GENERATE":"AI automatic generation","SHOW_ORGS":"Only show groups within permission","SHOW_TASKS":"Only show task sets within permission","SHOW_TASKS_WITHOUT_COUNT":"Only show task sets within permission","SHOW_REPOS":"Only show repositories within permission","VERIFIED_GROUP_CONTROLS_DOMAIN":"Verified that {{group}} controls the following domain","VERIFIED":"Verified","AI_SEARCH":"AI Search","LINK_TO_AI":"? To AI","SELECT_BRANCH_TAG_OR_INPUT_SHA":"Please select a branch、a tag or enter a commit SHA","ARTIFACTORY":"registry","UPDATE":"Update","DISCARD":"Discard","COMPARE":"Compare","AI_CHAT_IS_REASONING":"Deep thinking in progress","AI_CHAT_REASONING_STOPPED":"Deep thinking completed","AI_CHAT_SPENDT_TIME":"Time spent: {{time}} seconds","TIP_FROM_AI":"The following content is generated by AI","COMPANY_ADDRESS":"35/F,Tencent Building,Kejizhongyi Avenue,Nanshan District,Shenzhen","TAPD_TITLE":"Title","TAPD_PRIORITY":"Priority","TAPD_STATUS":"Status","TAPD_ENDTIME":"Estimated End Time","TAPD_OWNER":"Owner","TAPD_CREATED_TIME":"Creation Time","TAGGET_BRANCH":"Branch","ACCEPT":"Accept","REQUIRED_MUST_AUTO_MERGE_TIP":"When both code review and automated checks pass, it can be automatically merged into the target branch. For details, see: \u003c0\u003eUsage Documentation\u003c/0\u003e.","BANNED_TIP_USERNAME":"\u003c0\u003e{{type}} {{userName}} has been banned for violating \u003c/0\u003e\u003c1\u003ecommunity guidelines\u003c/1\u003e.","BANNED_TIP_USERNAME_REASON":"\u003c0\u003e{{type}} {{userName}} due to {{reason}}\u003c/0\u003e has been banned."},"convert-time":{"MINUTES":"{{time}} minutes ago","HOURS":"{{time}} hours ago","DAYS":"{{time}} days ago","WEEKS":"{{time}} weeks ago","MONTHS":"{{time}} months ago","NOW":"now","INVALID_DATE":"Invalid data"},"report":{"SUCCESS_TITLE":"Report submitted successfully, we will process it as soon as possible","SUCCESS_DESC":"The audit result will be replied in the form of a notification. Thank you for maintaining the order of the CNB community!","RETURN_DASHBOARD":"Return to the dashboard","REPORTING_DESC":"I want to report ","SUBMIT_TEXT":"Submit","TAG_LIMIT_DESC":"Please select violation tags (\u003c0\u003eup to 3\u003c0/\u003e)","TAG_LIMIT_WARNING":"Select up to 3 violation tags","CONTENT_DESC":"Reason for reporting, screenshots are recommended","COPYRIGHT_DESC":"\u003c0\u003eBecause it involves infringement, please refer to the \u003c/0\u003e\u003c1\u003e\"Infringement Complaint Instructions\"\u003c/1\u003e to fill in the reason for the report","CODE":"Sponsor Code","USER":"User","REPO":"Repository","GROUP":"Group","ARTIFACTORY":"Registry","ARTIFACT":"Package","ARTIFACT_TAG":"Package Version","ISSUE_DETAIL":"Issue","ISSUE_COMMENT":"Issue Comment","PR_DETAIL":"PR","PR_COMMENT":"PR Comment","OWNER_BANNED_REASON":"The current account has been banned due to \u003c0\u003e{{reason}}\u003c/0\u003e.","OWNER_BANNED_NORMAL":"The current account has been banned.","HAS_BANNED_REASON":"The {{type}} has been banned due to \u003c0\u003e{{reason}}\u003c/0\u003e.","HAS_BANNED_NORMAL":"The {{type}} has been banned","BAN_DESC":"You can contact the person in charge, delete the {{type}}, or file a complaint.","DELETE_BAN_RESOURCE":"delete the {{type}}","COMMENTED":"commented","SLUG_BAN_DESC":"It is recommended to read the \"\u003c0\u003eCommunity Guidelines\u003c/0\u003e\" to maintain a healthy online environment.","USER_BAN_DESC":"It is recommended to read the \"\u003c0\u003eCommunity Guidelines\u003c/0\u003e\" to avoid violating the rules again.","OWNER_BAN_DESC_INFO":"If you have any objections, you can appeal through a support ticket."},"slug-tab-common":{"REPO_TAB_CODE":"Code","REPO_TAB_ISSUE":"Issues","REPO_TAB_PR":"Pull requests","REPO_TAB_OCI":"Events","REPO_TAB_PACKAGES":"Packages","REPO_TAB_INSIGHTS":"Insights","GROUP_TAB_ARTIFACTORY":"Registries","ARTIFACTORY_TAB_PACKAGE":"Packages","ARTIFACTORY_TAB_SETTING":"Registry Setting","REPO_TAB_SETTING":"Settings","GROUP_TAB_OVERVIEW":"Overview","GROUP_TAB_REPOSITORIES":"Repositories","GROUP_TAB_SUB_ORGANIZATIONS":"Sub-groups","GROUP_TAB_MISSIONS":"Missions","GROUP_TAB_MEMBERS":"Members","GROUP_TAB_SETTINGS":"Settings","GROUP_TAB_SECURITY":"Security","REPOSITORY_ARCHIVED_READ_ONLY":"The repository has been archived and is now in read-only mode.","REPO_OVER_LIMIT":"The current repo capacity has reached the limit and the submission operation cannot be performed. You can go to","USAGE_STATISTICS":"usage statistics","TO_VIEW_LIMIT":"to view or modify the repo capacity.","NO_ACCESS_TO_REPO_SETTINGS":"No access to repository settings","NO_ACCESS_TO_REPO_SECURITY":"No access to repository security","NO_ACCESS_TO_GROUP_SETTINGS":"No access to group settings","NO_ACCESS_TO_MISSION_SETTINGS":"No access to mission settings","NO_ACCESS_TO_ARTIFACTORY_SETTINGS":"No access to registry settings","REPO_TAB_AUDIT":"Audit","ROOT_GROUP_CHARGE_FREEZE_TIP":"The root group's budget is in arrears and has been frozen. Actions such as writing and uploading, will be restricted. Please contact the group master.","ROOT_GROUP_CHARGE_FREEZE_TIP_WITH_ACCESS":"The root group's budget is in arrears and has been frozen. Actions such as writing and uploading, will be restricted. Please go to \u003clinkCom\u003eTencent Cloud\u003cicon\u003e\u003c/icon\u003e\u003c/linkCom\u003e to recharge your balance.","ROOT_GROUP_BUDGET_RESOURCE_LIMIT_TIP_GIT":"The root group's code repository has reached its capacity limit, and commit operations are not allowed.","ROOT_GROUP_BUDGET_RESOURCE_LIMIT_TIP_LFS":"The root group's object storage has reached its capacity limit, and pushing new artifacts is not allowed.","ROOT_GROUP_BUDGET_RESOURCE_LIMIT_TIP_CI":"The root group's event usage has reached its limit, and event tasks cannot be started.","ROOT_GROUP_BUDGET_RESOURCE_LIMIT_TIP_WORKSPACE":"The root group's workspace usage has reached its limit, and workspace cannot be started.","ROOT_GROUP_BUDGET_RESOURCE_LIMIT_ACTION":"Please contact the group administrator.","ROOT_GROUP_BUDGET_RESOURCE_LIMIT_ACTION_WITH_ACCESS":"Please go to \u003clinkCom\u003egroup Billing Management\u003c/linkCom\u003e to adjust the resource budget.","ROOT_GROUP_BUDGET_RESOURCE_WARNING_GIT":"The root group's code repository usage has exceeded 80%.","ROOT_GROUP_BUDGET_RESOURCE_WARNING_LFS":"The root group's object storage usage has exceeded 80%.","ROOT_GROUP_BUDGET_RESOURCE_WARNING_CI":"The root group's event usage has exceeded 80%.","ROOT_GROUP_BUDGET_RESOURCE_WARNING_WORKSPACE":"The root group's workspace usage has exceeded 80%.","ROOT_GROUP_BUDGET_RESOURCE_WARNING_ACTION":"Please adjust the budget to avoid affecting usage."},"description":{"DEFAULT":"Cloud Native Build, Everything as code.","GREATE_COMMON":"Our developer platform is dedicated to helping developers accelerate software development.","GROUP_DESCRIPTION":"has {{groupCount}} valid repositories.","PR_LIST":"{{repoSlug}} currently has {{prCount}} open pull requests.","PR_DETAIL_COMMIT_TITLE":"commits at #{{pullId}}","PR_DETAIL_FILECHANGES_TITLE":"diff at #{{pullId}}","DEFAULT_DESCIPTION":"Our developer platform is dedicated to helping developers accelerate software development.","USER_PAGE_TAB_OVERVIEW_TITLE":"{{userName}}","USER_PAGE_TAB_REPOS_TITLE":"{{userName}} · repos","USER_PAGE_TAB_STARS_TITLE":"{{userName}} · stars","USER_PAGE_TAB_GROUPS_TITLE":"{{userName}} · groups","USER_PAGE_TAB_WORKDSPACES_TITLE":"{{userName}} · workspaces","USER_PAGE_TAB_MISSIONS_TITLE":"{{userName}} · missions","USER_PAGE_TAB_DEFUALT_DESC":"{{userName}} has {{followerNumber}} followers. Follow their code on CNB!"},"slug-repo-common":{"REPO_DATA_ERROR":"Some errors in the data of page, please refresh the page and try again","SUBMIT":"Submit","CND_BTN_TEXT":"Workspace","NOT_LOGIN_TIP":"The current user is not logged in, please login","INSUFFICIENT_PERMISSIONS_TIP":"The current user does not have sufficient permissions","LOAD_BRNACH_ERROR_TIP":"Failed to obtain current branch information","LOAD_REPO_ERROR_TIP":"Failed to obtain current repository information","CRATING_ENV_WAIT_TIP":"The development environment is being prepared","CREATE_ENV_ERROR_TIP":"Failed to create development environment","START_CND_SLOGAN":"Let's goooooo =\u003e","NOT_ALLOW_LAUNCH_TIP":"The repository size {{size}} exceeds the {{limit}} limit, so cloud native development has been disabled.","PORTECT_BRNACH_CND_TIP":"Protect branch to statr workspace reminders","START_CND":"Start workspace","SWITCH_BRANCH_TIP":"You are starting a workspace in a protected branch. You don't have permission to push code. Do you want to create a branch to start? ","SELECTED_BRANCH":"Select branch","NEW_BRNACH":"New branch","CREATE_BRNACH":"New branch","CREATE_SOURCE":"Source branch","BRANCH_NAME":"Branch name","BRANCH_NAME2":"Branch name","Create":"Create","CANCEL":"Cancel","CREATE_BRANCH_SUCCESS_TIP":"branch {{branchName}} has been created successfully","CREATE_BRANCH_ERROR_BY_REPO_DATE_TIP":"Creation failed, the current repository information is incorrect","SUPPORT":"Support ","SUPPORTED_CHARACTERS_RULE":"Branch name cannot contain \"*\", \"?\" and spaces ","SUPPORTED_STR_LENGTH":" must be a non-empty string with a maximum length of 128 characters.","ONLY_SUPPORT":" must only contain","SUPPORTED_STR_END_TIP":" cannot start or end with a hyphen (-), period (.), or slash (/).","SUPPORTED_STR_CONSECUTIVE_PERIODS_TIP":" cannot contain consecutive periods (.) or consecutive slashes (/).","NO_ACCESS":"No access","NO_ACCESS_TO_SECRET_REPO":"The secret repository is only accessible to \u003cbold\u003erepository members\u003c/bold\u003e, \u003cbold\u003egroup admins\u003c/bold\u003e, and \u003cbold\u003egroup owners\u003c/bold\u003e. \u003cbr\u003e\u003c/br\u003ePlease contact an administrator if you need access.","NO_ACCESS_TO_SECRET_REPO_DEMO_SEC":"Secret repository code is only visible to \u003cbold\u003eadministrator\u003c/bold\u003e, \u003cbold\u003eowners\u003c/bold\u003e, \u003cbr\u003e\u003c/br\u003ePlease contact an administrator if you need access.","NO_ACCESS_TO_SECRET_REPO_DEMO":"Your current access level is insufficient. Please contact an administrator if you need to view this content.","BRANCH_LOWER":"branch","BRANCH_UPPER":"Branch","NEW_FILE":"Create","INSUFFICIENT_PERMISSION":"Insufficient permissions","DELETE_RELEASE_ERROR":"Deletion failed, the repository data has error","COMMITS":"Commits","SELECT_RBANCH_OR_INPUT_SHA":"Please select a branch or enter a commit sha","LOADING":"Loading...","DEFALUT_RBANCH":" (Default branch)","FETCH_DATA_ERROR":"Fetch data failed","BUILDING":"Building","BUILDING_FAILED":"Build failed","BUILDING_SUCCESSFULLY":"Build successfully","BUILDING_SKIPPED":"Build skipped","BUILDING_WITH_ERROR":"Build data has error","STAR":"Star","LOOKUP_GROUP_MEMBERS":"View group administrators"},"slug-repo-not-inited":{"NOT_INITED_GUIDE":"This repository has not yet been initialized. You can choose any of the following methods to complete the initialization of the repository.","QUICK_INIT_IN_CLOUD":"Cloud fast initialization","RECOMMEND":"Recommend","YOU_CAN_USE_COMMAND_CND_FRONT_PART":"Executing the following command in workspace to \u003cbold\u003emigrate existing repository with one-liner command\u003c/bold\u003e, or directly \u003cnewFileLink\u003e{{newFileText}}\u003c/newFileLink\u003e to complete initialization. It is recommended to add\u003creadmeFileLink\u003eREADME\u003c/readmeFileLink\u003eand\u003cciFileToolTip\u003e\u003cciFileLink\u003e{{ciFileText}}\u003c/ciFileLink\u003e\u003c/ciFileToolTip\u003efile ","NEW_FILE":"create a new file","CI_FILE":"Event config file","LOCAL_INIT":"Local initialization","ACCESS_TOKEN":"access token","SSH_PUBLIC_KEY":"SSH public key","METHOD_TITLE_1":"Method 1: Bare repository migration","METHOD_TITLE_2":"Method 2: Branch migration","METHOD_TITLE_3":"Method 3: Empty repository initialization","CAN_USE_COMMAND_IN_LOCAL":"You can use the following command locally ","MIGRATE":"to migrate","ALL_TAGS_AND_BRNACH":"all branches and tags","CURRENT_BRANCH":"current branch","NEW_RPOE":"new repository.","TO_THIS_RPOE":"to this repository.","CLICK_TO_CREATE":" Click to create","USE_KEY_TO_ACCESS":"Use {{tokenName}} for Git authentication","CREATE":"to create a","REPO_IS_NOT_INITED":"The repository has not been initialized","VIEW_SOON":"Please check back later","GO_TO_EXAMPLE":"Go to example"},"refs-select":{"BRANCH_SUBFFIX":" (Default)","BRANCH_PROTECTED_SUBFFIX":" (Protected branch)","VIEW_ALL_BRANCH":"View all branches","VIEW_ALL_TAG":"View all tags","TAB_BRANCH":"Branch"},"form":{"TITLE":"Title","DESCRIPTION":"Description","INPUT_SEARCH_PLACEHOLDER":"Please search","INPUT_DEFAULT_PLACEHODLER":"Please enter","INPUT_MARKDOWN_PLACEHODLER":"Please enter a brief description, markdown supported","BTN_CANCEL":"Cancel","BTN_SUBMIT":"Submit","SELECT_DEFAULT_PLACEHODLER":"Please select"},"file-table":{"NO_FILE_IN_BRANCH":"No file in this branch","OPEN_ALL":"Expand all","HIDE":"Hide","NAME":"Name","COMMIT":"Commit","UPDATE_AT":"Updated at","BRANCH":"branch"},"slug-repo-blob":{"NO_VALID_REF":"No valid ref","HAS_NO_VALID_REF":" has no valid ref","DIRECTORY_DATA_ERROR_AND_FRESH":"The directory data has errpr, please refresh","NO_FILE_IN_BRANCH":"There is no file in the current branch","FILES":"File directory","LOADING":"Loading","PLEASE_INPUT_FILE_NAME":"Input file name","REPO_DATE_HAS_ERROR":"The current repository data has error. ","FRESH_PAGE_AND_TRY":"please refresh the page and try again","DELETE_ERROR_AND_FRESH_PAGE_SOON":"Deletion failed. The current page data has error, please refresh the page and try again","FILE":"File","HAD_DELETED_SUCCESSFULLY":"deleted successfully","HAD_DELETED_FAILED":"Failed to delete","LOADING_FILE_ERROR_PART1":"Failed to get ","LOADING_FILE_ERROR_PART2":"file content ","RELOAD":"Reload","NETWORK_ERROR":"Network error","NO_PATH_IN_BRANCH":"This path does not exist in the current branch","BACK_TO_HOME_TO_VIEW_ALL_FILE":"Return to the repository home page to view all files of the branch","NOT_FOUND_REF":"The branch you are trying to view was not found","BACK_TO_DEFAULT_BRANCH":"Back to the default branch","COPY_PATH":"Copy path","COPY_FAILED":"Copy failed","LOAD_PERMANENT_LINK_FAILED":"Failed to get permanent link information","COPY_PERMANENT_LINK":"Copy permanent link","DELETE_FILE":"Delete file","FAILED_TO_SUBMIT_FILE_DUE_TO_DATA_ERROR":"Failed to submit the file {{currentFileName}}. The file data of {{parentPath}} has error. Please refresh the page and try again","FAILED_TO_SUBMIT_FILE_DUE_TO_BACKEND_SERVICE":"Failed to submit the file {{currentFileName}}, {{errorMessage}}","FAILED_TO_SUBMIT_FILE_DUE_TO_FILE_EXISTS":"Failed to submit the file {{currentFileName}}, the current file name already exists","FAILED_TO_SUBMIT_FILE_DUE_TO_PATH_DATA_OF_FILE_ERROR":"Failed to submit the file {{currentFileName}}. The path data of the original file has error. Please try to refresh the page and edit it again","HAD_SUBMIT_SUCCESSFULLY":"submitted successfully","ID_OF_REPO_IS_EMPTY":"The repository id is empty","SOURCE_BRANCH_IS_EMPTY":"The source branch name is empty","NEW_BRANCH_NAME_SUBMITTING_IS_EMPTY":"The new branch name for the current commit cannot be empty","DESCRIPTION":"Description","EXTRA_DESCRIPTION":"Additional description","ADD_EXTRA_DESCRIPTION":"Add additional descriptions","AI_GENERATE_COMMIT_MESSAGE":"AI generate commit message","AI_GENERATE_TIP":"Did not generate a valid commit message.","AI_GENERATE_NETWORK_ERROR_TIP":"Network error, please try again later.","AI_GENERATE_SYSTEM_ERROR_TIP":"System error, please try again later.","AI_GENERATE_SYSTEM_CONTENT":"You are a developer. When submitting changes with the git command after modifying the code, you need to fill in the commit message.","DIRECTLY_SUBMIT_TO":"Submit directly to","CREATE_FOR_THIS_SUBMIT":"Submit to a ","NEW_BRANCH":"new branch","EDIT_UPPER":"Edit","VIEW_RAW":"View raw file","COPY_FILE_CONTENT":"Copy file contents","DOWNLOAD_FILE":"Download file","EDIT_FILE":"Edit file","CREATE_FILE":"Create file","TAG_CAN_NOT_OPERATE":"The current view is a tag, and this operation is not supported","NOT_BRANCH_CAN_NOT_OPERATE":"The current view is not a branch, and this operation is not supported","FILE_CONTENT_IS_EMPTY":"The file content is empty","BINARY_FILE_CAN_BE_DOWNLOADED":"The file is a binary file, having {{size}} size. you can download it directly","FILE_IS_OVER_LIMIT_THAT_CAN_NOT_BE_VIEWABLE":"The file size is {{size}}  and the content has not be displayed.","VIEWING_LFS_FILE_IS_NOT_SUPPORTED":"The LFS file is not supported to display, and the file size is {{size}}","CLICK_TO_DOWNLOAD":"Click to download","OID_OF_LFS_IS_EMPTY_AND_FRESH_PAGE_TO_RETRY":"The oid of the current LFS file is empty, please refresh the page and try again","PREVIEW":"Preview","CODE_VIEW":"Code view","VIEW_BY_ROW":"View by row","BACK_TO_HOME_PAGE_OF_REPO":"Back to repository home page","COMPARE":"Compare","FILE_HAS_NOT_BEEN_CHANGED":"The current file has not been modified","LFS_FILE_IS_ERROR":"The current LFS file has error","GEN_CONFIG_FILE_BY_AI":"Generate config file with AI","GEN_CONFIG_FILE_BY_AI_DESC":"Make requests to AI to generate configuration file","FIX_CONFIG_FILE_BY_AI":"Fix config file with AI","FIX_CONFIG_FILE_BY_AI_DESC":"Check and fix configuration file syntax and format","HIDE_DIR":"Hide file tree","EXPAND_DIR":"Collapse file tree","CI_CONFIG_INVALID":"The CI configuration is invalidate: {{errMsg}}. \u003c0\u003ePipeline syntax\u003c/0\u003e","CI_CONFIG_VALID":"The CI configuration syntax is correct. \u003c0\u003ePipeline syntax\u003c/0\u003e","CI_CONFIG_TIP":"Only check the YAML syntax of the .cnb.yml file. To view the complete configuration including files introduced via include, please refer to the preview view."},"last-commit":{"COMMITS":"commits","TOTAL":""}}},"initialLocale":"en-US","ns":["fork-dialog","validate-repo-name","common","convert-time","report","slug-tab-common","description","slug-repo-common","slug-repo-not-inited","common","refs-select","form","file-table","slug-repo-blob","last-commit"],"userConfig":null},"slugIndexPageProps":{"repoProps":{"error":false,"useIndexPage":false}},"initialState":{"global":{"appConfigManage":{"AI_ENABLED":true,"AI_CODE_ASSISTANT_ENABLED":true,"AI_DEFAULT_MODEL":"deepseek-r1-qwen-32b","WX_FAST_LOGIN_CONFIG_KEY":{"all":true,"windows":true,"mac":true},"TAPD_ENABLED":true},"e2e":false,"authInfo":{"userNickName":"","userName":"","userVerified":false,"userFreeze":false,"userEmail":"","slugName":"tencent/cloud/cos/cos-mcp","slugType":1,"slugStatus":0,"slugFreeze":false,"slugVisibility":"Public","slugRole":"Unknown","language":"en-US","ban":false,"meta":{"FE_has_fork_form":false,"FE_root_organization_setting":{"group_protection":1,"email_verification":"tencent.com"}}},"tid":"fc4bede730e436726a8a1f3b780fa76e","browser":{"ios":false,"iphone":false,"iPad":false,"android":false,"mac":false,"windows":false,"chorme":false,"safari":false,"firefox":false,"opera":false,"ie":false,"ie9":false,"edge":false,"wx":false,"wxwork":false,"wxMini":false,"qq":false,"mobileBrowser":false},"nextMatchRouter":"/[...slug]","_renderKey":"1747810635348","globalLayoutInfo":{"header":{"slugLink":null,"showHeader":true,"slugName":"tencent/cloud/cos/cos-mcp","slugVisbility":"Public","buttons":["appreciate","fork","star"],"showLogo":true,"showSearch":true,"showNoticification":true,"showFollowButton":false,"showAddIcon":true,"showUserInfo":true,"search":{"isSearchPage":false,"outerClassName":"","searchClassName":""}},"body":{"waterMark":""},"footer":{"showFooter":true,"dark":false}},"htmlDomConfig":{"body":{"overflowY":"scroll"}},"globalDialog":null,"notifications":{"data":null,"status":"initial"},"__hydrate_flag":true},"artifact":{"list":{"status":"initial","data":[],"dockerPushOnOCIMD":"","dockerPushOnLocalMD":"","dockerBuildPushOnOCIMD":"","helmPushOnOCIMD":"","helmPushOnLocalMD":"","helmBuildPushOnOCIMD":"","isEmpty":true,"page":1,"pageSize":10,"total":0,"hasMore":false,"keyword":"","sortType":"last_push_at","filterType":"docker","__hydrate_flag":false},"detail":{"status":"initial","updateStatus":"initial","code":0,"data":null,"artifactType":null,"artifactName":"","isAdmin":false,"__hydrate_flag":false},"tags":{"status":"initial","data":[],"isFirst":true,"page":1,"pageSize":30,"total":0,"hasMore":false,"keyword":"","sortType":"last_push_at","artifactType":"docker","artifactName":"","isAdmin":false,"__hydrate_flag":false},"tagDetail":{"status":"initial","updateStatus":"initial","data":null,"dockerTagDetailMD":"","helmTagDetailMD":"","artifactType":null,"artifactName":"","artifactTag":"","artifactSha":"","isAdmin":false,"detailDataStaus":"initial","code":0,"manifest":null,"manifestList":null,"manifestDataStatus":"initial","__hydrate_flag":false}},"home":{"message":"首次文案","__hydrate_flag":false},"user":{"global":{"userInfo":{"data":null,"status":"initial"},"userGroups":[],"tab":"overview","role":"ghost","userBanned":false,"userBannedReason":""},"following":{"users":[],"total":0,"page":1,"pageSize":20,"hasMore":false,"status":"initial"},"follower":{"users":[],"total":0,"page":1,"pageSize":20,"hasMore":false,"status":"initial"},"overview":{"pinnedRepoList":null,"type":"index","userCalendar":null,"userCalendarStatus":"initial","userCalendarTimeSelect":"last","userCalendarMode":"year","userCalendarTimeRange":[],"userActivitiesDay":null,"userActivitiesStatus":"initial","userActivitiesList":[],"userActivitiesHoverRepo":null},"sidebar":{"isEditting":false,"isUpdating":false,"overvMaxShowGroupsCount":19,"moreGroupCount":0,"isFollowing":false},"repo":{"data":[],"total":null,"category":"participate"},"stars":{"total":null},"__hydrate_flag":false},"slug":{"repo":{"index":{"ref":null,"content":{"data":null,"status":"initial"},"lfsDataMap":{},"selfDescription":{"licenseTabInfos":[],"licenseMaxShoingNumber":3,"selfDectriptionTabInfos":{"README-ov-file":{"value":"README-ov-file","path":"README.md"}},"selectedTabInfo":{"value":"README-ov-file","path":"README.md"},"selfDectriptionContents":{"README-ov-file":null,"CODE_OF_CONDUCT-ov-file":null,"CONTRIBUTING-ov-file":null,"SECURITY-ov-file":null,"GOVERNANCE-ov-file":null,"SUPPORT-ov-file":null,"CNBYML-ov-file":null}},"forkFromInfo":null,"artifactData":{"data":null,"status":"initial"},"artifactTotalCount":0,"releasesInfo":null,"commitDataMap":{},"languageRatios":null,"licenses":[],"contributorInfo":null,"lastPushBranch":null,"__hydrate_flag":false},"branches":{"keyWord":"","branchesViewState":{"data":null,"status":"initial"},"pageNumber":1,"pageSize":10,"type":"overview","__hydrate_flag":false},"tags":{"total":0,"data":null,"status":"initial","__hydrate_flag":false},"releases":{"detail":{"data":null,"status":"initial","__hydrate_flag":false},"list":{"total":0,"data":null,"status":"initial","__hydrate_flag":false}},"blob":{"path":"README.md","ref":"refs/heads/master","refType":"branch","contentDataMap":{"README.md":{"data":{"type":"blob","initialized":true,"repository":{"slug":"tencent/cloud/cos/cos-mcp","http_clone_url":"https://cnb.cool/tencent/cloud/cos/cos-mcp.git","ssh_clone_url":"","head_ref":"refs/heads/master","usage":{"git_size_in_kib":"1820","lfs_size_in_kib":"0"}},"branch_count":1,"tag_count":0,"is_protected":false,"commit_count":12,"commit_count_exceeded":false,"path":"README.md","name":"README.md","size":6763,"encoding":"base64","content":"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","file_stat":{"mime_type":{"content_type":"text/plain; charset=utf-8","externsion":".txt","is_representable_as_text":true,"is_browsable_binary_type":false,"is_text":true,"is_image":false,"is_svg_image":false,"is_pdf":false,"is_video":false,"is_audio":false}},"last_commit":{"sha":"fea4008f0fa78e617f7332387dc73032ee554d0b","commit":{"author":{"name":"cnb.acooLZzSAHA","email":"<EMAIL>","date":"2025-04-21T17:35:32+08:00"},"committer":{"name":"cnb.acooLZzSAHA","email":"<EMAIL>","date":"2025-04-21T17:35:32+08:00"},"message":"add changelog\n","tree":{"sha":"cb5b84a6a89fcb9c42a8d6721b6106a3a9b0c496"},"comment_count":0,"verification":{"verified":false,"reason":"","key_id":"","has_signature":false,"signer":"","signed_by_cnb":false,"verified_at":""}},"author":{"username":"cnb.acooLZzSAHA","nickname":"休戚与共🙊","freeze":false},"committer":{"username":"cnb.acooLZzSAHA","nickname":"休戚与共🙊","freeze":false},"parents":[{"sha":"1df73a4a3450cb670adea3324f579dbf55ef8518"}]},"file_size_limit":1048576,"file_size_exceeded":false,"is_lfs":false,"lfs_oid":"","lfs_hash_algorithm":"","lfs_size_in_byte":""},"status":"success"},"":{"status":"success","data":{"type":"tree","initialized":true,"repository":{"slug":"tencent/cloud/cos/cos-mcp","http_clone_url":"https://cnb.cool/tencent/cloud/cos/cos-mcp.git","ssh_clone_url":"","head_ref":"refs/heads/master","usage":{"git_size_in_kib":"1820","lfs_size_in_kib":"0"}},"branch_count":1,"tag_count":0,"is_protected":false,"commit_count":12,"commit_count_exceeded":false,"path":"","name":"","entries":[{"sha":"c682a79d8021fc96cc3657a8b1fa40abf34b4169","type":"dir","path":"public","name":"public","is_lfs":false},{"sha":"cdfc174642c5d3d30c16e84a24cac988ae90d39c","type":"dir","path":"src","name":"src","is_lfs":false},{"sha":"142ef92e66e795a984181c3bce1cefce150dc5b2","type":"file","path":".envTemplate","name":".envTemplate","is_lfs":false},{"sha":"797ab2d1c178d7afa0a471670a33d02433a732cb","type":"file","path":".eslintrc.js","name":".eslintrc.js","is_lfs":false},{"sha":"64e2cface0763253f1eecc216fbc87b2ed6be086","type":"file","path":".gitignore","name":".gitignore","is_lfs":false},{"sha":"88f43007fef83bb3034c826b7d6b87ae90df246e","type":"file","path":".prettierrc","name":".prettierrc","is_lfs":false},{"sha":"a5bb3f846702d1bf2d0bf14945384efff7698465","type":"file","path":"CHANGELOG.md","name":"CHANGELOG.md","is_lfs":false},{"sha":"38e6c849bc4c2f188a3fb6bb7ff532ce132e9b0c","type":"file","path":"Contributing Guidelines.md","name":"Contributing Guidelines.md","is_lfs":false},{"sha":"62d3bd25dfdc434b870db4ad3044a08992556bf7","type":"file","path":"License.txt","name":"License.txt","is_lfs":false},{"sha":"8b81598c5a78dee2858701f8df0d16a96e57c37d","type":"file","path":"README.en.md","name":"README.en.md","is_lfs":false},{"sha":"f313c6e1f521cd31f7c07549b1c5c791866898bb","type":"file","path":"README.md","name":"README.md","is_lfs":false},{"sha":"394b40409221758b24a74256ab51d540551f0335","type":"file","path":"code-of-conduct.md","name":"code-of-conduct.md","is_lfs":false},{"sha":"40dd835446e4b9ed00b3509eb3f4ab16d1da2f80","type":"file","path":"eslint.config.cjs","name":"eslint.config.cjs","is_lfs":false},{"sha":"4c5b3d445c412c7ba2d982801289361da9080c04","type":"file","path":"jest.config.js","name":"jest.config.js","is_lfs":false},{"sha":"45d0c21b5d0a53409f47abde8e3eb43753ea1af4","type":"file","path":"package-lock.json","name":"package-lock.json","is_lfs":false},{"sha":"f7543a28e4c3b89d16b45cbfe5a9002d78882e57","type":"file","path":"package.json","name":"package.json","is_lfs":false},{"sha":"e9877004997ac91a2e923cf0a05945a0b428d7c8","type":"file","path":"tsconfig.json","name":"tsconfig.json","is_lfs":false}],"last_commit":{"sha":"fea4008f0fa78e617f7332387dc73032ee554d0b","commit":{"author":{"name":"cnb.acooLZzSAHA","email":"<EMAIL>","date":"2025-04-21T17:35:32+08:00"},"committer":{"name":"cnb.acooLZzSAHA","email":"<EMAIL>","date":"2025-04-21T17:35:32+08:00"},"message":"add changelog\n","tree":{"sha":"cb5b84a6a89fcb9c42a8d6721b6106a3a9b0c496"},"comment_count":0,"verification":{"verified":false,"reason":"","key_id":"","has_signature":false,"signer":"","signed_by_cnb":false,"verified_at":""}},"author":{"username":"cnb.acooLZzSAHA","nickname":"休戚与共🙊","freeze":false},"committer":{"username":"cnb.acooLZzSAHA","nickname":"休戚与共🙊","freeze":false},"parents":[{"sha":"1df73a4a3450cb670adea3324f579dbf55ef8518"}]},"read_me":{"name":"README.en.md","encoding":"base64","content":"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","file_stat":{"mime_type":{"content_type":"text/plain; charset=utf-8","externsion":".txt","is_representable_as_text":true,"is_browsable_binary_type":false,"is_text":true,"is_image":false,"is_svg_image":false,"is_pdf":false,"is_video":false,"is_audio":false}}},"file_list_limit":500,"file_list_exceeded":false,"has_web_trigger":false}}},"commitDataMap":{},"lfsDataMap":{},"viewMode":"blob","newFileName":null,"directoryVisible":false,"modifiedFile":false,"isProtectedRef":{"status":false,"ref":"refs/heads/master"},"__hydrate_flag":true},"pulls":{"common":{"commitCount":0,"changesCount":0,"__hydrate_flag":false},"detail":{"data":null,"status":"initial","isWorkInProgress":false,"activities":[],"actCurrentPage":1,"actTotalCount":0,"lastAIReviewId":"","activityStatus":"initial","__hydrate_flag":false}},"settings":{"common":{"menuValue":"basic","__hydrate_flag":false},"branchProtect":{"ruleList":{"data":null,"status":"initial"},"__hydrate_flag":false},"basic":{"repoInfo":null,"defaultBranch":null,"pushLimit":null,"__hydrate_flag":false},"advanced":{"transfer":{"target":null,"searchVal":""},"__hydrate_flag":false},"usage":{"isRepoGitCapacityFull":false,"repo":null,"productsDocker":null,"dockerGCSchedule":null,"productsHelm":null,"amount":null,"asset":{"used":null,"list":null},"changeCapacityDialogInfo":{"isShow":false,"title":"","value":"0","max":"0","used":"0","type":"git"},"__hydrate_flag":false},"events":{"settings":null,"__hydrate_flag":false},"pullRequest":{"settings":null,"__hydrate_flag":false},"member":{"viewData":{"type":"member","memberList":null},"totalInfo":{"status":"initial","data":{"inherit":null,"member":null,"outter":null}},"curInheritTable":null,"__hydrate_flag":false}},"compare":{"activeBranch":"","defaultBranch":"","headRepo":"","baseRepos":[],"baseRef":"","headRef":"","isJustDiffShow":false,"__hydrate_flag":false},"stargazers":{"data":null,"totalMap":null,"pageNumber":1,"pageSize":20,"currentTab":"all","__hydrate_flag":false},"global":{"repoInformation":{"status":"success","data":{"id":1911987981148717000,"name":"cos-mcp","freeze":false,"status":0,"visibility_level":"Public","created_at":"2025-04-15T03:40:32Z","updated_at":"2025-04-15T03:40:32Z","description":"腾讯云COS mcp服务器","site":"","topics":"","license":"","display_module":{"activity":true,"contributors":true,"release":true},"star_count":0,"fork_count":0,"mark_count":0,"last_updated_at":null,"web_url":"https://cnb.cool/tencent/cloud/cos/cos-mcp","path":"tencent/cloud/cos/cos-mcp","tags":null,"open_issue_count":0,"open_pull_request_count":0,"languages":{"language":"TypeScript","color":"#3178c6"},"second_languages":{"language":"","color":""},"last_update_username":"cnb.acooLZzSAHA","last_update_nickname":"休戚与共🙊","access":"Unknown","stared":false,"star_time":"0001-01-01T00:00:00Z","pinned":false,"pinned_time":"0001-01-01T00:00:00Z"}},"sponsor":{"fundingInfo":{"users":null,"urls":null},"topActiveUsers":[{"id":"1911675010759245824","username":"cnb.acooLZzSAHA","nickname":"休戚与共🙊","type":0,"verified":1,"verified_expire_in":"2026-04-15T03:38:15Z","created_at":"2025-04-14T06:56:54Z","gender":0,"bio":"","company":"","location":"","site":"","address":"","wechat_mp":"","wechat_mp_qrcode":"","appreciate_status":0,"follow_count":0,"follower_count":0,"reward_count":0,"reward_amount":0,"stars_count":0,"group_count":1,"repo_count":1,"mission_count":0,"registry_count":0,"follow_repo_count":1,"follow_mission_count":0,"is_following":false}],"customUsers":[]},"isNoAccessToRepoDemo":false,"__hydrate_flag":true},"issues":{"activities":[],"actCurrentPage":1,"actTotalCount":0,"activityStatus":"initial","__hydrate_flag":false},"contributor":{"trend":{"status":"initial","data":null},"__hydrate_flag":false},"commits":{"refName":null,"ref":null,"refType":"branch","path":null,"status":"initial","data":{},"page":1,"pageSize":20,"hasMore":false,"filters":{"users":[],"updatedTimeBegin":null,"updateTimeEnd":null},"__hydrate_flag":false},"security":{"common":{"menuValue":"setting","__hydrate_flag":false}},"commit":{"commitInfo":null,"status":"initial","__hydrate_flag":false}},"global":{"layout":{"slugTab":{}},"chargeStausInfo":{"data":null,"status":"initial"},"openIssues":{"status":"success","data":0},"closedIssues":{"status":"success","data":0},"openPulls":{"status":"success","data":0},"closedPulls":{"status":"success","data":0},"userInfoMap":{},"accessLevelToSecretRepo":null,"accessLevelToRootGroup":null,"__hydrate_flag":true}},"groupTree":{"group":{"groupList":[],"getGroupListStatus":"initial","page":1,"pageSize":10,"haveMore":false},"user":{"groupList":[],"getGroupListStatus":"initial","page":1,"pageSize":10,"haveMore":false},"__hydrate_flag":false},"repoList":{"status":"initial","groupRepoList":[],"userRepoList":[],"userStarRepoList":[],"userStarredRepoList":[],"repoType":"all","sortType":"last_updated_at","keyword":"","hasNoRepos":false,"descendant":"all","category":"participate","page":1,"pageSize":20,"total":0,"hasMore":false,"__hydrate_flag":false},"profile":{"setting":{"userProfile":null,"updateStatus":"initial","checkDeleteAccountInfo":null,"__hydrate_flag":false},"tokens":{"tokensData":{"data":null,"status":"initial"},"page":1,"pageSize":10,"total":null,"showDeleteDialog":false,"noData":false,"deleteDialog":null,"__hydrate_flag":false},"create_token":{"tokenConfig":null,"__hydrate_flag":false},"auth":{"phone":null,"__hydrate_flag":false},"email":{"state":null,"__hydrate_flag":false},"tapd":{"boundInfo":null,"__hydrate_flag":false}},"commitList":{"status":"initial","data":{},"page":1,"pageSize":20,"hasMore":false,"currentRef":null,"filters":{"users":[],"updatedTimeBegin":null,"updateTimeEnd":null},"__hydrate_flag":false},"dashboard":{"activityCounts":{"data":[],"status":"initial"},"activityWorkFlowData":{"status":"initial"},"activityMineData":{"status":"initial"},"todoCount":{"data":{},"status":"initial"},"todoData":{"data":null,"pageInfo":null,"status":"initial"},"mineCount":{"data":{},"status":"initial"},"mineData":{"data":null,"pageInfo":null,"status":"initial"},"repoList":{"data":[],"status":"initial"},"groupList":{"data":[],"status":"initial"},"pinnedstarsList":{"data":[],"status":"initial"},"staredList":{"data":[],"status":"initial"},"exploreRepoList":{"data":null,"status":"initial"},"__hydrate_flag":false},"workspace":{"list":[],"loading":false,"hasMore":true,"hasNoWorkspace":false,"page":1,"pageSize":20,"runningTotal":0,"filterTotal":0,"status":"","slug":"","branch":"","start":"","end":"","__hydrate_flag":false},"group":{"overview":{"NEED_HYDRATE":false,"status":"initial","data":null,"updateRepos":{"status":"initial","data":[],"page":1,"pageSize":10,"total":0,"hasMore":true},"readmeRepo":null,"pinnedRepoList":null,"__hydrate_flag":false},"setting":{"common":{"menuValue":"basic","dialog":{"visible":false,"type":"UPDATE"},"can_show_members":true,"hide_members":0,"can_show_sub_groups":true,"hide_sub_groups":0,"settings_status":"initial","show_private_repo_watermark":0,"can_show_watermark":false,"root_group_protection":false,"group_protection":0,"__hydrate_flag":false},"charge":{"formattedPacks":null,"packs":{"data":null,"status":"initial"},"usage":{"data":null,"status":"initial"},"formattedUsage":null,"serverMonth":"2025-05","viewingMonth":"","viewingDate":"","trendBymonth":null,"repoUsage":null,"formattedRepoUsage":null,"formatRepoVolumeLoading":false,"budget":{"info":{"data":null,"status":"initial"},"price":null},"__hydrate_flag":false},"branch":{"ruleList":{"data":null,"status":"initial"},"__hydrate_flag":false}},"members":{"members":{"status":"initial","data":[],"page":1,"pageSize":10,"total":0,"hasMore":true,"count":0},"inheritMembers":{"status":"initial","data":[],"page":1,"pageSize":10,"total":0,"hasMore":true,"count":0},"allMembers":{"status":"initial","data":{},"page":1,"pageSize":10,"total":0,"hasMore":true,"count":0},"allOutsideCollaborators":{"status":"initial","data":{},"page":1,"pageSize":10,"total":0,"hasMore":true,"count":0},"__hydrate_flag":false}},"report":{"userInfo":null,"repoInfo":null,"__hydrate_flag":false},"mission":{"list":{"status":"initial","data":[],"sortType":"createdAt_descend","keyword":"","hasNoMissions":false,"descendant":"all","page":1,"pageSize":10,"total":0,"hasMore":false,"countInfo":{"participate":0,"starred":0},"__hydrate_flag":false},"views":{"interceptionInfo":{"status":"initial","code":-1,"message":"","slug":"","repos":[]},"keyword":"","list":{"status":"initial","sortStatus":"initial"},"detail":{"status":"initial"},"currentViewId":"","showConfirm":false,"redoData":null,"createIssueInfo":{"visible":false,"data":{"repo_slug":"","name":"","description":"","assignees":[],"labels":[],"priority":null}},"showSelectorsLiteViews":[],"sortingRow":["","","ltr"],"__hydrate_flag":false},"resources":{"data":[],"type":"","status":"initial","pathStatus":"initial","__hydrate_flag":false},"setting":{"menuValue":"basic","missionsInfo":{"data":null,"status":"initial"},"missionsMember":{"viewData":{"type":"member","memberInfo":{"data":[{"inheritPath":null,"users":[],"total":0}],"status":"initial"}},"totalInfo":{"member":null,"inherit":null,"outter":null},"keyword":""},"__hydrate_flag":false},"fields":{"data":[],"status":"initial","__hydrate_flag":false},"tableOperations":{"selectedTableId":"000","selectedCellIds":[],"onCopyData":[],"isCopying":false,"isAutoFilling":false,"__hydrate_flag":false},"reviewerMap":{"data":{},"status":"initial","__hydrate_flag":false}},"search":{"conditions":{"keyword":"","type":"repo","orderBy":"best_match","filterArr":[]},"repos":[],"groups":[],"publicRepos":[],"missions":[],"registries":[],"listening":true,"__hydrate_flag":false},"artifactory":{"artifactoryDetail":{"status":"initial","data":[],"page":1,"pageSize":10,"packageCount":0,"sortType":"last_push_at","keyword":"","detail":null,"artifactoryType":"maven","artifactoryName":"","npmConfigMD":"","npmPushMD":"","npmPullMD":"","npmBuildPushMD":"","yarnConfigMD":"","yarnPushMD":"","yarnPullMD":"","yarnBuildPushMD":"","ohpmConfigMD":"","ohpmPullMD":"","ohpmPushMD":"","ohpmDevConfigMD":"","ohpmBuildPushMD":"","mavenConfigMD":"","mavenPushMD":"","mavenPullMD":"","mavenDevConfigMD":"","mavenBuildConfigMD":"","mavenBuildPushMD":"","gradleConfigMD":"","gradlePushMD":"","gradlePullMD":"","gradleBuildPushMD":"","hasMore":false,"isEmpty":false,"isFirst":true,"code":0,"__hydrate_flag":false},"artifactoryList":{"status":"initial","data":[],"page":1,"pageSize":10,"filterType":"all","sortType":"created_at_desc","keyword":"","desc":true,"hasMore":false,"isEmpty":false,"isFirst":true,"total":0,"__hydrate_flag":false},"packageDetail":{"status":"initial","data":null,"artifactoryType":"maven","artifactoryName":"","packageName":"","code":0,"__hydrate_flag":false},"packageTagDetail":{"status":"initial","data":null,"artifactoryType":"maven","artifactoryName":"","packageName":"","tagName":"","code":0,"__hydrate_flag":false},"packageTagList":{"status":"initial","data":[],"artifactoryType":"maven","artifactoryName":"","packageName":"","sortType":"last_push_at","keyword":"","hasMore":false,"page":1,"pageSize":10,"__hydrate_flag":false},"settings":{"menuValue":"basic","policyInfo":null,"systemAcceleration":[],"quota":null,"packageQuotas":[],"page":1,"pageSize":10,"total":0,"artifactoryMember":{"type":"member","inheritPath":"","users":[],"total":0,"page":1,"pageSize":10,"status":"initial","totalInfo":{"member":0,"inherit":0,"outter":0},"keyword":""},"__hydrate_flag":false},"artifactoryMine":{"status":"initial","data":[],"page":1,"pageSize":10,"role":"Guest","keyword":"","hasMore":false,"total":0,"__hydrate_flag":false}},"announcement":{"announcements":{"data":[],"status":"initial"},"__hydrate_flag":false}}},"initialState":{"global":{"appConfigManage":{"AI_ENABLED":true,"AI_CODE_ASSISTANT_ENABLED":true,"AI_DEFAULT_MODEL":"deepseek-r1-qwen-32b","WX_FAST_LOGIN_CONFIG_KEY":{"all":true,"windows":true,"mac":true},"TAPD_ENABLED":true},"e2e":false,"authInfo":{"userNickName":"","userName":"","userVerified":false,"userFreeze":false,"userEmail":"","slugName":"tencent/cloud/cos/cos-mcp","slugType":1,"slugStatus":0,"slugFreeze":false,"slugVisibility":"Public","slugRole":"Unknown","language":"en-US","ban":false,"meta":{"FE_has_fork_form":false,"FE_root_organization_setting":{"group_protection":1,"email_verification":"tencent.com"}}},"tid":"fc4bede730e436726a8a1f3b780fa76e","browser":{"ios":false,"iphone":false,"iPad":false,"android":false,"mac":false,"windows":false,"chorme":false,"safari":false,"firefox":false,"opera":false,"ie":false,"ie9":false,"edge":false,"wx":false,"wxwork":false,"wxMini":false,"qq":false,"mobileBrowser":false},"nextMatchRouter":"/[...slug]","_renderKey":"1747810635348","globalLayoutInfo":{"header":{"slugLink":null,"showHeader":true,"slugName":null,"slugVisbility":null,"buttons":[],"showLogo":true,"showSearch":true,"showNoticification":true,"showFollowButton":false,"showAddIcon":true,"showUserInfo":true,"search":{"isSearchPage":false,"outerClassName":"","searchClassName":""}},"body":{"waterMark":""},"footer":{"showFooter":true,"dark":false}},"htmlDomConfig":{"body":{"overflowY":"scroll"}},"globalDialog":null,"notifications":{"data":null,"status":"initial"},"__hydrate_flag":true},"artifact":{"list":{"status":"initial","data":[],"dockerPushOnOCIMD":"","dockerPushOnLocalMD":"","dockerBuildPushOnOCIMD":"","helmPushOnOCIMD":"","helmPushOnLocalMD":"","helmBuildPushOnOCIMD":"","isEmpty":true,"page":1,"pageSize":10,"total":0,"hasMore":false,"keyword":"","sortType":"last_push_at","filterType":"docker","__hydrate_flag":false},"detail":{"status":"initial","updateStatus":"initial","code":0,"data":null,"artifactType":null,"artifactName":"","isAdmin":false,"__hydrate_flag":false},"tags":{"status":"initial","data":[],"isFirst":true,"page":1,"pageSize":30,"total":0,"hasMore":false,"keyword":"","sortType":"last_push_at","artifactType":"docker","artifactName":"","isAdmin":false,"__hydrate_flag":false},"tagDetail":{"status":"initial","updateStatus":"initial","data":null,"dockerTagDetailMD":"","helmTagDetailMD":"","artifactType":null,"artifactName":"","artifactTag":"","artifactSha":"","isAdmin":false,"detailDataStaus":"initial","code":0,"manifest":null,"manifestList":null,"manifestDataStatus":"initial","__hydrate_flag":false}},"home":{"message":"首次文案","__hydrate_flag":false},"user":{"global":{"userInfo":{"data":null,"status":"initial"},"userGroups":[],"tab":"overview","role":"ghost","userBanned":false,"userBannedReason":""},"following":{"users":[],"total":0,"page":1,"pageSize":20,"hasMore":false,"status":"initial"},"follower":{"users":[],"total":0,"page":1,"pageSize":20,"hasMore":false,"status":"initial"},"overview":{"pinnedRepoList":null,"type":"index","userCalendar":null,"userCalendarStatus":"initial","userCalendarTimeSelect":"last","userCalendarMode":"year","userCalendarTimeRange":[],"userActivitiesDay":null,"userActivitiesStatus":"initial","userActivitiesList":[],"userActivitiesHoverRepo":null},"sidebar":{"isEditting":false,"isUpdating":false,"overvMaxShowGroupsCount":19,"moreGroupCount":0,"isFollowing":false},"repo":{"data":[],"total":null,"category":"participate"},"stars":{"total":null},"__hydrate_flag":false},"slug":{"repo":{"index":{"ref":null,"content":{"data":null,"status":"initial"},"lfsDataMap":{},"selfDescription":{"licenseTabInfos":[],"licenseMaxShoingNumber":3,"selfDectriptionTabInfos":{"README-ov-file":{"value":"README-ov-file","path":"README.md"}},"selectedTabInfo":{"value":"README-ov-file","path":"README.md"},"selfDectriptionContents":{"README-ov-file":null,"CODE_OF_CONDUCT-ov-file":null,"CONTRIBUTING-ov-file":null,"SECURITY-ov-file":null,"GOVERNANCE-ov-file":null,"SUPPORT-ov-file":null,"CNBYML-ov-file":null}},"forkFromInfo":null,"artifactData":{"data":null,"status":"initial"},"artifactTotalCount":0,"releasesInfo":null,"commitDataMap":{},"languageRatios":null,"licenses":[],"contributorInfo":null,"lastPushBranch":null,"__hydrate_flag":false},"branches":{"keyWord":"","branchesViewState":{"data":null,"status":"initial"},"pageNumber":1,"pageSize":10,"type":"overview","__hydrate_flag":false},"tags":{"total":0,"data":null,"status":"initial","__hydrate_flag":false},"releases":{"detail":{"data":null,"status":"initial","__hydrate_flag":false},"list":{"total":0,"data":null,"status":"initial","__hydrate_flag":false}},"blob":{"path":"","ref":null,"refType":null,"contentDataMap":{},"commitDataMap":{},"lfsDataMap":{},"viewMode":"blob","newFileName":null,"directoryVisible":false,"modifiedFile":false,"isProtectedRef":{"status":false,"ref":""},"__hydrate_flag":false},"pulls":{"common":{"commitCount":0,"changesCount":0,"__hydrate_flag":false},"detail":{"data":null,"status":"initial","isWorkInProgress":false,"activities":[],"actCurrentPage":1,"actTotalCount":0,"lastAIReviewId":"","activityStatus":"initial","__hydrate_flag":false}},"settings":{"common":{"menuValue":"basic","__hydrate_flag":false},"branchProtect":{"ruleList":{"data":null,"status":"initial"},"__hydrate_flag":false},"basic":{"repoInfo":null,"defaultBranch":null,"pushLimit":null,"__hydrate_flag":false},"advanced":{"transfer":{"target":null,"searchVal":""},"__hydrate_flag":false},"usage":{"isRepoGitCapacityFull":false,"repo":null,"productsDocker":null,"dockerGCSchedule":null,"productsHelm":null,"amount":null,"asset":{"used":null,"list":null},"changeCapacityDialogInfo":{"isShow":false,"title":"","value":"0","max":"0","used":"0","type":"git"},"__hydrate_flag":false},"events":{"settings":null,"__hydrate_flag":false},"pullRequest":{"settings":null,"__hydrate_flag":false},"member":{"viewData":{"type":"member","memberList":null},"totalInfo":{"status":"initial","data":{"inherit":null,"member":null,"outter":null}},"curInheritTable":null,"__hydrate_flag":false}},"compare":{"activeBranch":"","defaultBranch":"","headRepo":"","baseRepos":[],"baseRef":"","headRef":"","isJustDiffShow":false,"__hydrate_flag":false},"stargazers":{"data":null,"totalMap":null,"pageNumber":1,"pageSize":20,"currentTab":"all","__hydrate_flag":false},"global":{"repoInformation":{"data":null,"status":"initial"},"sponsor":{"fundingInfo":null,"topActiveUsers":[],"customUsers":[]},"isNoAccessToRepoDemo":false,"__hydrate_flag":false},"issues":{"activities":[],"actCurrentPage":1,"actTotalCount":0,"activityStatus":"initial","__hydrate_flag":false},"contributor":{"trend":{"status":"initial","data":null},"__hydrate_flag":false},"commits":{"refName":null,"ref":null,"refType":"branch","path":null,"status":"initial","data":{},"page":1,"pageSize":20,"hasMore":false,"filters":{"users":[],"updatedTimeBegin":null,"updateTimeEnd":null},"__hydrate_flag":false},"security":{"common":{"menuValue":"setting","__hydrate_flag":false}},"commit":{"commitInfo":null,"status":"initial","__hydrate_flag":false}},"global":{"layout":{"slugTab":{}},"chargeStausInfo":{"data":null,"status":"initial"},"openIssues":{"data":null,"status":"initial"},"closedIssues":{"data":null,"status":"initial"},"openPulls":{"data":null,"status":"initial"},"closedPulls":{"data":null,"status":"initial"},"userInfoMap":{},"accessLevelToSecretRepo":null,"accessLevelToRootGroup":null,"__hydrate_flag":false}},"groupTree":{"group":{"groupList":[],"getGroupListStatus":"initial","page":1,"pageSize":10,"haveMore":false},"user":{"groupList":[],"getGroupListStatus":"initial","page":1,"pageSize":10,"haveMore":false},"__hydrate_flag":false},"repoList":{"status":"initial","groupRepoList":[],"userRepoList":[],"userStarRepoList":[],"userStarredRepoList":[],"repoType":"all","sortType":"last_updated_at","keyword":"","hasNoRepos":false,"descendant":"all","category":"participate","page":1,"pageSize":20,"total":0,"hasMore":false,"__hydrate_flag":false},"profile":{"setting":{"userProfile":null,"updateStatus":"initial","checkDeleteAccountInfo":null,"__hydrate_flag":false},"tokens":{"tokensData":{"data":null,"status":"initial"},"page":1,"pageSize":10,"total":null,"showDeleteDialog":false,"noData":false,"deleteDialog":null,"__hydrate_flag":false},"create_token":{"tokenConfig":null,"__hydrate_flag":false},"auth":{"phone":null,"__hydrate_flag":false},"email":{"state":null,"__hydrate_flag":false},"tapd":{"boundInfo":null,"__hydrate_flag":false}},"commitList":{"status":"initial","data":{},"page":1,"pageSize":20,"hasMore":false,"currentRef":null,"filters":{"users":[],"updatedTimeBegin":null,"updateTimeEnd":null},"__hydrate_flag":false},"dashboard":{"activityCounts":{"data":[],"status":"initial"},"activityWorkFlowData":{"status":"initial"},"activityMineData":{"status":"initial"},"todoCount":{"data":{},"status":"initial"},"todoData":{"data":null,"pageInfo":null,"status":"initial"},"mineCount":{"data":{},"status":"initial"},"mineData":{"data":null,"pageInfo":null,"status":"initial"},"repoList":{"data":[],"status":"initial"},"groupList":{"data":[],"status":"initial"},"pinnedstarsList":{"data":[],"status":"initial"},"staredList":{"data":[],"status":"initial"},"exploreRepoList":{"data":null,"status":"initial"},"__hydrate_flag":false},"workspace":{"list":[],"loading":false,"hasMore":true,"hasNoWorkspace":false,"page":1,"pageSize":20,"runningTotal":0,"filterTotal":0,"status":"","slug":"","branch":"","start":"","end":"","__hydrate_flag":false},"group":{"overview":{"NEED_HYDRATE":false,"status":"initial","data":null,"updateRepos":{"status":"initial","data":[],"page":1,"pageSize":10,"total":0,"hasMore":true},"readmeRepo":null,"pinnedRepoList":null,"__hydrate_flag":false},"setting":{"common":{"menuValue":"basic","dialog":{"visible":false,"type":"UPDATE"},"can_show_members":true,"hide_members":0,"can_show_sub_groups":true,"hide_sub_groups":0,"settings_status":"initial","show_private_repo_watermark":0,"can_show_watermark":false,"root_group_protection":false,"group_protection":0,"__hydrate_flag":false},"charge":{"formattedPacks":null,"packs":{"data":null,"status":"initial"},"usage":{"data":null,"status":"initial"},"formattedUsage":null,"serverMonth":"2025-05","viewingMonth":"","viewingDate":"","trendBymonth":null,"repoUsage":null,"formattedRepoUsage":null,"formatRepoVolumeLoading":false,"budget":{"info":{"data":null,"status":"initial"},"price":null},"__hydrate_flag":false},"branch":{"ruleList":{"data":null,"status":"initial"},"__hydrate_flag":false}},"members":{"members":{"status":"initial","data":[],"page":1,"pageSize":10,"total":0,"hasMore":true,"count":0},"inheritMembers":{"status":"initial","data":[],"page":1,"pageSize":10,"total":0,"hasMore":true,"count":0},"allMembers":{"status":"initial","data":{},"page":1,"pageSize":10,"total":0,"hasMore":true,"count":0},"allOutsideCollaborators":{"status":"initial","data":{},"page":1,"pageSize":10,"total":0,"hasMore":true,"count":0},"__hydrate_flag":false}},"report":{"userInfo":null,"repoInfo":null,"__hydrate_flag":false},"mission":{"list":{"status":"initial","data":[],"sortType":"createdAt_descend","keyword":"","hasNoMissions":false,"descendant":"all","page":1,"pageSize":10,"total":0,"hasMore":false,"countInfo":{"participate":0,"starred":0},"__hydrate_flag":false},"views":{"interceptionInfo":{"status":"initial","code":-1,"message":"","slug":"","repos":[]},"keyword":"","list":{"status":"initial","sortStatus":"initial"},"detail":{"status":"initial"},"currentViewId":"","showConfirm":false,"redoData":null,"createIssueInfo":{"visible":false,"data":{"repo_slug":"","name":"","description":"","assignees":[],"labels":[],"priority":null}},"showSelectorsLiteViews":[],"sortingRow":["","","ltr"],"__hydrate_flag":false},"resources":{"data":[],"type":"","status":"initial","pathStatus":"initial","__hydrate_flag":false},"setting":{"menuValue":"basic","missionsInfo":{"data":null,"status":"initial"},"missionsMember":{"viewData":{"type":"member","memberInfo":{"data":[{"inheritPath":null,"users":[],"total":0}],"status":"initial"}},"totalInfo":{"member":null,"inherit":null,"outter":null},"keyword":""},"__hydrate_flag":false},"fields":{"data":[],"status":"initial","__hydrate_flag":false},"tableOperations":{"selectedTableId":"000","selectedCellIds":[],"onCopyData":[],"isCopying":false,"isAutoFilling":false,"__hydrate_flag":false},"reviewerMap":{"data":{},"status":"initial","__hydrate_flag":false}},"search":{"conditions":{"keyword":"","type":"repo","orderBy":"best_match","filterArr":[]},"repos":[],"groups":[],"publicRepos":[],"missions":[],"registries":[],"listening":true,"__hydrate_flag":false},"artifactory":{"artifactoryDetail":{"status":"initial","data":[],"page":1,"pageSize":10,"packageCount":0,"sortType":"last_push_at","keyword":"","detail":null,"artifactoryType":"maven","artifactoryName":"","npmConfigMD":"","npmPushMD":"","npmPullMD":"","npmBuildPushMD":"","yarnConfigMD":"","yarnPushMD":"","yarnPullMD":"","yarnBuildPushMD":"","ohpmConfigMD":"","ohpmPullMD":"","ohpmPushMD":"","ohpmDevConfigMD":"","ohpmBuildPushMD":"","mavenConfigMD":"","mavenPushMD":"","mavenPullMD":"","mavenDevConfigMD":"","mavenBuildConfigMD":"","mavenBuildPushMD":"","gradleConfigMD":"","gradlePushMD":"","gradlePullMD":"","gradleBuildPushMD":"","hasMore":false,"isEmpty":false,"isFirst":true,"code":0,"__hydrate_flag":false},"artifactoryList":{"status":"initial","data":[],"page":1,"pageSize":10,"filterType":"all","sortType":"created_at_desc","keyword":"","desc":true,"hasMore":false,"isEmpty":false,"isFirst":true,"total":0,"__hydrate_flag":false},"packageDetail":{"status":"initial","data":null,"artifactoryType":"maven","artifactoryName":"","packageName":"","code":0,"__hydrate_flag":false},"packageTagDetail":{"status":"initial","data":null,"artifactoryType":"maven","artifactoryName":"","packageName":"","tagName":"","code":0,"__hydrate_flag":false},"packageTagList":{"status":"initial","data":[],"artifactoryType":"maven","artifactoryName":"","packageName":"","sortType":"last_push_at","keyword":"","hasMore":false,"page":1,"pageSize":10,"__hydrate_flag":false},"settings":{"menuValue":"basic","policyInfo":null,"systemAcceleration":[],"quota":null,"packageQuotas":[],"page":1,"pageSize":10,"total":0,"artifactoryMember":{"type":"member","inheritPath":"","users":[],"total":0,"page":1,"pageSize":10,"status":"initial","totalInfo":{"member":0,"inherit":0,"outter":0},"keyword":""},"__hydrate_flag":false},"artifactoryMine":{"status":"initial","data":[],"page":1,"pageSize":10,"role":"Guest","keyword":"","hasMore":false,"total":0,"__hydrate_flag":false}},"announcement":{"announcements":{"data":[],"status":"initial"},"__hydrate_flag":false}},"__N_SSP":true},"page":"/[...slug]","query":{"slug":["tencent","cloud","cos","cos-mcp","-","blob","master","README.md"]},"buildId":"12216581d5b61465","assetPrefix":"https://cnb.cdn-go.cn/frontend/latest/12216581d5b61465","isFallback":false,"isExperimentalCompile":false,"dynamicIds":[54102],"gssp":true,"appGip":true,"locale":"zh","locales":["zh","en"],"defaultLocale":"zh","scriptLoader":[]}</script></body></html>