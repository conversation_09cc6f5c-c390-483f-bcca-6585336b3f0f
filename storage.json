{"userDataProfilesMigration": true, "profileAssociations": {"workspaces": {"file:///Users/<USER>/Desktop/mediapipe-demo": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/course-management-select": "__default__profile__", "file:///Users/<USER>/Documents/notes": "__default__profile__", "file:///Users/<USER>/Documents/work/study/npmPlugin": "__default__profile__", "file:///Users/<USER>/Documents/work/block-health-scheme-edit": "__default__profile__", "file:///Users/<USER>/Documents/work/jdh-marketing-manage-web": "__default__profile__", "file:///Users/<USER>/Documents/work/nethp-static-front-b097157": "__default__profile__", "file:///Users/<USER>/Documents/work/healthjd-im-user": "__default__profile__", "file:///Users/<USER>/Desktop/node_modules": "__default__profile__", "file:///Users/<USER>/Documents/work/vuepress-starter": "__default__profile__", "file:///Users/<USER>/Documents/work/%E5%8E%86%E5%8F%B2%E9%A1%B9%E7%9B%AE/%E7%A7%BB%E5%8A%A8%E6%8A%80%E6%9C%AF%E7%A0%94%E5%8F%91%E9%A1%B9%E7%9B%AE/jdmp_ide": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis-cli/src/mPaaS-local-man": "__default__profile__", "file:///Users/<USER>/Documents/work/oasis/project/robot-man": "__default__profile__", "file:///Users/<USER>/Documents/work/robot-man-ui": "__default__profile__", "file:///Users/<USER>/Documents/work/oasis": "__default__profile__", "file:///Users/<USER>/Documents/work/%E5%8E%86%E5%8F%B2%E9%A1%B9%E7%9B%AE/wfdkc/www": "__default__profile__", "file:///Users/<USER>/Documents/work/aaa/robot-man-ui": "__default__profile__", "file:///Users/<USER>/Documents/work/robot": "__default__profile__", "file:///Users/<USER>/Documents/work/PowerJob-Console": "__default__profile__", "file:///Users/<USER>/Documents/work/jdh-medical-famdoc-saas-web": "__default__profile__", "file:///Users/<USER>/Documents/work/robot/PowerJob-Console": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/robots-lnspection-report": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/robot-detection-guidance-page": "__default__profile__", "file:///Users/<USER>/Documents/work/study/antv-x6": "__default__profile__", "file:///Users/<USER>/Documents/work/robot/ic-powerjob-ui": "__default__profile__", "file:///Users/<USER>/Documents/work/mpaas-editor": "__default__profile__", "file:///Users/<USER>/Documents/work/study/SourceDetector": "__default__profile__", "file:///Users/<USER>/Documents/work/study/htmlDemo": "__default__profile__", "file:///Users/<USER>/Documents/work/study/react-vite-template": "__default__profile__", "file:///Users/<USER>/Documents/work/jdh-innovation-comp": "__default__profile__", "file:///Users/<USER>/Documents/work/lcpCreatePage": "__default__profile__", "file:///Users/<USER>/Documents/work/study/antd-demo": "__default__profile__", "file:///Users/<USER>/Documents/work/pro-components": "__default__profile__", "file:///Users/<USER>/Documents/work/jdh-paas-cms": "__default__profile__", "file:///Users/<USER>/Documents/work/robot/ic-man-ui": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/zhangtao272-merge-master": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis-static-fe/deploy/configure": "__default__profile__", "file:///Users/<USER>/Documents/work/study/rtkq": "__default__profile__", "file:///Users/<USER>/Documents/work/study/rtk-app": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/cross-component-test2": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/cross-component-test3": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/test-zt-component": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/cross-component-test4": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/cross-component-test-h5": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/cross-component-test6": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa-taro-sdk": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa-doc": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/cross-component1": "__default__profile__", "file:///Users/<USER>/Documents/work/nest-standard-server": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/testNewH5Temp": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/cross-component-config": "__default__profile__", "file:///Users/<USER>/Documents/work": "__default__profile__", "file:///": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa-projects": "__default__profile__", "file:///Users/<USER>/Documents/work/nethp-static-front": "__default__profile__", "file:///Users/<USER>/Documents/work/robot/langflow": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/cross-test-component1": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/sk3o5VSPCJ-CuGk5XuQTe": "__default__profile__", "file:///Users/<USER>/Documents/work/robot/dify": "__default__profile__", "file:///Users/<USER>/Documents/work/listening": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/project-cross": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa": "__default__profile__", "file:///Users/<USER>/Documents/work/jnpm": "__default__profile__", "file:///Users/<USER>/Documents/work/ting": "__default__profile__", "file:///Users/<USER>/Documents/work/scale-h5-fe": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa-cli/src/mpaas-node-server": "__default__profile__", "file:///Users/<USER>/.nvm/versions/node/v16.20.2/lib": "__default__profile__", "file:///Users/<USER>/Documents/work/taro-component-template-aegis": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/cross-component-test1": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/cross-component2-test": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/test-component3333": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/cross-component2-test2": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/cross-zujian-test": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/laputa3-test-taro": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/crosss-ssssss": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/23232323": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/23232223232332": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/sk3f2fr4V9wUt2eyTNqRvsrc": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/sk3s8uSuJcKJMG-EDGcaM": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/sk3f2fr4V9wUt2eyTNqRv": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/taro-test-1": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/sk3zKza1DKpn4-KuGyYyZ": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/cross-temp2-test": "__default__profile__", "file:///Users/<USER>/Documents/work/hackathon24-ting": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis-cross-temp": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis-cross-project": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/cross3-com": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/newCrossCompon": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/traffic-area238": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/new-component3": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/traffic-area441": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/sdfsdfsdf222": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/cross-test2": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/traffic-sc05545": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/cross-test": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis-temp/componentTpl": "__default__profile__", "file:///Users/<USER>/Desktop/component": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/111111222222a": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/traffic-area456": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/laocal-tpl-test": "__default__profile__", "file:///Users/<USER>/Documents/work/skycity": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/aj-temp-ultra": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/traffic-field38": "__default__profile__", "file:///Users/<USER>/Desktop/component_%E5%89%AF%E6%9C%AC": "__default__profile__", "file:///Users/<USER>/Desktop/component-1": "__default__profile__", "file:///Users/<USER>/Documents/work/component-management": "__default__profile__", "file:///Users/<USER>/Documents/work/robot/robot-man-ui": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/traffic-area125": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/traffic-field33": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis/project": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/component-exam40912111313": "__default__profile__", "file:///Users/<USER>/Documents/work/aris-home": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis-static-fe": "__default__profile__", "file:///Users/<USER>/Documents/work/h5-builder": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/component-upgrade-try1636": "__default__profile__", "file:///Users/<USER>/Desktop/project": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/test0-Evaluation927163829": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/build-part-wechattest1413": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/traffic-space58": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/.components/component-upgrade-try1636": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis/components/goods-detail": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis-cli": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis/components/fun-emotion-cover": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/stable-hospital": "__default__profile__", "file:///Users/<USER>/Documents/work/jdh-design-taro": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/lilly-ltd161714": "__default__profile__", "file:///Users/<USER>/Documents/work/study/scripts": "__default__profile__", "file:///Users/<USER>/Documents/work/jdh-effect": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa-cli": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/test-new-component": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/skyCity3-Test01": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis-temp": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/sk3mKPRWq9gLOULCR9GAK": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/zhangtao-UnitTrial1144946": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/test33333": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/init-module20241011202614": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/overseas-doctor": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis-conf-sdk": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/doctorDetaiPage": "__default__profile__", "file:///Users/<USER>/Documents/work/nethp-source-react": "__default__profile__", "file:///Users/<USER>/Documents/work/sdk-core": "__default__profile__", "file:///Users/<USER>/Documents/work/ipaas-tools": "__default__profile__", "file:///Users/<USER>/Documents/work/jdh-lowcode": "__default__profile__", "file:///Users/<USER>/Documents/work/lowcode-engine": "__default__profile__", "file:///Users/<USER>/Desktop/component-ipaas": "__default__profile__", "file:///Users/<USER>/Documents/work/study/ipaasTest/434343434/components/pcCompTest": "__default__profile__", "file:///Users/<USER>/Documents/work/ipaas-tools/packages/templates/component": "__default__profile__", "file:///Users/<USER>/Desktop/blogs-master": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/pcCompTest": "__default__profile__", "file:///Users/<USER>/Documents/work/study/ipaasTest/434343434/components/ipaasModuleExam1028144502": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/ipaasModuleExam1028144502": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/zhangtaoCheck241023105801": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/componentSetupExam8153252": "__default__profile__", "file:///Users/<USER>/Documents/work/study/ipaasTest/laputa/components/ipaasModuleExam1028144502": "__default__profile__", "file:///Users/<USER>/Documents/work/study/ipaasTest/laputa/components/ipaas-test-component": "__default__profile__", "file:///Users/<USER>/Documents/work/study/ipaasTest/laputa/components/zhangtao-UnitTrial1144946": "__default__profile__", "file:///Users/<USER>/Documents/work/study/ipaasTest/laputa/components/mpaaszttestcomponent": "__default__profile__", "file:///Users/<USER>/Documents/work/ipaas-tools/packages/templates/application": "__default__profile__", "file:///Users/<USER>/Desktop/project-ipaas": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/care-card-fee": "__default__profile__", "file:///Users/<USER>/Documents/work/study/ipaasTest/laputa/crossProjects/messageCent1561": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa-preview": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/testCrossComponent": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/greenway-index-bgv35": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/ztComponentTest1": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/ztProjectTest1": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/convenience-find-drugv3109": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/project": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/H5ConponentTest": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/nethpman": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/zhangtao272-test2": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/test-h5-component": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/pcTestCom": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/pcComponentTestZt": "__default__profile__", "file:///Users/<USER>/Documents/work/allstar": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/ipaas-tools": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/task-common-integral-list": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/task-common-integral-list/.mpaas": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/task-create-target-cardv31011": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/task-create-target-card": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/vip-member-service-introduce": "__default__profile__", "file:///Users/<USER>/Desktop/%E5%BD%92%E6%A1%A3": "__default__profile__", "file:///Users/<USER>/Documents/work/study/taro": "__default__profile__", "file:///Users/<USER>/Documents/work/study/Taro-component": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects": "__default__profile__", "file:///Users/<USER>/Documents/work/study": "__default__profile__", "file:///Users/<USER>/Documents/work/study/aegis-temp": "__default__profile__", "file:///Users/<USER>/Documents/work/study/taro-project": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/.components/cognitive-behavioral-cour": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/testEmotion1szv": "__default__profile__", "file:///Users/<USER>/Documents/work/study/laputa": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/h5TestComponent": "__default__profile__", "file:///Users/<USER>/Documents/work/study/reportPreview": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa-cli/.tpl/componentTpl": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/crossComponent": "__default__profile__", "file:///Users/<USER>/Documents/work/study/LarkDownload": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa-cli/.tpl/projectTpl": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/mopen-prescription-listv3109": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/mopen-patient-detailv3106": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/my-medical-records": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/ztTestProject": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/healthRightsTPT": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/cetest": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/taskCenter": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/healthCheck": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/consult-after3": "__default__profile__", "file:///Users/<USER>/Documents/work/study/laputa2.0": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/consultStarV3": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/consult-after-test2": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/consult-after-test2-2": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/consult-after": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/nethpNewHome": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/xidingComponentTest": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis/components/jdheath-task-center": "__default__profile__", "file:///Users/<USER>/Desktop/laputa-tpl_project": "__default__profile__", "file:///Users/<USER>/Desktop/project3": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/nethpNewHome3": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/laputaTaroNethpBottom": "__default__profile__", "file:///Users/<USER>/Documents/work/study/testTest": "__default__profile__", "file:///Users/<USER>/Documents/work/study/sgmTest2": "__default__profile__", "file:///Users/<USER>/Desktop/project%20%281%29": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/ztCrossTest": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis-cli/src/mpaas-node-server": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/KKservice": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/testSgmInit": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/bargain-pullnew-atmospherev3105": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/aigc-summary": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/titleChangeTest": "__default__profile__", "file:///Users/<USER>/Documents/work/miniProgress/video-list": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/videoConent": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/videoConent": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/consult-qaCard": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/common-carousel": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/laputa-swiper-l3": "__default__profile__", "file:///Users/<USER>/Documents/work/miniProgress/Utopia": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/doctor-workbench": "__default__profile__", "file:///Users/<USER>/Documents/work/study/laputa/components/doctor-workbench": "__default__profile__", "file:///Users/<USER>/Documents/work/study/laputa/components/pageHeader": "__default__profile__", "file:///Users/<USER>/jdProject/miniprogram-3": "__default__profile__", "file:///Users/<USER>/jdProject/miniprogram-2": "__default__profile__", "file:///Users/<USER>/Documents/work/miniProgress/taro-sample-weapp": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/YemiPro": "__default__profile__", "file:///Users/<USER>/Documents/work/study/aegisTest/components/settled-step1-submit": "__default__profile__", "file:///Users/<USER>/Documents/work/study/hospital-compile-static-fe": "__default__profile__", "file:///Users/<USER>/Documents/work/study/deplotany": "__default__profile__", "file:///Users/<USER>/Desktop/project/build": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/dulibushuComTest": "__default__profile__", "file:///Users/<USER>/Documents/work/study/ipaasTest/dulibushuTest": "__default__profile__", "file:///Users/<USER>/Documents/work/iPaaS/jdh-lowcode": "__default__profile__", "file:///Users/<USER>/Documents/work/iPaaS/lowcode-engine": "__default__profile__", "file:///Users/<USER>/Documents/work/iPaaS/ipaas-tools": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/ipaasProjects/dulibushuIpaas": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/ipaasProjects/dulibushuTest": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/ipaasProjects/dulibushuOnlin": "__default__profile__", "file:///Users/<USER>/Desktop/build": "__default__profile__", "file:///Users/<USER>/Desktop/build%202": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis/components/yemi-jumping": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis/components": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis/components/.components": "__default__profile__", "file:///Users/<USER>/.config/yarn/global": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/springBeginning": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/crossCompileH5npm": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/taskList": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/familyCobuild": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/crossDeployTest": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/RNProjects/JDReactztRNtest": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/supportHttpTest": "__default__profile__", "file:///Users/<USER>/.nvm/versions/node/v18.20.4": "__default__profile__", "file:///Users/<USER>/Documents/work/study/xfyl-appointment": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/ipaasProjects/deployIndependT": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/ipaasProjects/yjtManTest": "__default__profile__", "file:///Users/<USER>/Documents/work/study/node-sass": "__default__profile__", "file:///Users/<USER>/.nvm/versions/node/v18.20.4/lib/node_modules": "__default__profile__", "file:///opt/homebrew": "__default__profile__", "file:///usr/bin": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/ipaasProjects": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/weappIncludeCross": "__default__profile__", "file:///Users/<USER>/Documents/work/pipeline/plugin-mpaas-rn-deploy": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/ipaasProjects/pcDulibushuTest": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/independentDeployTest": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/crossRnDeployTe": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/AIRobotInquiry": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis/components/settled-step1-submit": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/crossHttpTest": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/multiEndHttpPar": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/h5HttpParamsTest": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/rnProjects": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/test17013taro": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/compileConfig": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/doctor-my-idcardv31011": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/greenway-service-listv3101": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis/components/settled-supply-submit": "__default__profile__", "file:///Applications/Trae.app/Contents": "__default__profile__", "file:///Applications/%E4%BA%AC%E4%B8%9C%E5%BC%80%E5%8F%91%E8%80%85%E5%B7%A5%E5%85%B7.app/Contents": "__default__profile__", "file:///Applications/%E6%8A%96%E9%9F%B3%E5%BC%80%E5%8F%91%E8%80%85%E5%B7%A5%E5%85%B7.app/Contents": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis/components/inquiry-form": "__default__profile__", "file:///Users/<USER>/Documents/work/aegis/components/question-and-ask": "__default__profile__", "file:///Users/<USER>/Documents/work/study/ihub-floors": "__default__profile__", "file:///Users/<USER>/Documents/work/study/post-assistant": "__default__profile__", "file:///Users/<USER>/Documents/work/study/ihub-floors/ds-channel-feeds_100": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/components/h5TestParams": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/crossDeployT": "__default__profile__", "file:///Users/<USER>/Desktop/projectTpl": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/DHDHomePage": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/lt8506c7c7": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/lt38648e69": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/multiEndDeployT": "__default__profile__", "file:///Users/<USER>/Documents/work/laputa/crossProjects/lt40125b66": "__default__profile__", "file:///Users/<USER>/Documents/work/scripts": "__default__profile__", "file:///Users/<USER>/Documents/work/JoyCoder-IDE": "__default__profile__", "file:///Users/<USER>/Documents/work/JoyCoder-Plugin": "__default__profile__", "file:///Users/<USER>/Documents/work/study/my-extension": "__default__profile__", "file:///Users/<USER>/Desktop/devtool.asar": "__default__profile__", "file:///Users/<USER>/Documents/work/%E5%8E%86%E5%8F%B2%E9%A1%B9%E7%9B%AE/%E7%A7%BB%E5%8A%A8%E6%8A%80%E6%9C%AF%E7%A0%94%E5%8F%91%E9%A1%B9%E7%9B%AE/jdmp_ide/electron/dist/mac/%E4%BA%AC%E4%B8%9C%E5%BC%80%E5%8F%91%E8%80%85%E5%B7%A5%E5%85%B7.app/Contents": "__default__profile__", "file:///Users/<USER>/Documents/work/%E5%8E%86%E5%8F%B2%E9%A1%B9%E7%9B%AE/%E5%81%A5%E5%BA%B7/allstar": "__default__profile__", "file:///Users/<USER>/Documents/work/jdmp_ide": "__default__profile__", "file:///Users/<USER>/Documents/work/test/shell": "__default__profile__", "file:///Users/<USER>/Documents/work/%E5%8E%86%E5%8F%B2%E9%A1%B9%E7%9B%AE/%E5%81%A5%E5%BA%B7/laputa-cli": "__default__profile__", "file:///Users/<USER>/Documents/work/%E5%8E%86%E5%8F%B2%E9%A1%B9%E7%9B%AE/%E5%81%A5%E5%BA%B7/aegis-temp": "__default__profile__", "file:///Users/<USER>/Documents/work/pipeline_build_ide": "__default__profile__", "file:///Users/<USER>/Documents/work/docker": "__default__profile__", "file:///Users/<USER>/Desktop/Python-3.11.0": "__default__profile__", "file:///Applications/JoyCoder.app/Contents/Resources/app/node_modules.asar": "__default__profile__", "file:///Users/<USER>/Documents/work/%E5%8E%86%E5%8F%B2%E9%A1%B9%E7%9B%AE/%E5%81%A5%E5%BA%B7/iPaaS/jdh-lowcode": "__default__profile__", "file:///Users/<USER>/Documents/work/%E5%8E%86%E5%8F%B2%E9%A1%B9%E7%9B%AE/%E5%81%A5%E5%BA%B7/iPaaS/lowcode-engine": "__default__profile__", "file:///Users/<USER>/Documents/work/%E5%8E%86%E5%8F%B2%E9%A1%B9%E7%9B%AE/%E5%81%A5%E5%BA%B7/iPaaS/ipaas-tools": "__default__profile__", "file:///Users/<USER>/Documents/work/%E5%8E%86%E5%8F%B2%E9%A1%B9%E7%9B%AE/%E5%81%A5%E5%BA%B7/iPaaS": "__default__profile__", "file:///Users/<USER>/Documents/work/%E5%8E%86%E5%8F%B2%E9%A1%B9%E7%9B%AE/%E5%81%A5%E5%BA%B7": "__default__profile__", "file:///Applications/JoyCoder.app/Contents/Resources/app": "__default__profile__", "file:///Users/<USER>/Documents/work/test/JoyCoder-Plugin": "__default__profile__", "file:///Users/<USER>/Documents/work/test": "__default__profile__", "file:///Users/<USER>/Documents/work/jdhopenplatform-JoyCoder": "__default__profile__", "file:///Users/<USER>/Documents/work/joycoder-home": "__default__profile__", "file:///Users/<USER>/Documents/work/test/scripts": "__default__profile__", "file:///Users/<USER>/Desktop/aaa_64": "__default__profile__", "file:///Users/<USER>/Documents/work/demo/Roo-Code": "__default__profile__", "file:///Users/<USER>/Documents/work/test/agent-setting": "__default__profile__", "file:///Users/<USER>/Desktop/camunda-rule-engine-platform/backend": "__default__profile__", "file:///Users/<USER>/Desktop/camunda-rule-engine-platform": "__default__profile__", "file:///Applications/JoyCoder.app/Contents/Resources/app/extensions/joycoder.joycoder-editor-2.8.0": "__default__profile__", "file:///Users/<USER>/Library/Application%20Support/Code/User/globalStorage/joycoder.joycoder-fe": "__default__profile__", "file:///Users/<USER>/Documents/work/repos/vscode-copilot-release": "__default__profile__", "file:///Users/<USER>/.joycoder": "__default__profile__", "file:///Users/<USER>/Library/Application%20Support": "__default__profile__", "file:///Users/<USER>/Desktop/font_4926664_7s1o7er6d5j": "__default__profile__", "file:///Applications/Visual%20Studio%20Code.app/Contents": "__default__profile__", "file:///Users/<USER>/.vscode/extensions": "__default__profile__", "file:///Users/<USER>/Library/Application%20Support/Code/User": "__default__profile__", "file:///Users/<USER>/Documents/work/JoyCoder-Plugin/packages/vscode-IDE/joycoder-editor-2.9.19/extension": "__default__profile__", "file:///Users/<USER>/Desktop/themes": "__default__profile__", "file:///Users/<USER>/.joycode": "__default__profile__", "file:///Users/<USER>/Documents/work/test/jd-project": "__default__profile__", "file:///Users/<USER>/Documents/work/test/jd-Demo": "__default__profile__", "file:///Users/<USER>": "__default__profile__", "file:///Users/<USER>/Desktop/paula": "__default__profile__", "file:///Users/<USER>/Desktop/test": "__default__profile__", "file:///Applications/Lingma.app/Contents/Resources/app/extensions/Alibaba-Cloud.tongyi-lingma": "__default__profile__", "file:///Users/<USER>/Documents/work/repos": "__default__profile__", "file:///Users/<USER>/Documents/work/repos/vscode-copilot-chat": "__default__profile__", "file:///Users/<USER>/Tao": "__default__profile__", "file:///Users/<USER>/Library/Application%20Support/Code/User/globalStorage": "__default__profile__"}, "emptyWindows": {"1753085243226": "__default__profile__"}}, "profileAssociationsMigration": true, "telemetry.machineId": "7001c921c24350527445d25914293097f77f2da4fa459b8dfbfb17bcca24ba70", "backupWorkspaces": {"workspaces": [], "folders": [{"folderUri": "file:///Users/<USER>/Documents/work/JoyCoder-Plugin"}, {"folderUri": "file:///Users/<USER>/Tao"}, {"folderUri": "file:///Users/<USER>/Library/Application%20Support/Code/User/globalStorage"}], "emptyWindows": [{"backupFolder": "1753085243226"}]}, "windowControlHeight": 35, "lastKnownMenubarData": {"menus": {"File": {"items": [{"id": "workbench.action.files.newUntitledFile", "label": "新建文本文件(&&N)"}, {"id": "welcome.showNewFileEntries", "label": "新建文件..."}, {"id": "workbench.action.newWindow", "label": "新建窗口(&&W)"}, {"id": "submenuitem.OpenProfile", "label": "使用配置文件新建窗口", "submenu": {"items": [{"id": "workbench.profiles.actions.createProfile", "label": "新建配置文件..."}]}}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.files.openFileFolder", "label": "打开(&&O)..."}, {"id": "workbench.action.files.openFolder", "label": "打开文件夹(&&F)..."}, {"id": "workbench.action.openWorkspace", "label": "从文件打开工作区(&&K)..."}, {"id": "submenuitem.MenubarRecentMenu", "label": "打开最近的文件(&&R)", "submenu": {"items": [{"id": "workbench.action.reopenClosedEditor", "label": "重新打开已关闭的编辑器(&&R)"}, {"id": "vscode.menubar.separator"}, {"id": "openRecentFolder", "uri": {"$mid": 1, "path": "/Users/<USER>/Library/Application Support/Code/User/globalStorage", "scheme": "file"}, "enabled": true, "label": "~/Library/Application Support/Code/User/globalStorage"}, {"id": "openRecentFolder", "uri": {"$mid": 1, "path": "/Users/<USER>/Documents/work/JoyCoder-Plugin", "scheme": "file"}, "enabled": true, "label": "~/Documents/work/JoyCoder-Plugin"}, {"id": "openRecentFolder", "uri": {"$mid": 1, "path": "/Users/<USER>/Tao", "scheme": "file"}, "enabled": true, "label": "~/Tao"}, {"id": "openRecentFolder", "uri": {"$mid": 1, "path": "/Users/<USER>/Documents/work/JoyCoder-IDE", "scheme": "file"}, "enabled": true, "label": "~/Documents/work/JoyCoder-IDE"}, {"id": "openRecentFolder", "uri": {"$mid": 1, "path": "/Users/<USER>/Documents/work/pipeline_build_ide", "scheme": "file"}, "enabled": true, "label": "~/Documents/work/pipeline_build_ide"}, {"id": "openRecentFolder", "uri": {"$mid": 1, "path": "/Users/<USER>/Documents/work/repos/vscode-copilot-chat", "scheme": "file"}, "enabled": true, "label": "~/Documents/work/repos/vscode-copilot-chat"}, {"id": "openRecentFolder", "uri": {"$mid": 1, "path": "/Users/<USER>/Documents/work/repos", "scheme": "file"}, "enabled": true, "label": "~/Documents/work/repos"}, {"id": "openRecentFolder", "uri": {"$mid": 1, "path": "/Users/<USER>/Library/Application Support", "scheme": "file"}, "enabled": true, "label": "~/Library/Application Support"}, {"id": "openRecentFolder", "uri": {"$mid": 1, "path": "/Applications/Lingma.app/Contents/Resources/app/extensions/Alibaba-Cloud.tongyi-lingma", "scheme": "file"}, "enabled": true, "label": "/Applications/Lingma.app/Contents/Resources/app/extensions/Alibaba-Cloud.tongyi-lingma"}, {"id": "openRecentFolder", "uri": {"$mid": 1, "path": "/Users/<USER>/Desktop/paula", "scheme": "file"}, "enabled": true, "label": "~/Desktop/paula"}, {"id": "vscode.menubar.separator"}, {"id": "openRecentFile", "uri": {"$mid": 1, "path": "/Users/<USER>/Library/Application Support/com.jd.me/data/ee/zhangtao272/file/吴翠萍_2025-07-16-14-35-19.txt", "scheme": "file"}, "enabled": true, "label": "~/Library/Application Support/com.jd.me/data/ee/zhangtao272/file/吴翠萍_2025-07-16-14-35-19.txt"}, {"id": "openRecentFile", "uri": {"$mid": 1, "path": "/Applications/JoyCode.app/Contents/Resources/app/extensions/joycode.joycoder-editor/package.json", "scheme": "file"}, "enabled": true, "label": "/Applications/JoyCode.app/Contents/Resources/app/extensions/joycode.joycoder-editor/package.json"}, {"id": "openRecentFile", "uri": {"$mid": 1, "path": "/Users/<USER>/Library/Application Support/com.jd.me/data/ee/zhangtao272/file/ChatTextAreaAdaptor.tsx", "scheme": "file"}, "enabled": true, "label": "~/Library/Application Support/com.jd.me/data/ee/zhangtao272/file/ChatTextAreaAdaptor.tsx"}, {"id": "openRecentFile", "uri": {"$mid": 1, "path": "/Users/<USER>/Library/Application Support/com.jd.me/data/ee/zhangtao272/file/joycode-mcp.json", "scheme": "file"}, "enabled": true, "label": "~/Library/Application Support/com.jd.me/data/ee/zhangtao272/file/joycode-mcp.json"}, {"id": "openRecentFile", "uri": {"$mid": 1, "path": "/Users/<USER>/Desktop/fixMcp.js", "scheme": "file"}, "enabled": true, "label": "~/Desktop/fixMcp.js"}, {"id": "openRecentFile", "uri": {"$mid": 1, "path": "/Users/<USER>/Desktop/taro-overview.md", "scheme": "file"}, "enabled": true, "label": "~/Desktop/taro-overview.md"}, {"id": "openRecentFile", "uri": {"$mid": 1, "path": "/Users/<USER>/Desktop/AI代码插件核心能力建设.md", "scheme": "file"}, "enabled": true, "label": "~/Desktop/AI代码插件核心能力建设.md"}, {"id": "openRecentFile", "uri": {"$mid": 1, "path": "/Users/<USER>/Downloads/joycoder_task_jun-30-2025_12-00-59-am.md", "scheme": "file"}, "enabled": true, "label": "~/Downloads/joycoder_task_jun-30-2025_12-00-59-am.md"}, {"id": "openRecentFile", "uri": {"$mid": 1, "path": "/Applications/JoyCode.app/Contents/Resources/app/extensions/ai-resource/package.json", "scheme": "file"}, "enabled": true, "label": "/Applications/JoyCode.app/Contents/Resources/app/extensions/ai-resource/package.json"}, {"id": "openRecentFile", "uri": {"$mid": 1, "path": "/Users/<USER>/Desktop/博查搜索.md", "scheme": "file"}, "enabled": true, "label": "~/Desktop/博查搜索.md"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.openRecent", "label": "更多(&&M)..."}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.clearRecentFiles", "label": "清除最近打开的…(&&C)"}]}}, {"id": "vscode.menubar.separator"}, {"id": "addRootFolder", "label": "将文件夹添加到工作区(&&D)..."}, {"id": "workbench.action.saveWorkspaceAs", "label": "将工作区另存为..."}, {"id": "workbench.action.duplicateWorkspaceInNewWindow", "label": "复制工作区"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.files.save", "label": "保存(&&S)"}, {"id": "workbench.action.files.saveAs", "label": "另存为(&&A)..."}, {"id": "saveAll", "label": "全部保存(&&L)", "enabled": false}, {"id": "vscode.menubar.separator"}, {"id": "submenuitem.MenubarShare", "label": "共享", "submenu": {"items": [{"id": "workbench.profiles.actions.exportProfile", "label": "导出配置文件(默认)..."}]}}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.toggleAutoSave", "label": "自动保存(&&U)", "checked": true}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.files.revert", "label": "还原文件(&&V)"}, {"id": "workbench.action.closeActiveEditor", "label": "关闭编辑器(&&C)"}, {"id": "workbench.action.closeFolder", "label": "关闭文件夹(&&F)"}, {"id": "workbench.action.closeWindow", "label": "关闭窗口(&&E)"}]}, "Edit": {"items": [{"id": "undo", "label": "撤消(&&U)"}, {"id": "redo", "label": "恢复(&&R)"}, {"id": "vscode.menubar.separator"}, {"id": "editor.action.clipboardCutAction", "label": "剪切(&&T)"}, {"id": "editor.action.clipboardCopyAction", "label": "复制(&&C)"}, {"id": "editor.action.clipboardPasteAction", "label": "粘贴(&&P)"}, {"id": "vscode.menubar.separator"}, {"id": "actions.find", "label": "查找(&&F)"}, {"id": "editor.action.startFindReplaceAction", "label": "替换(&&R)"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.findInFiles", "label": "在文件中查找(&&I)"}, {"id": "workbench.action.replaceInFiles", "label": "在文件中替换"}, {"id": "vscode.menubar.separator"}, {"id": "editor.action.commentLine", "label": "切换行注释(&&T)"}, {"id": "editor.action.blockComment", "label": "切换块注释(&&B)"}, {"id": "editor.emmet.action.expandAbbreviation", "label": "Emmet: 展开缩写(&&X)"}]}, "Selection": {"items": [{"id": "editor.action.selectAll", "label": "全选(&&S)"}, {"id": "editor.action.smartSelect.expand", "label": "扩大选区(&&E)"}, {"id": "editor.action.smartSelect.shrink", "label": "缩小选区(&&S)"}, {"id": "vscode.menubar.separator"}, {"id": "editor.action.copyLinesUpAction", "label": "向上复制一行(&&C)"}, {"id": "editor.action.copyLinesDownAction", "label": "向下复制一行(&&P)"}, {"id": "editor.action.moveLinesUpAction", "label": "向上移动一行(&&V)"}, {"id": "editor.action.moveLinesDownAction", "label": "向下移动一行(&&L)"}, {"id": "editor.action.duplicateSelection", "label": "重复选择(&&D)"}, {"id": "vscode.menubar.separator"}, {"id": "editor.action.insertCursorAbove", "label": "在上面添加光标(&&A)"}, {"id": "editor.action.insertCursorBelow", "label": "在下面添加光标(&&D)"}, {"id": "editor.action.insertCursorAtEndOfEachLineSelected", "label": "在行尾添加光标(&&U)"}, {"id": "editor.action.addSelectionToNextFindMatch", "label": "添加下一个匹配项(&&N)"}, {"id": "editor.action.addSelectionToPreviousFindMatch", "label": "添加上一个匹配项(&&R)"}, {"id": "editor.action.selectHighlights", "label": "选择所有匹配项(&&O)"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.toggleMultiCursorModifier", "label": "切换为“Cmd+单击”进行多光标功能"}, {"id": "editor.action.toggleColumnSelection", "label": "列选择模式(&&S)"}]}, "View": {"items": [{"id": "workbench.action.showCommands", "label": "命令面板(&&C)…"}, {"id": "workbench.action.openView", "label": "打开视图(&&O)…"}, {"id": "vscode.menubar.separator"}, {"id": "submenuitem.MenubarAppearanceMenu", "label": "外观(&&A)", "submenu": {"items": [{"id": "workbench.action.toggleFullScreen", "label": "全屏(&&F)"}, {"id": "workbench.action.toggleZenMode", "label": "禅模式"}, {"id": "workbench.action.toggleCenteredLayout", "label": "居中布局(&&C)"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.toggleSidebarVisibility", "label": "主侧边栏(&&P)", "checked": true}, {"id": "workbench.action.toggleAuxiliaryBar", "label": "辅助(&&S)侧边栏", "checked": true}, {"id": "workbench.action.toggleStatusbarVisibility", "label": "状态栏(&&T)", "checked": true}, {"id": "workbench.action.togglePanel", "label": "面板(&&P)"}, {"id": "vscode.menubar.separator"}, {"id": "submenuitem.ActivityBarPositionMenu", "label": "活动栏位置", "submenu": {"items": [{"id": "workbench.action.activityBarLocation.default", "label": "默认(&&D)", "checked": true}, {"id": "workbench.action.activityBarLocation.top", "label": "顶部(&&T)"}, {"id": "workbench.action.activityBarLocation.bottom", "label": "底部(&&B)"}, {"id": "workbench.action.activityBarLocation.hide", "label": "隐藏(&&H)"}]}}, {"id": "workbench.action.toggleSidebarPosition", "label": "向右移动主侧栏(&&M)"}, {"id": "submenuitem.PanelPositionMenu", "label": "面板位置", "submenu": {"items": [{"id": "workbench.action.positionPanelTop", "label": "顶部"}, {"id": "workbench.action.positionPanelLeft", "label": "左"}, {"id": "workbench.action.positionPanelRight", "label": "右"}, {"id": "workbench.action.positionPanelBottom", "label": "底部", "checked": true}]}}, {"id": "submenuitem.PanelAlignmentMenu", "label": "对齐面板", "submenu": {"items": [{"id": "workbench.action.alignPanelCenter", "label": "居中", "checked": true}, {"id": "workbench.action.alignPanelJustify", "label": "两端对齐"}, {"id": "workbench.action.alignPanelRight", "label": "右"}, {"id": "workbench.action.alignPanelLeft", "label": "左"}]}}, {"id": "submenuitem.EditorTabsBarShowTabsSubmenu", "label": "选项卡栏", "submenu": {"items": [{"id": "workbench.action.showMultipleEditorTabs", "label": "多个选项卡", "checked": true}, {"id": "workbench.action.showEditorTab", "label": "单个选项卡"}, {"id": "workbench.action.hideEditorTabs", "label": "隐藏"}]}}, {"id": "submenuitem.EditorActionsPositionSubmenu", "label": "编辑器操作位置", "submenu": {"items": [{"id": "workbench.action.editorActionsDefault", "label": "选项卡栏", "checked": true}, {"id": "workbench.action.editorActionsTitleBar", "label": "标题栏"}, {"id": "workbench.action.hideEditorActions", "label": "隐藏"}]}}, {"id": "vscode.menubar.separator"}, {"id": "editor.action.toggleMinimap", "label": "缩略图(&&M)", "checked": true}, {"id": "breadcrumbs.toggle", "label": "痕迹导航(&&B)", "checked": true}, {"id": "editor.action.toggleStickyScroll", "label": "粘滞滚动(&&S)", "checked": true}, {"id": "editor.action.toggleRenderWhitespace", "label": "显示空格(&&R)", "checked": true}, {"id": "editor.action.toggleRenderControlCharacter", "label": "显示控制字符(&&C)", "checked": true}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.zoomIn", "label": "放大(&&Z)"}, {"id": "workbench.action.zoomOut", "label": "缩小(&&Z)"}, {"id": "workbench.action.zoomReset", "label": "重置缩放(&&R)"}]}}, {"id": "submenuitem.MenubarLayoutMenu", "label": "编辑器布局(&&L)", "submenu": {"items": [{"id": "workbench.action.splitEditorUp", "label": "向上拆分(&&U)"}, {"id": "workbench.action.splitEditorDown", "label": "向下拆分(&&D)"}, {"id": "workbench.action.splitEditorLeft", "label": "向左拆分(&&L)"}, {"id": "workbench.action.splitEditorRight", "label": "向右拆分(&&R)"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.moveEditorToNewWindow", "label": "将编辑器移动到新窗口(&&M)"}, {"id": "workbench.action.copyEditorToNewWindow", "label": "将编辑器复制到新窗口(&&C)"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.editorLayoutSingle", "label": "单列(&&S)"}, {"id": "workbench.action.editorLayoutTwoColumns", "label": "双列(&&T)"}, {"id": "workbench.action.editorLayoutThreeColumns", "label": "三列(&&H)"}, {"id": "workbench.action.editorLayoutTwoRows", "label": "双行(&&W)"}, {"id": "workbench.action.editorLayoutThreeRows", "label": "三行(&&R)"}, {"id": "workbench.action.editorLayoutTwoByTwoGrid", "label": "2x2 网格(&&G)"}, {"id": "workbench.action.editorLayoutTwoRowsRight", "label": "右侧双行(&&O)"}, {"id": "workbench.action.editorLayoutTwoColumnsBottom", "label": "底部双列(&&C)"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.toggleEditorGroupLayout", "label": "翻转布局(&&L)"}]}}, {"id": "vscode.menubar.separator"}, {"id": "workbench.view.explorer", "label": "资源管理器(&&E)"}, {"id": "workbench.view.search", "label": "搜索(&&S)"}, {"id": "workbench.view.scm", "label": "源代码管理(&&C)"}, {"id": "workbench.view.debug", "label": "运行(&&R)"}, {"id": "workbench.view.extensions", "label": "扩展(&&X)"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.panel.chat", "label": "聊天(&&C)"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.actions.view.problems", "label": "问题(&&P)"}, {"id": "workbench.action.output.toggleOutput", "label": "输出(&&O)"}, {"id": "workbench.debug.action.toggleRepl", "label": "调试控制台(&&B)"}, {"id": "workbench.action.terminal.toggleTerminal", "label": "终端(&&T)"}, {"id": "vscode.menubar.separator"}, {"id": "editor.action.toggleWordWrap", "label": "自动换行(&&W)", "enabled": false}]}, "Go": {"items": [{"id": "workbench.action.navigateBack", "label": "返回(&&B)", "enabled": false}, {"id": "workbench.action.navigateForward", "label": "前进(&&F)", "enabled": false}, {"id": "workbench.action.navigateToLastEditLocation", "label": "上次编辑位置(&&L)", "enabled": false}, {"id": "vscode.menubar.separator"}, {"id": "submenuitem.MenubarSwitchEditorMenu", "label": "切换编辑器(&&E)", "submenu": {"items": [{"id": "workbench.action.nextEditor", "label": "下一个编辑器(&&N)"}, {"id": "workbench.action.previousEditor", "label": "上一个编辑器(&&P)"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.openNextRecentlyUsedEditor", "label": "下一个使用过的编辑器(&&N)"}, {"id": "workbench.action.openPreviousRecentlyUsedEditor", "label": "上一个使用过的编辑器(&&P)"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.nextEditorInGroup", "label": "组中的下一个编辑器(&&N)"}, {"id": "workbench.action.previousEditorInGroup", "label": "组中的上一个编辑器(&&P)"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.openNextRecentlyUsedEditorInGroup", "label": "组中下一个使用过的编辑器(&&N)"}, {"id": "workbench.action.openPreviousRecentlyUsedEditorInGroup", "label": "组中上一个使用过的编辑器(&&P)"}]}}, {"id": "submenuitem.MenubarSwitchGroupMenu", "label": "切换组(&&G)", "submenu": {"items": [{"id": "workbench.action.focusFirstEditorGroup", "label": "第 1 组(&&1)"}, {"id": "workbench.action.focusSecondEditorGroup", "label": "第 2 组(&&2)"}, {"id": "workbench.action.focusThirdEditorGroup", "label": "第 3 组(&&3)", "enabled": false}, {"id": "workbench.action.focusFourthEditorGroup", "label": "第 4 组(&&4)", "enabled": false}, {"id": "workbench.action.focusFifthEditorGroup", "label": "第 5 组(&&5)", "enabled": false}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.focusNextGroup", "label": "下一个组(&&N)", "enabled": false}, {"id": "workbench.action.focusPreviousGroup", "label": "上一个组(&&P)", "enabled": false}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.focusLeftGroup", "label": "左侧组(&&L)", "enabled": false}, {"id": "workbench.action.focusRightGroup", "label": "右侧组(&&R)", "enabled": false}, {"id": "workbench.action.focusAboveGroup", "label": "上方组(&&A)", "enabled": false}, {"id": "workbench.action.focusBelowGroup", "label": "下方组(&&B)", "enabled": false}]}}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.quickOpen", "label": "转到文件(&&F)..."}, {"id": "workbench.action.showAllSymbols", "label": "转到工作区中的符号(&&W)…"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.gotoSymbol", "label": "转到编辑器中的符号(&&S)…"}, {"id": "editor.action.revealDefinition", "label": "转到定义(&&D)"}, {"id": "editor.action.revealDeclaration", "label": "转到声明(&&D)"}, {"id": "editor.action.goToTypeDefinition", "label": "转到类型定义(&&T)"}, {"id": "editor.action.goToImplementation", "label": "转到实现(&&I)"}, {"id": "editor.action.goToReferences", "label": "转到引用(&&R)"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.gotoLine", "label": "转到行/列(&&L)…"}, {"id": "editor.action.jumpToBracket", "label": "转到括号(&&B)"}, {"id": "vscode.menubar.separator"}, {"id": "editor.action.marker.nextInFiles", "label": "下一个问题(&&P)"}, {"id": "editor.action.marker.prevInFiles", "label": "上一个问题(&&P)"}, {"id": "vscode.menubar.separator"}, {"id": "editor.action.dirtydiff.next", "label": "下一个更改(&&C)"}, {"id": "editor.action.dirtydiff.previous", "label": "上一个更改(&&C)"}]}, "Run": {"items": [{"id": "workbench.action.debug.start", "label": "启动调试(&&S)"}, {"id": "workbench.action.debug.run", "label": "以非调试模式运行(&&W)"}, {"id": "workbench.action.debug.stop", "label": "停止调试(&&S)", "enabled": false}, {"id": "workbench.action.debug.restart", "label": "重启调试(&&R)", "enabled": false}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.debug.configure", "label": "打开配置(&&C)", "enabled": false}, {"id": "debug.addConfiguration", "label": "添加配置(&&D)..."}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.debug.stepOver", "label": "逐过程(&&O)", "enabled": false}, {"id": "workbench.action.debug.stepInto", "label": "单步执行(&&I)", "enabled": false}, {"id": "workbench.action.debug.stepOut", "label": "单步停止(&&U)", "enabled": false}, {"id": "workbench.action.debug.continue", "label": "继续(&&C)", "enabled": false}, {"id": "vscode.menubar.separator"}, {"id": "editor.debug.action.toggleBreakpoint", "label": "切换断点(&&B)"}, {"id": "submenuitem.MenubarNewBreakpointMenu", "label": "新建断点(&&N)", "submenu": {"items": [{"id": "editor.debug.action.editBreakpoint", "label": "编辑断点(&&E)"}, {"id": "editor.debug.action.conditionalBreakpoint", "label": "条件断点(&&C)..."}, {"id": "editor.debug.action.toggleInlineBreakpoint", "label": "内联断点(&&O)"}, {"id": "workbench.debug.viewlet.action.addFunctionBreakpointAction", "label": "函数断点(&&F)..."}, {"id": "editor.debug.action.triggerByBreakpoint", "label": "触发的断点..."}, {"id": "editor.debug.action.addLogPoint", "label": "记录点(&&L)..."}]}}, {"id": "vscode.menubar.separator"}, {"id": "workbench.debug.viewlet.action.enableAllBreakpoints", "label": "启用所有断点(&&E)"}, {"id": "workbench.debug.viewlet.action.disableAllBreakpoints", "label": "禁用所有断点(&&L)"}, {"id": "workbench.debug.viewlet.action.removeAllBreakpoints", "label": "删除所有断点(&&A)"}, {"id": "vscode.menubar.separator"}, {"id": "debug.installAdditionalDebuggers", "label": "安装附加调试器(&&I)..."}]}, "Terminal": {"items": [{"id": "workbench.action.terminal.new", "label": "新建终端(&&N)"}, {"id": "workbench.action.terminal.split", "label": "拆分终端(&&S)"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.tasks.runTask", "label": "运行任务(&&R)…"}, {"id": "workbench.action.tasks.build", "label": "运行生成任务(&&B)…"}, {"id": "workbench.action.terminal.runActiveFile", "label": "运行活动文件(&&A)"}, {"id": "workbench.action.terminal.runSelectedText", "label": "运行所选文本(&&S)"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.tasks.showTasks", "label": "显示正在运行的任务(&&G)…", "enabled": false}, {"id": "workbench.action.tasks.restartTask", "label": "重启正在运行的任务(&&E)…", "enabled": false}, {"id": "workbench.action.tasks.terminate", "label": "终止任务(&&T)…", "enabled": false}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.tasks.configureTaskRunner", "label": "配置任务(&&C)…"}, {"id": "workbench.action.tasks.configureDefaultBuildTask", "label": "配置默认生成任务(&&F)…"}]}, "Help": {"items": [{"id": "workbench.action.openWalkthrough", "label": "欢迎"}, {"id": "workbench.action.showCommands", "label": "显示所有命令"}, {"id": "workbench.action.showInteractivePlayground", "label": "编辑器操场(&&N)"}, {"id": "workbench.action.openDocumentationUrl", "label": "文档(&&D)"}, {"id": "welcome.showAllWalkthroughs", "label": "打开演练..."}, {"id": "update.showCurrentReleaseNotes", "label": "显示发行说明(&&R)"}, {"id": "workbench.action.getStartedWithAccessibilityFeatures", "label": "辅助功能入门"}, {"id": "workbench.action.askVScode", "label": "询问 @vscode"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.keybindingsReference", "label": "键盘快捷方式参考(&&K)"}, {"id": "workbench.action.openVideoTutorialsUrl", "label": "视频教程(&&V)"}, {"id": "workbench.action.openTipsAndTricksUrl", "label": "贴士和技巧(&&C)"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.openYouTubeUrl", "label": "在 YouTube 上加入我们(&&J)"}, {"id": "workbench.action.openRequestFeatureUrl", "label": "搜索功能请求(&&S)"}, {"id": "workbench.action.openIssueReporter", "label": "使用英文报告问题(&&I)"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.openLicenseUrl", "label": "查看许可证(&&V)"}, {"id": "workbench.action.openPrivacyStatementUrl", "label": "隐私声明(&&Y)"}, {"id": "vscode.menubar.separator"}, {"id": "workbench.action.toggleDevTools", "label": "切换开发人员工具"}, {"id": "workbench.action.openProcessExplorer", "label": "打开进程资源管理器(&&P)"}]}, "Preferences": {"items": [{"id": "workbench.profiles.actions.manageProfiles", "label": "配置文件(&&P)"}, {"id": "workbench.action.openSettings", "label": "设置(&&S)"}, {"id": "workbench.view.extensions", "label": "扩展(&&E)"}, {"id": "workbench.action.openGlobalKeybindings", "label": "键盘快捷方式"}, {"id": "workbench.action.openSnippets", "label": "配置代码片段"}, {"id": "workbench.action.tasks.openUserTasks", "label": "任务"}, {"id": "submenuitem.ThemesSubMenu", "label": "主题(&&T)", "submenu": {"items": [{"id": "workbench.action.selectTheme", "label": "颜色主题"}, {"id": "workbench.action.selectIconTheme", "label": "文件图标主题"}, {"id": "workbench.action.selectProductIconTheme", "label": "产品图标主题"}]}}, {"id": "vscode.menubar.separator"}, {"id": "workbench.userDataSync.actions.manage", "label": "设置同步已打开", "checked": true}, {"id": "vscode.menubar.separator"}, {"id": "settings.filterByOnline", "label": "联机服务设置(&&O)"}]}}, "keybindings": {"workbench.action.quit": {"label": "Cmd+Q", "userSettingsLabel": "cmd+q"}, "workbench.action.files.newUntitledFile": {"label": "Cmd+N", "userSettingsLabel": "cmd+n"}, "welcome.showNewFileEntries": {"label": "Ctrl+Alt+Cmd+N", "userSettingsLabel": "ctrl+alt+cmd+n"}, "workbench.action.newWindow": {"label": "Shift+Cmd+N", "userSettingsLabel": "shift+cmd+n"}, "workbench.action.files.openFileFolder": {"label": "Cmd+O", "userSettingsLabel": "cmd+o"}, "workbench.action.reopenClosedEditor": {"label": "Shift+Cmd+T", "userSettingsLabel": "shift+cmd+t"}, "workbench.action.openRecent": {"label": "Ctrl+R", "userSettingsLabel": "ctrl+r"}, "workbench.action.files.save": {"label": "Cmd+S", "userSettingsLabel": "cmd+s"}, "workbench.action.files.saveAs": {"label": "Shift+Cmd+S", "userSettingsLabel": "shift+cmd+s"}, "saveAll": {"label": "Alt+Cmd+S", "userSettingsLabel": "alt+cmd+s"}, "workbench.action.closeActiveEditor": {"label": "Cmd+W", "userSettingsLabel": "cmd+w"}, "workbench.action.closeFolder": {"label": "⌘K F", "isNative": false, "userSettingsLabel": "cmd+k f"}, "workbench.action.closeWindow": {"label": "Shift+Cmd+W", "userSettingsLabel": "shift+cmd+w"}, "undo": {"label": "Cmd+Z", "userSettingsLabel": "cmd+z"}, "redo": {"label": "Shift+Cmd+Z", "userSettingsLabel": "shift+cmd+z"}, "editor.action.clipboardCutAction": {"label": "Cmd+X", "userSettingsLabel": "cmd+x"}, "editor.action.clipboardCopyAction": {"label": "Cmd+C", "userSettingsLabel": "cmd+c"}, "editor.action.clipboardPasteAction": {"label": "Cmd+V", "userSettingsLabel": "cmd+v"}, "actions.find": {"label": "Cmd+F", "userSettingsLabel": "cmd+f"}, "editor.action.startFindReplaceAction": {"label": "Alt+Cmd+F", "userSettingsLabel": "alt+cmd+f"}, "workbench.action.findInFiles": {"label": "Shift+Cmd+F", "userSettingsLabel": "shift+cmd+f"}, "workbench.action.replaceInFiles": {"label": "Shift+Cmd+H", "userSettingsLabel": "shift+cmd+h"}, "editor.action.commentLine": {"label": "Cmd+/", "userSettingsLabel": "cmd+/"}, "editor.action.blockComment": {"label": "Shift+Alt+A", "userSettingsLabel": "shift+alt+a"}, "editor.emmet.action.expandAbbreviation": {"label": "Tab", "userSettingsLabel": "tab"}, "editor.action.selectAll": {"label": "Cmd+A", "userSettingsLabel": "cmd+a"}, "editor.action.smartSelect.expand": {"label": "Ctrl+Shift+Cmd+Right", "userSettingsLabel": "ctrl+shift+cmd+right"}, "editor.action.smartSelect.shrink": {"label": "Ctrl+Shift+Cmd+Left", "userSettingsLabel": "ctrl+shift+cmd+left"}, "editor.action.copyLinesUpAction": {"label": "Shift+Alt+Up", "userSettingsLabel": "shift+alt+up"}, "editor.action.copyLinesDownAction": {"label": "Shift+Alt+Down", "userSettingsLabel": "shift+alt+down"}, "editor.action.moveLinesUpAction": {"label": "Alt+Up", "userSettingsLabel": "alt+up"}, "editor.action.moveLinesDownAction": {"label": "Alt+Down", "userSettingsLabel": "alt+down"}, "editor.action.insertCursorAbove": {"label": "Alt+Cmd+Up", "userSettingsLabel": "alt+cmd+up"}, "editor.action.insertCursorBelow": {"label": "Alt+Cmd+Down", "userSettingsLabel": "alt+cmd+down"}, "editor.action.addSelectionToNextFindMatch": {"label": "Cmd+D", "userSettingsLabel": "cmd+d"}, "editor.action.selectHighlights": {"label": "Shift+Cmd+L", "userSettingsLabel": "shift+cmd+l"}, "workbench.action.showCommands": {"label": "Shift+Cmd+P", "userSettingsLabel": "shift+cmd+p"}, "workbench.action.toggleFullScreen": {"label": "Ctrl+Cmd+F", "userSettingsLabel": "ctrl+cmd+f"}, "workbench.action.toggleZenMode": {"label": "⌘K Z", "isNative": false, "userSettingsLabel": "cmd+k z"}, "workbench.action.toggleSidebarVisibility": {"label": "Cmd+B", "userSettingsLabel": "cmd+b"}, "workbench.action.toggleAuxiliaryBar": {"label": "Alt+Cmd+B", "userSettingsLabel": "alt+cmd+b"}, "workbench.action.togglePanel": {"label": "Cmd+J", "userSettingsLabel": "cmd+j"}, "workbench.action.zoomIn": {"label": "Cmd+=", "userSettingsLabel": "cmd+="}, "workbench.action.zoomOut": {"label": "Cmd+-", "userSettingsLabel": "cmd+-"}, "workbench.action.zoomReset": {"label": "⌘NumPad0", "isNative": false, "userSettingsLabel": "cmd+numpad0"}, "workbench.action.splitEditorUp": {"label": "⌘K ⌘\\", "isNative": false, "userSettingsLabel": "cmd+k cmd+\\"}, "workbench.action.copyEditorToNewWindow": {"label": "⌘K O", "isNative": false, "userSettingsLabel": "cmd+k o"}, "workbench.action.toggleEditorGroupLayout": {"label": "Alt+Cmd+0", "userSettingsLabel": "alt+cmd+0"}, "workbench.view.explorer": {"label": "Shift+Cmd+E", "userSettingsLabel": "shift+cmd+e"}, "workbench.view.search": {"label": "Shift+Cmd+F", "userSettingsLabel": "shift+cmd+f"}, "workbench.view.scm": {"label": "Ctrl+Shift+G", "userSettingsLabel": "ctrl+shift+g"}, "workbench.view.debug": {"label": "Shift+Cmd+D", "userSettingsLabel": "shift+cmd+d"}, "workbench.view.extensions": {"label": "Shift+Cmd+X", "userSettingsLabel": "shift+cmd+x"}, "workbench.actions.view.problems": {"label": "Shift+Cmd+M", "userSettingsLabel": "shift+cmd+m"}, "workbench.debug.action.toggleRepl": {"label": "Shift+Cmd+Y", "userSettingsLabel": "shift+cmd+y"}, "workbench.action.terminal.toggleTerminal": {"label": "Ctrl+`", "userSettingsLabel": "ctrl+`"}, "editor.action.toggleWordWrap": {"label": "Alt+Z", "userSettingsLabel": "alt+z"}, "workbench.action.navigateBack": {"label": "Ctrl+-", "userSettingsLabel": "ctrl+-"}, "workbench.action.navigateForward": {"label": "Ctrl+Shift+-", "userSettingsLabel": "ctrl+shift+-"}, "workbench.action.navigateToLastEditLocation": {"label": "⌘K ⌘Q", "isNative": false, "userSettingsLabel": "cmd+k cmd+q"}, "workbench.action.nextEditor": {"label": "Alt+Cmd+Right", "userSettingsLabel": "alt+cmd+right"}, "workbench.action.previousEditor": {"label": "Alt+Cmd+Left", "userSettingsLabel": "alt+cmd+left"}, "workbench.action.nextEditorInGroup": {"label": "⌘K ⌥⌘→", "isNative": false, "userSettingsLabel": "cmd+k alt+cmd+right"}, "workbench.action.previousEditorInGroup": {"label": "⌘K ⌥⌘←", "isNative": false, "userSettingsLabel": "cmd+k alt+cmd+left"}, "workbench.action.focusFirstEditorGroup": {"label": "Cmd+1", "userSettingsLabel": "cmd+1"}, "workbench.action.focusSecondEditorGroup": {"label": "Cmd+2", "userSettingsLabel": "cmd+2"}, "workbench.action.focusThirdEditorGroup": {"label": "Cmd+3", "userSettingsLabel": "cmd+3"}, "workbench.action.focusFourthEditorGroup": {"label": "Cmd+4", "userSettingsLabel": "cmd+4"}, "workbench.action.focusFifthEditorGroup": {"label": "Cmd+5", "userSettingsLabel": "cmd+5"}, "workbench.action.focusLeftGroup": {"label": "⌘K ⌘←", "isNative": false, "userSettingsLabel": "cmd+k cmd+left"}, "workbench.action.focusRightGroup": {"label": "⌘K ⌘→", "isNative": false, "userSettingsLabel": "cmd+k cmd+right"}, "workbench.action.focusAboveGroup": {"label": "⌘K ⌘↑", "isNative": false, "userSettingsLabel": "cmd+k cmd+up"}, "workbench.action.focusBelowGroup": {"label": "⌘K ⌘↓", "isNative": false, "userSettingsLabel": "cmd+k cmd+down"}, "workbench.action.quickOpen": {"label": "Cmd+P", "userSettingsLabel": "cmd+p"}, "workbench.action.showAllSymbols": {"label": "Cmd+T", "userSettingsLabel": "cmd+t"}, "workbench.action.gotoSymbol": {"label": "Shift+Cmd+O", "userSettingsLabel": "shift+cmd+o"}, "editor.action.revealDefinition": {"label": "F12", "userSettingsLabel": "f12"}, "editor.action.goToImplementation": {"label": "Cmd+F12", "userSettingsLabel": "cmd+f12"}, "editor.action.goToReferences": {"label": "Shift+F12", "userSettingsLabel": "shift+f12"}, "workbench.action.gotoLine": {"label": "Ctrl+G", "userSettingsLabel": "ctrl+g"}, "editor.action.jumpToBracket": {"label": "Shift+Cmd+\\", "userSettingsLabel": "shift+cmd+\\"}, "editor.action.marker.nextInFiles": {"label": "F8", "userSettingsLabel": "f8"}, "editor.action.marker.prevInFiles": {"label": "Shift+F8", "userSettingsLabel": "shift+f8"}, "editor.action.dirtydiff.next": {"label": "Alt+F3", "userSettingsLabel": "alt+f3"}, "editor.action.dirtydiff.previous": {"label": "Shift+Alt+F3", "userSettingsLabel": "shift+alt+f3"}, "workbench.action.debug.start": {"label": "F5", "userSettingsLabel": "f5"}, "workbench.action.debug.run": {"label": "Ctrl+F5", "userSettingsLabel": "ctrl+f5"}, "workbench.action.debug.stop": {"label": "Shift+F5", "userSettingsLabel": "shift+f5"}, "workbench.action.debug.restart": {"label": "Shift+Cmd+F5", "userSettingsLabel": "shift+cmd+f5"}, "workbench.action.debug.stepOver": {"label": "F10", "userSettingsLabel": "f10"}, "workbench.action.debug.stepInto": {"label": "F11", "userSettingsLabel": "f11"}, "workbench.action.debug.stepOut": {"label": "Shift+F11", "userSettingsLabel": "shift+f11"}, "workbench.action.debug.continue": {"label": "F5", "userSettingsLabel": "f5"}, "editor.debug.action.toggleBreakpoint": {"label": "F9", "userSettingsLabel": "f9"}, "editor.debug.action.toggleInlineBreakpoint": {"label": "Shift+F9", "userSettingsLabel": "shift+f9"}, "workbench.action.terminal.new": {"label": "Ctrl+Shift+`", "userSettingsLabel": "ctrl+shift+`"}, "workbench.action.terminal.split": {"label": "Cmd+\\", "userSettingsLabel": "cmd+\\"}, "workbench.action.tasks.build": {"label": "Shift+Cmd+B", "userSettingsLabel": "shift+cmd+b"}, "workbench.action.keybindingsReference": {"label": "⌘K ⌘R", "isNative": false, "userSettingsLabel": "cmd+k cmd+r"}, "workbench.action.openSettings": {"label": "Cmd+,", "userSettingsLabel": "cmd+,"}, "workbench.action.openGlobalKeybindings": {"label": "⌘K ⌘S", "isNative": false, "userSettingsLabel": "cmd+k cmd+s"}, "workbench.action.selectTheme": {"label": "⌘K ⌘T", "isNative": false, "userSettingsLabel": "cmd+k cmd+t"}}}, "theme": "vs-dark", "themeBackground": "#1f1f1f", "windowSplash": {"zoomLevel": 0, "baseTheme": "vs-dark", "colorInfo": {"foreground": "#cccccc", "background": "#1f1f1f", "editorBackground": "#1f1f1f", "titleBarBackground": "#181818", "titleBarBorder": "#2b2b2b", "activityBarBackground": "#181818", "activityBarBorder": "#2b2b2b", "sideBarBackground": "#181818", "sideBarBorder": "#2b2b2b", "statusBarBackground": "#181818", "statusBarBorder": "#2b2b2b", "statusBarNoFolderBackground": "#1f1f1f"}, "layoutInfo": {"sideBarSide": "left", "editorPartMinWidth": 220, "titleBarHeight": 35, "activityBarWidth": 48, "sideBarWidth": 318, "auxiliaryBarWidth": 407, "statusBarHeight": 22, "windowBorder": false}}, "windowsState": {"lastActiveWindow": {"folder": "file:///Users/<USER>/Library/Application%20Support/Code/User/globalStorage", "backupPath": "/Users/<USER>/Library/Application Support/Code/Backups/0153a15728a3be5f7b29db336cb74f9b", "uiState": {"mode": 1, "x": 57, "y": 44, "width": 1999, "height": 1285}}, "lastPluginDevelopmentHostWindow": {"folder": "file:///Users/<USER>/Desktop/aaa_64", "uiState": {"mode": 1, "x": 2056, "y": -1279, "width": 2560, "height": 1415}}, "openedWindows": [{"folder": "file:///Users/<USER>/Documents/work/JoyCoder-Plugin", "backupPath": "/Users/<USER>/Library/Application Support/Code/Backups/7418d5fdf152aa4fe9ded6a4e45e7344", "uiState": {"mode": 3, "display": 1, "width": 1999, "height": 1285, "x": 57, "y": 44}}, {"folder": "file:///Users/<USER>/Library/Application%20Support/Code/User/globalStorage", "backupPath": "/Users/<USER>/Library/Application Support/Code/Backups/0153a15728a3be5f7b29db336cb74f9b", "uiState": {"mode": 1, "x": 57, "y": 44, "width": 1999, "height": 1285}}]}, "telemetry.sqmId": "", "telemetry.devDeviceId": "ca9faafa-986d-4753-afe5-e24498cedf63", "windowSplashWorkspaceOverride": {"layoutInfo": {"auxiliarySideBarWidth": [526, ["84c17242e2e94617cc20a0e96f9c12fa", "66ac75db2b14fae5f2b82fc5923b675c", "50f2b5883353f352c3bc51847ce469cb", "ceaa008fe8722bd3cd924d6b6fbea5c3"]], "sideBarWidth": 318, "auxiliaryBarWidth": 407, "workspaces": {"d773dc26e6fb9b8ffc4722c02b0ff954": {"sideBarVisible": true, "auxiliaryBarVisible": false}, "00f515853e6a069c2144f8b5baf2a0c3": {"sideBarVisible": true, "auxiliaryBarVisible": false}, "ca77fba8edc943ee7e87b6ea909e8975": {"sideBarVisible": true, "auxiliaryBarVisible": false}, "fed08d896b797d7f8594dd29a42f8f91": {"sideBarVisible": true, "auxiliaryBarVisible": true}, "55378f695a06179d24be71c07897d071": {"sideBarVisible": true, "auxiliaryBarVisible": false}, "9077405709d78fec902a1e8c1ca0f203": {"sideBarVisible": true, "auxiliaryBarVisible": true}, "34c2f938f25e8d55a92fe4eb55667a25": {"sideBarVisible": true, "auxiliaryBarVisible": true}, "4d6fe83d838f15bacc48a6e7db494d5d": {"sideBarVisible": true, "auxiliaryBarVisible": false}, "fb7dc8664abd32cfc3f6757b84dc6581": {"sideBarVisible": false, "auxiliaryBarVisible": true}, "d926e25959bc24abc11c51b6ca7c892a": {"sideBarVisible": true, "auxiliaryBarVisible": true}, "ceaa008fe8722bd3cd924d6b6fbea5c3": {"sideBarVisible": true, "auxiliaryBarVisible": true}, "7728716b7cd87908f18b908de286da44": {"sideBarVisible": true, "auxiliaryBarVisible": true}, "f12b9d23756ed3cbf72676ba74f201c3": {"sideBarVisible": true, "auxiliaryBarVisible": true}, "c9c7f5aab050877b09fb1152d935be52": {"sideBarVisible": true, "auxiliaryBarVisible": false}, "96fa57136136dc1e6d3b605e6612bd9d": {"sideBarVisible": true, "auxiliaryBarVisible": true}, "c131535886ca41814647d9fa648e1a95": {"sideBarVisible": true, "auxiliaryBarVisible": true}, "f7bff13e1ac0e83858cc4f94d293675c": {"sideBarVisible": true, "auxiliaryBarVisible": false}, "0c84f156d5621a303f4fefa0fbd91d0a": {"sideBarVisible": true, "auxiliaryBarVisible": false}, "5160a1cd1c4dc8771b67c586d7696243": {"sideBarVisible": true, "auxiliaryBarVisible": true}, "ef9c95ab587ae10a07e1d56455ef46a7": {"sideBarVisible": true, "auxiliaryBarVisible": true}, "b249814af72f0569c6cecc1a6c36be86": {"sideBarVisible": true, "auxiliaryBarVisible": true}, "611fd435285a37ee08d23086057be4b9": {"sideBarVisible": true, "auxiliaryBarVisible": true}}}}, "issue.processExplorerWindowState": {"width": 800, "height": 500, "x": 375, "y": -970}}