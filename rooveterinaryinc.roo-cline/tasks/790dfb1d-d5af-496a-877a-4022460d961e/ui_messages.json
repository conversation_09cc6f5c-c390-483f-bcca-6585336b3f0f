[{"ts": 1750736938605, "type": "say", "say": "text", "text": "看一下www.baidu.com", "images": []}, {"ts": 1750736938620, "type": "say", "say": "api_req_started", "text": "{\"request\":\"<task>\\n看一下www.baidu.com\\n</task>\\n\\n<environment_details>\\n# VSCode Visible Files\\npackages/agent-setting/src/components/AgentManagement/index.tsx\\n\\n# VSCode Open Tabs\\npackages/agent-setting/src/components/AgentManagement/index.tsx\\n\\n# Current Time\\n2025/6/24 上午11:48:58 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n(Not available)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>code</slug>\\n<name>💻 Code</name>\\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\\n\\n\\n# Current Workspace Directory (/Users/<USER>/Documents/work/JoyCoder-Plugin) Files\\nassets/\\nassets/agent/\\nassets/agent/default-themes/\\nassets/codicon/\\nassets/d2c/\\nassets/dark/\\nassets/iconfont/\\nassets/iconfont/joycode-iconfont/\\nassets/light/\\nassets/sec/\\nassets/translation/\\nassets/treeView/\\nassets/wasm/\\ndoc/\\npackages/\\npackages/agent-common/\\npackages/agent-common/src/\\npackages/agent-common/src/demo/\\npackages/agent-common/src/tools/\\npackages/agent-common/src/utils/\\npackages/agent-common/src/vscode/\\npackages/agent-driven/\\npackages/agent-driven/src/\\npackages/agent-driven/src/__mocks__/\\npackages/agent-driven/src/adaptor/\\npackages/agent-driven/src/adaptor/api/\\npackages/agent-driven/src/adaptor/api/providers/\\npackages/agent-driven/src/adaptor/api/transform/\\npackages/agent-driven/src/adaptor/contextMenu/\\npackages/agent-driven/src/adaptor/translate/\\npackages/agent-driven/src/core/\\npackages/agent-driven/src/core/assistant-message/\\npackages/agent-driven/src/core/config/\\npackages/agent-driven/src/core/context-management/\\npackages/agent-driven/src/core/context-management/__tests__/\\npackages/agent-driven/src/core/context-tracking/\\npackages/agent-driven/src/core/diff/\\npackages/agent-driven/src/core/diff/strategies/\\npackages/agent-driven/src/core/diff/strategies/__tests__/\\npackages/agent-driven/src/core/ignore/\\npackages/agent-driven/src/core/JoyCoder/\\npackages/agent-driven/src/core/JoyCoder/tools/\\npackages/agent-driven/src/core/mentions/\\npackages/agent-driven/src/core/prompts/\\npackages/agent-driven/src/core/prompts/__tests__/\\npackages/agent-driven/src/core/prompts/instructions/\\npackages/agent-driven/src/core/prompts/sections/\\npackages/agent-driven/src/core/prompts/tools/\\npackages/agent-driven/src/core/slash-commands/\\npackages/agent-driven/src/core/sliding-window/\\npackages/agent-driven/src/core/storage/\\npackages/agent-driven/src/core/user-pormpt/\\npackages/agent-driven/src/core/webview/\\npackages/agent-driven/src/exports/\\npackages/agent-driven/src/integrations/\\npackages/agent-driven/src/integrations/checkpoints/\\npackages/agent-driven/src/integrations/debug/\\npackages/agent-driven/src/integrations/diagnostics/\\npackages/agent-driven/src/integrations/editor/\\npackages/agent-driven/src/integrations/misc/\\npackages/agent-driven/src/integrations/notifications/\\npackages/agent-driven/src/integrations/terminal/\\npackages/agent-driven/src/integrations/theme/\\npackages/agent-driven/src/integrations/theme/default-themes/\\npackages/agent-driven/src/integrations/workspace/\\npackages/agent-driven/src/schemas/\\npackages/agent-driven/src/services/\\npackages/agent-driven/src/services/auth/\\npackages/agent-driven/src/services/browser/\\npackages/agent-driven/src/services/glob/\\npackages/agent-driven/src/services/mcp/\\npackages/agent-driven/src/services/ripgrep/\\npackages/agent-driven/src/services/tree-sitter/\\npackages/agent-driven/src/services/tree-sitter/queries/\\npackages/agent-driven/src/shared/\\npackages/agent-driven/src/test/\\npackages/agent-driven/src/test/suite/\\npackages/agent-driven/src/test/webview/\\npackages/agent-driven/src/utils/\\npackages/agent-driven/web-agent/\\npackages/agent-driven/web-agent/public/\\npackages/agent-driven/web-agent/scripts/\\npackages/agent-driven/web-agent/src/\\npackages/agent-driven/web-agent/src/adaptor/\\npackages/agent-driven/web-agent/src/adaptor/components/\\npackages/agent-driven/web-agent/src/adaptor/locales/\\npackages/agent-driven/web-agent/src/adaptor/login/\\npackages/agent-driven/web-agent/src/components/\\npackages/agent-driven/web-agent/src/components/account/\\npackages/agent-driven/web-agent/src/components/browser/\\npackages/agent-driven/web-agent/src/components/chat/\\npackages/agent-driven/web-agent/src/components/chat/__tests__/\\npackages/agent-driven/web-agent/src/components/common/\\npackages/agent-driven/web-agent/src/components/history/\\npackages/agent-driven/web-agent/src/components/mcp/\\npackages/agent-driven/web-agent/src/components/prompts/\\npackages/agent-driven/web-agent/src/components/settings/\\npackages/agent-driven/web-agent/src/components/settings/__tests__/\\npackages/agent-driven/web-agent/src/components/welcome/\\npackages/agent-driven/web-agent/src/context/\\npackages/agent-driven/web-agent/src/types/\\npackages/agent-driven/web-agent/src/utils/\\npackages/agent-driven/web-agent/src/utils/__tests__/\\npackages/agent-runtime/\\npackages/agent-runtime/agent/\\npackages/agent-runtime/agent/bindings/\\npackages/agent-runtime/agent/bindings/kotlin/\\npackages/agent-runtime/agent/bindings/kotlin/gradle/\\npackages/agent-runtime/agent/bindings/kotlin/gradle/wrapper/\\npackages/agent-runtime/agent/bindings/kotlin/lib/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/com/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/com/sourcegraph/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/com/sourcegraph/cody/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/com/sourcegraph/cody/agent/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/com/sourcegraph/cody/agent/protocol_generated/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/test/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/test/kotlin/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/test/kotlin/org/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/test/kotlin/org/example/\\npackages/agent-runtime/agent/bindings/kotlin/src/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/kotlin/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/kotlin/com/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/kotlin/com/sourcegraph/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/kotlin/com/sourcegraph/cody/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/kotlin/com/sourcegraph/cody/protocol_generated/\\npackages/agent-runtime/agent/demo/\\npackages/agent-runtime/agent/recordings/\\npackages/agent-runtime/agent/recordings/auth_2503977039/\\npackages/agent-runtime/agent/recordings/autocomplete_2136217965/\\npackages/agent-runtime/agent/recordings/cody-chat_103640681/\\npackages/agent-runtime/agent/recordings/customCommandsClient_509552979/\\npackages/agent-runtime/agent/recordings/defaultClient_631904893/\\npackages/agent-runtime/agent/recordings/document-code_965949506/\\npackages/agent-runtime/agent/recordings/edit_1541920145/\\npackages/agent-runtime/agent/recordings/enterpriseClient_3965582033/\\npackages/agent-runtime/agent/recordings/enterpriseMainBranchClient_759014996/\\npackages/agent-runtime/agent/recordings/fix_3001320056/\\npackages/agent-runtime/agent/recordings/generate-unit-test_1969275822/\\npackages/agent-runtime/agent/recordings/rateLimitedClient_2043004676/\\npackages/agent-runtime/agent/recordings/unauthed_2245427793/\\npackages/agent-runtime/agent/recordings/vscode_525627603/\\npackages/agent-runtime/agent/scripts/\\npackages/agent-runtime/agent/src/\\npackages/agent-runtime/agent/src/__snapshots__/\\npackages/agent-runtime/agent/src/__tests__/\\npackages/agent-runtime/agent/src/__tests__/auth/\\npackages/agent-runtime/agent/src/__tests__/autocomplete/\\npackages/agent-runtime/agent/src/__tests__/autocomplete/src/\\npackages/agent-runtime/agent/src/__tests__/chat-response-quality/\\npackages/agent-runtime/agent/src/__tests__/chat-response-quality/grpc/\\npackages/agent-runtime/agent/src/__tests__/chat-response-quality/grpc/chunk/\\npackages/agent-runtime/agent/src/__tests__/chat-response-quality/shards/\\npackages/agent-runtime/agent/src/__tests__/cody-cli-chat/\\npackages/agent-runtime/agent/src/__tests__/custom-commands/\\npackages/agent-runtime/agent/src/__tests__/custom-commands/src/\\npackages/agent-runtime/agent/src/__tests__/document-code/\\npackages/agent-runtime/agent/src/__tests__/document-code/src/\\npackages/agent-runtime/agent/src/__tests__/edit-code/\\npackages/agent-runtime/agent/src/__tests__/edit-code/src/\\npackages/agent-runtime/agent/src/__tests__/example-ts/\\npackages/agent-runtime/agent/src/__tests__/example-ts/src/\\npackages/agent-runtime/agent/src/__tests__/fix/\\npackages/agent-runtime/agent/src/__tests__/fix/src/\\npackages/agent-runtime/agent/src/__tests__/generate-unit-test/\\npackages/agent-runtime/agent/src/__tests__/generate-unit-test/src/\\npackages/agent-runtime/agent/src/__tests__/graph-test/\\npackages/agent-runtime/agent/src/__tests__/graph-test/src/\\npackages/agent-runtime/agent/src/cli/\\npackages/agent-runtime/agent/src/cli/__snapshots__/\\npackages/agent-runtime/agent/src/cli/command-auth/\\npackages/agent-runtime/agent/src/cli/command-bench/\\npackages/agent-runtime/agent/src/cli/command-bench/__snapshots__/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/cpp/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/csharp/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/dart/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/go/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/java/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/javascript/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/php/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/python/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/tsx/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/typescript/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/zig/\\npackages/agent-runtime/agent/src/cli/command-context/\\npackages/agent-runtime/agent/src/cli/scip-codegen/\\npackages/agent-runtime/agent/src/cli/scip-codegen/emitters/\\npackages/agent-runtime/agent/src/global-state/\\npackages/agent-runtime/agent/src/global-state/migrations/\\npackages/agent-runtime/agent/src/local-e2e/\\npackages/agent-runtime/agent/src/lsp/\\npackages/agent-runtime/cli/\\npackages/agent-runtime/doc/\\npackages/agent-runtime/doc/dev/\\npackages/agent-runtime/doc/dev/quality/\\npackages/agent-runtime/lib/\\n\\n(File list truncated. Use list_files on specific subdirectories if you need to explore further.)\\n</environment_details>\",\"tokensIn\":15134,\"tokensOut\":120,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1750736942725, "type": "say", "say": "text", "text": "我需要使用browser_action工具来访问百度网站。让我们按以下步骤进行：\n1. 首先启动浏览器并访问www", "partial": true}, {"ts": 1750736943771, "type": "say", "say": "checkpoint_saved", "text": "470d7bd356e11189577a294522a5e187b31be0fd", "checkpoint": {"isFirst": true, "from": "470d7bd356e11189577a294522a5e187b31be0fd", "to": "470d7bd356e11189577a294522a5e187b31be0fd"}}, {"ts": 1750736943816, "type": "say", "say": "text", "text": "我需要使用browser_action工具来访问百度网站。让我们按以下步骤进行：\n1. 首先启动浏览器并访问www.baidu.com\n2. 查看页面内容\n3. 完成后关闭浏览器", "partial": false}, {"ts": 1750736944477, "type": "ask", "ask": "browser_action_launch", "text": "https://www.baidu.com", "partial": false, "isProtected": false}, {"ts": 1750736944885, "type": "say", "say": "browser_action_result", "text": ""}, {"ts": 1750736951387, "type": "say", "say": "browser_action_result", "text": "{\"screenshot\":\"data:image/webp;base64,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\",\"logs\":\"无畏是青春的态度，热AI是时代的脉搏。\\n各位新同学正以无畏的姿态奔赴通用人工智能的星辰大海！\\n作为引领AI时代浪潮的主力军，广阔舞台，待你大展身手。\\n乘风破浪，勇往直前，未来百度将与你一起，创造无限可能！\\n\\n%c百度2025校园招聘简历投递：https://talent.baidu.com/jobs/list color:red\\n[info] Noise was added to a canvas readback. If this has caused breakage, please file a bug at https://issues.chromium.org/issues/new?component=1456351&title=Canvas%20noise%20breakage. This feature can be disabled through chrome://flags/#enable-canvas-noise\\n[info] Noise was added to a canvas readback. If this has caused breakage, please file a bug at https://issues.chromium.org/issues/new?component=1456351&title=Canvas%20noise%20breakage. This feature can be disabled through chrome://flags/#enable-canvas-noise\",\"currentUrl\":\"https://www.baidu.com/\"}"}, {"ts": 1750736951419, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[browser_action for 'launch'] Result:\\n\\nThe browser action has been executed. The console logs and screenshot have been captured for your analysis.\\n\\nConsole logs:\\n无畏是青春的态度，热AI是时代的脉搏。\\n各位新同学正以无畏的姿态奔赴通用人工智能的星辰大海！\\n作为引领AI时代浪潮的主力军，广阔舞台，待你大展身手。\\n乘风破浪，勇往直前，未来百度将与你一起，创造无限可能！\\n\\n%c百度2025校园招聘简历投递：https://talent.baidu.com/jobs/list color:red\\n[info] Noise was added to a canvas readback. If this has caused breakage, please file a bug at https://issues.chromium.org/issues/new?component=1456351&title=Canvas%20noise%20breakage. This feature can be disabled through chrome://flags/#enable-canvas-noise\\n[info] Noise was added to a canvas readback. If this has caused breakage, please file a bug at https://issues.chromium.org/issues/new?component=1456351&title=Canvas%20noise%20breakage. This feature can be disabled through chrome://flags/#enable-canvas-noise\\n\\n(REMEMBER: if you need to proceed to using non-`browser_action` tools or launch a new browser, you MUST first close cline browser. For example, if after analyzing the logs and screenshot you need to edit a file, you must first close the browser before you can use the write_to_file tool.)\\n\\n[Image]\\n\\n<environment_details>\\n# VSCode Visible Files\\npackages/agent-setting/src/components/AgentManagement/index.tsx\\n\\n# VSCode Open Tabs\\npackages/agent-setting/src/components/AgentManagement/index.tsx\\n\\n# Current Time\\n2025/6/24 上午11:49:11 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n15,254 (12%)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>code</slug>\\n<name>💻 Code</name>\\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\\n</environment_details>\",\"tokensIn\":0,\"tokensOut\":0,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1750736956429, "type": "say", "say": "text", "text": "我看到了百度首页的内容和控制台日志。页面显示了以下主要内容：\n1. 顶部导航栏包含新闻、地图、贴吧等服务\n2. 中间是搜索框，显示支持 DeepSeek R1 最新版\n3. 下方是热搜榜单\n4. 控制台日志显示了百度2025校园招聘信息\n\n既然已经看到了主要内容，我应该关闭浏览器。", "partial": false}, {"ts": 1750736959565, "type": "say", "say": "browser_action", "text": "{\"action\":\"close\"}", "partial": false}, {"ts": 1750736959888, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[browser_action for 'close'] Result:\\n\\nThe browser has been closed. You may now proceed to using other tools.\\n\\nTool [attempt_completion] was not executed because a tool has already been used in this message. Only one tool may be used per message. You must assess the first tool's result before proceeding to use the next tool.\\n\\n<environment_details>\\n# VSCode Visible Files\\npackages/agent-setting/src/components/AgentManagement/index.tsx\\n\\n# VSCode Open Tabs\\npackages/agent-setting/src/components/AgentManagement/index.tsx\\n\\n# Current Time\\n2025/6/24 上午11:49:19 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n15,254 (12%)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>code</slug>\\n<name>💻 Code</name>\\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\\n</environment_details>\",\"tokensIn\":17015,\"tokensOut\":183,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1750736965277, "type": "say", "say": "text", "text": "", "partial": false}, {"ts": 1750736965343, "type": "say", "say": "completion_result", "text": "我已访问了百度首页(www.baidu.com)。页面显示了以下主要内容：\n\n1. 顶部导航栏提供新闻、地图、贴吧等多项服务\n2. 中央搜索框展示\"AI搜索已支持DeepSeek R1最新版\"\n3. 热搜榜单展示当前热门话题\n4. 页面还包含百度2025校园招聘信息\n\n页面保持了百度一贯简洁清晰的界面设计，功能完整且运行正常。", "partial": false}, {"ts": 1750736968246, "type": "ask", "ask": "completion_result", "text": ""}]