[{"ts": 1752118987964, "type": "say", "say": "text", "text": "@/catch 帮我分析日志，查看是否有内存泄露等问题，进程占用内存越来越高", "images": []}, {"ts": 1752118987975, "type": "say", "say": "api_req_started", "text": "{\"request\":\"<task>\\n'catch' (see below for file content) 帮我分析日志，查看是否有内存泄露等问题，进程占用内存越来越高\\n</task>\\n\\n<file_content path=\\\"catch\\\">\\n   1 | Analysis of sampling Code Helper (Plugin) (pid 50419) every 1 millisecond\\n   2 | Process:         Code Helper (Plugin) [50419]\\n   3 | Path:            /Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)\\n   4 | Load Address:    0x10097c000\\n   5 | Identifier:      Code Helper (Plugin)\\n   6 | Version:         ???\\n   7 | Code Type:       ARM64\\n   8 | Platform:        macOS\\n   9 | Parent Process:  Electron [5397]\\n  10 | Target Type:     live task\\n  11 | \\n  12 | Date/Time:       2025-07-10 11:40:52.959 +0800\\n  13 | Launch Time:     2025-07-07 15:22:49.214 +0800\\n  14 | OS Version:      macOS 15.5 (24F74)\\n  15 | Report Version:  7\\n  16 | Analysis Tool:   /usr/bin/sample\\n  17 | \\n  18 | Physical footprint:         4.1G\\n  19 | Physical footprint (peak):  4.4G\\n  20 | Idle exit:                  untracked\\n  21 | ----\\n  22 | \\n  23 | Call graph:\\n  24 |     2445 Thread_959804   DispatchQueue_1: com.apple.main-thread  (serial)\\n  25 |     + 2445 start  (in dyld) + 6076  [0x191c2eb98]\\n  26 |     +   2445 operator new[](unsigned long)  (in Code Helper (Plugin)) + 244  [0x10097dc90]\\n  27 |     +     2445 ElectronMain  (in Electron Framework) + 124  [0x117100874]\\n  28 |     +       2445 v8::Object::SetNativeDataProperty(v8::Local<v8::Context>, v8::Local<v8::Name>, void (*)(v8::Local<v8::Name>, v8::PropertyCallbackInfo<v8::Value> const&), void (*)(v8::Local<v8::Name>, v8::Local<v8::Value>, v8::PropertyCallbackInfo<void> const&), v8::Local<v8::Value>, v8::PropertyAttribute, v8::SideEffectType, v8::SideEffectType)  (in Electron Framework) + 537620  [0x11689df04]\\n  29 |     +         2445 v8::Object::SetNativeDataProperty(v8::Local<v8::Context>, v8::Local<v8::Name>, void (*)(v8::Local<v8::Name>, v8::PropertyCallbackInfo<v8::Value> const&), void (*)(v8::Local<v8::Name>, v8::Local<v8::Value>, v8::PropertyCallbackInfo<void> const&), v8::Local<v8::Value>, v8::PropertyAttribute, v8::SideEffectType, v8::SideEffectType)  (in Electron Framework) + 539376  [0x11689e5e0]\\n  30 |     +           2445 v8::Isolate::SetAddCrashKeyCallback(void (*)(v8::CrashKeyId, std::__Cr::basic_string<char, std::__Cr::char_traits<char>, std::__Cr::allocator<char>> const&))  (in Electron Framework) + 44252  [0x1165b7800]\\n  31 |     +             2445 v8::Isolate::SetAddCrashKeyCallback(void (*)(v8::CrashKeyId, std::__Cr::basic_string<char, std::__Cr::char_traits<char>, std::__Cr::allocator<char>> const&))  (in Electron Framework) + 46244  [0x1165b7fc8]\\n  32 |     +               2445 v8::Isolate::SetAddCrashKeyCallback(void (*)(v8::CrashKeyId, std::__Cr::basic_string<char, std::__Cr::char_traits<char>, std::__Cr::allocator<char>> const&))  (in Electron Framework) + 53720  [0x1165b9cfc]\\n  33 |     +                 2445 v8::V8::SetFlagsFromString(char const*, unsigned long)  (in Electron Framework) + 354248  [0x1164690c0]\\n  34 |     +                   2445 node::PrincipalRealm::http2session_on_frame_error_function() const  (in Electron Framework) + 121004  [0x11621f004]\\n  35 |     +                     2415 v8::ScriptCompiler::CompileModule(v8::Local<v8::Context>, v8::ScriptCompiler::StreamedSource*, v8::Local<v8::String>, v8::ScriptOrigin const&)  (in Electron Framework) + 75092  [0x115f44d18]\\n  36 |     +                     ! 2415 uv_get_osfhandle  (in Electron Framework) + 95888  [0x114fb8730]\\n  37 |     +                     !   2415 uv_get_osfhandle  (in Electron Framework) + 96416  [0x114fb8940]\\n  38 |     +                     !     2415 mach_msg  (in libsystem_kernel.dylib) + 24  [0x191f8cfa8]\\n  39 |     +                     !       2415 mach_msg_overwrite  (in libsystem_kernel.dylib) + 484  [0x191f95764]\\n  40 |     +                     !         2415 mach_msg2_internal  (in libsystem_kernel.dylib) + 76  [0x191f9f3a0]\\n  41 |     +                     !           2415 mach_msg2_trap  (in libsystem_kernel.dylib) + 8  [0x191f8cc34]\\n  42 |     +                     29 v8::ScriptCompiler::CompileModule(v8::Local<v8::Context>, v8::ScriptCompiler::StreamedSource*, v8::Local<v8::String>, v8::ScriptOrigin const&)  (in Electron Framework) + 74952  [0x115f44c8c]\\n  43 |     +                     ! 18 v8::ValueSerializer::Delegate::HasCustomHostObject(v8::Isolate*)  (in Electron Framework) + 3848  [0x114cdfe88]\\n  44 |     +                     ! : 17 v8::ValueSerializer::Delegate::HasCustomHostObject(v8::Isolate*)  (in Electron Framework) + 6300  [0x114ce081c]\\n  45 |     +                     ! : | 17 node::PrincipalRealm::maybe_cache_generated_source_map() const  (in Electron Framework) + 319516  [0x11728162c]\\n  46 |     +                     ! : |   10 uv_run  (in Electron Framework) + 600  [0x1170eea48]\\n  47 |     +                     ! : |   + 10 uv__run_timers  (in Electron Framework) + 144  [0x1170eb7d4]\\n  48 |     +                     ! : |   +   6 node::Environment::RunTimers(uv_timer_s*)  (in Electron Framework) + 368  [0x1173880a4]\\n  49 |     +                     ! : |   +   ! 6 v8::Function::Call(v8::Isolate*, v8::Local<v8::Context>, v8::Local<v8::Value>, int, v8::Local<v8::Value>*)  (in Electron Framework) + 360  [0x114edae04]\\n  50 |     +                     ! : |   +   !   6 v8_inspector::V8ContextInfo::executionContextId(v8::Local<v8::Context>)  (in Electron Framework) + 2092  [0x114edbe68]\\n  51 |     +                     ! : |   +   !     6 ???  (in <unknown binary>)  [0x157e0aed4]\\n  52 |     +                     ! : |   +   !       6 ???  (in <unknown binary>)  [0x157e0b288]\\n  53 |     +                     ! : |   +   !         6 ???  (in <unknown binary>)  [0x15125dff0]\\n  54 |     +                     ! : |   +   !           3 ???  (in <unknown binary>)  [0x153fcd6d8]\\n  55 |     +                     ! : |   +   !           : 3 ???  (in <unknown binary>)  [0x150f40834]\\n  56 |     +                     ! : |   +   !           :   3 ???  (in <unknown binary>)  [0x157e0f82c]\\n  57 |     +                     ! : |   +   !           :     3 v8::internal::ThreadIsolation::JitPageReference::AllocationContaining(unsigned long)  (in Electron Framework) + 6640  [0x114d3e790]\\n  58 |     +                     ! : |   +   !           :       3 v8::MicrotasksScope::~MicrotasksScope()  (in Electron Framework) + 192  [0x114d3eb08]\\n  59 |     +                     ! : |   +   !           :         3 v8::MicrotasksScope::~MicrotasksScope()  (in Electron Framework) + 288  [0x114d3eb68]\\n  60 |     +                     ! : |   +   !           :           3 v8::Function::NewInstanceWithSideEffectType(v8::Local<v8::Context>, int, v8::Local<v8::Value>*, v8::SideEffectType) const  (in Electron Framework) + 2188  [0x114d225f8]\\n  61 |     +                     ! : |   +   !           :             3 ???  (in <unknown binary>)  [0x157e0b154]\\n  62 |     +                     ! : |   +   !           :               2 ???  (in <unknown binary>)  [0x157e3cc2c]\\n  63 |     +                     ! : |   +   !           :               | 2 ???  (in <unknown binary>)  [0x157f28218]\\n  64 |     +                     ! : |   +   !           :               |   2 ???  (in <unknown binary>)  [0x157e4e308]\\n  65 |     +                     ! : |   +   !           :               |     1 ???  (in <unknown binary>)  [0x151734db8]\\n  66 |     +                     ! : |   +   !           :               |     + 1 ???  (in <unknown binary>)  [0x153ffdcb4]\\n  67 |     +                     ! : |   +   !           :               |     +   1 ???  (in <unknown binary>)  [0x157e5d4a4]\\n  68 |     +                     ! : |   +   !           :               |     1 ???  (in <unknown binary>)  [0x1517351ac]\\n  69 |     +                     ! : |   +   !           :               1 ???  (in <unknown binary>)  [0x157e3d11c]\\n  70 |     +                     ! : |   +   !           :                 1 ???  (in <unknown binary>)  [0x157e60ee8]\\n  71 |     +                     ! : |   +   !           1 ???  (in <unknown binary>)  [0x153fcdc48]\\n  72 |     +                     ! : |   +   !           : 1 ???  (in <unknown binary>)  [0x151734ec0]\\n  73 |     +                     ! : |   +   !           1 ???  (in <unknown binary>)  [0x153fcfc50]\\n  74 |     +                     ! : |   +   !           : 1 ???  (in <unknown binary>)  [0x157e007b0]\\n  75 |     +                     ! : |   +   !           1 ???  (in <unknown binary>)  [0x150f16ca0]\\n  76 |     +                     ! : |   +   2 node::Environment::RunTimers(uv_timer_s*)  (in Electron Framework) + 416  [0x1173880d4]\\n  77 |     +                     ! : |   +   ! 2 node::InternalCallbackScope::~InternalCallbackScope()  (in Electron Framework) + 20  [0x11731d6b8]\\n  78 |     +                     ! : |   +   !   1 node::InternalCallbackScope::Close()  (in Electron Framework) + 156  [0x11731d9e4]\\n  79 |     +                     ! : |   +   !   : 1 node::AsyncHooks::pop_async_context(double)  (in Electron Framework) + 524  [0x117360f6c]\\n  80 |     +                     ! : |   +   !   :   1 v8::Object::Set(v8::Local<v8::Context>, v8::Local<v8::Value>, v8::Local<v8::Value>)  (in Electron Framework) + 276  [0x115e577b4]\\n  81 |     +                     ! : |   +   !   :     1 v8::internal::ThreadIsolation::LookupJitPage(unsigned long, unsigned long)  (in Electron Framework) + 59816  [0x114fe4ef4]\\n  82 |     +                     ! : |   +   !   :       1 v8::internal::ThreadIsolation::LookupJitPage(unsigned long, unsigned long)  (in Electron Framework) + 61540  [0x114fe55b0]\\n  83 |     +                     ! : |   +   !   :         1 v8::Isolate::GetCurrent()  (in Electron Framework) + 20020  [0x114e8e5fc]\\n  84 |     +                     ! : |   +   !   1 node::InternalCallbackScope::Close()  (in Electron Framework) + 348  [0x11731daa4]\\n  85 |     +                     ! : |   +   !     1 v8::internal::ThreadIsolation::JitPageReference::AllocationContaining(unsigned long)  (in Electron Framework) + 6640  [0x114d3e790]\\n  86 |     +                     ! : |   +   !       1 v8::MicrotasksScope::~MicrotasksScope()  (in Electron Framework) + 192  [0x114d3eb08]\\n  87 |     +                     ! : |   +   !         1 v8::MicrotasksScope::~MicrotasksScope()  (in Electron Framework) + 288  [0x114d3eb68]\\n  88 |     +                     ! : |   +   !           1 v8::Function::NewInstanceWithSideEffectType(v8::Local<v8::Context>, int, v8::Local<v8::Value>*, v8::SideEffectType) const  (in Electron Framework) + 2188  [0x114d225f8]\\n  89 |     +                     ! : |   +   !             1 ???  (in <unknown binary>)  [0x157e0b154]\\n  90 |     +                     ! : |   +   !               1 ???  (in <unknown binary>)  [0x157e3cc2c]\\n  91 |     +                     ! : |   +   !                 1 ???  (in <unknown binary>)  [0x157f28218]\\n  92 |     +                     ! : |   +   !                   1 ???  (in <unknown binary>)  [0x157e4e308]\\n  93 |     +                     ! : |   +   !                     1 ???  (in <unknown binary>)  [0x1529961d0]\\n  94 |     +                     ! : |   +   !                       1 ???  (in <unknown binary>)  [0x157e006c8]\\n  95 |     +                     ! : |   +   1 node::Environment::RunTimers(uv_timer_s*)  (in Electron Framework) + 280  [0x11738804c]\\n  96 |     +                     ! : |   +   ! 1 v8::Integer::NewFromUnsigned(v8::Isolate*, unsigned int)  (in Electron Framework) + 0  [0x115ca436c]\\n  97 |     +                     ! : |   +   1 node::Environment::RunTimers(uv_timer_s*)  (in Electron Framework) + 8  [0x117387f3c]\\n  98 |     +                     ! : |   4 uv_run  (in Electron Framework) + 376  [0x1170ee968]\\n  99 |     +                     ! : |   + 2 uv__io_poll  (in Electron Framework) + 1112  [0x117100048]\\n 100 |     +                     ! : |   + ! 2 kevent  (in libsystem_kernel.dylib) + 8  [0x191f92d04]\\n 101 |     +                     ! : |   + 2 uv__io_poll  (in Electron Framework) + 1516  [0x1171001dc]\\n 102 |     +                     ! : |   +   1 uv__async_fork  (in Electron Framework) + 644  [0x1170ee49c]\\n 103 |     +                     ! : |   +   : 1 node::PrincipalRealm::maybe_cache_generated_source_map() const  (in Electron Framework) + 186308  [0x117260dd4]\\n 104 |     +                     ! : |   +   :   1 v8::Object::New(v8::Isolate*)  (in Electron Framework) + 120  [0x1158de1e8]\\n 105 |     +                     ! : |   +   :     1 v8::api_internal::MoveGlobalReference(unsigned long**, unsigned long**)  (in Electron Framework) + 73196  [0x1154d4000]\\n 106 |     +                     ! : |   +   :       1 v8::api_internal::MoveGlobalReference(unsigned long**, unsigned long**)  (in Electron Framework) + 73196  [0x1154d4000]\\n 107 |     +                     ! : |   +   1 uv__async_fork  (in Electron Framework) + 568  [0x1170ee450]\\n 108 |     +                     ! : |   2 uv_run  (in Electron Framework) + 556  [0x1170eea1c]\\n 109 |     +                     ! : |   + 2 uv__run_check  (in Electron Framework) + 128  [0x1170f46b4]\\n 110 |     +                     ! : |   +   2 node::Environment::CheckImmediate(uv_check_s*)  (in Electron Framework) + 160  [0x117386b40]\\n 111 |     +                     ! : |   +     1 node::Environment::RunAndClearNativeImmediates(bool)  (in Electron Framework) + 216  [0x1173873d0]\\n 112 |     +                     ! : |   +     ! 1 node::InternalCallbackScope::InternalCallbackScope(node::Environment*, v8::Local<v8::Object>, node::async_context const&, int, v8::Local<v8::Value>)  (in Electron Framework) + 284  [0x11731d8b4]\\n 113 |     +                     ! : |   +     !   1 node::async_context_frame::exchange(v8::Isolate*, v8::Local<v8::Value>)  (in Electron Framework) + 44  [0x117325900]\\n 114 |     +                     ! : |   +     !     1 node::PrincipalRealm::maybe_cache_generated_source_map() const  (in Electron Framework) + 175580  [0x11725e3ec]\\n 115 |     +                     ! : |   +     !       1 node::PrincipalRealm::maybe_cache_generated_source_map() const  (in Electron Framework) + 175648  [0x11725e430]\\n 116 |     +                     ! : |   +     1 node::Environment::RunAndClearNativeImmediates(bool)  (in Electron Framework) + 484  [0x1173874dc]\\n 117 |     +                     ! : |   +       1 uv_prepare_stop  (in Electron Framework) + 60  [0x1170f4488]\\n 118 |     +                     ! : |   1 node::PrincipalRealm::maybe_cache_generated_source_map() const  (in Electron Framework) + 319516  [0x11728162c]\\n 119 |     +                     ! : 1 ElectronInitializeICUandStartNode  (in Electron Framework) + 243140  [0x11713be84]\\n 120 |     +                     ! 8 v8::ValueSerializer::Delegate::HasCustomHostObject(v8::Isolate*)  (in Electron Framework) + 3572  [0x114cdfd74]\\n 121 |     +                     ! : 4 v8::String::ExternalStringResource::UpdateDataCache()  (in Electron Framework) + 49564  [0x114dae72c]\\n 122 |     +                     ! : | 2 v8::String::ExternalStringResource::UpdateDataCache()  (in Electron Framework) + 54584  [0x114dafac8]\\n 123 |     +                     ! : | + 2 v8::Context::Exit()  (in Electron Framework) + 2688  [0x114ecbe34]\\n 124 |     +                     ! : | +   2 v8::Context::Exit()  (in Electron Framework) + 2836  [0x114ecbec8]\\n 125 |     +                     ! : | +     1 v8::Context::Exit()  (in Electron Framework) + 3012  [0x114ecbf78]\\n 126 |     +                     ! : | +     ! 1 v8::String::ExternalStringResource::UpdateDataCache()  (in Electron Framework) + 60640  [0x114db1270]\\n 127 |     +                     ! : | +     1 v8::Context::Exit()  (in Electron Framework) + 3316  [0x114ecc0a8]\\n 128 |     +                     ! : | +       1 ares_llist_node_first  (in Electron Framework) + 2931100  [0x11983be10]\\n 129 |     +                     ! : | 2 v8::String::ExternalStringResource::UpdateDataCache()  (in Electron Framework) + 54504,54548  [0x114dafa78,0x114dafaa4]\\n 130 |     +                     ! : 2 v8::String::ExternalStringResource::UpdateDataCache()  (in Electron Framework) + 49736  [0x114dae7d8]\\n 131 |     +                     ! : | 1 v8::String::ExternalStringResource::UpdateDataCache()  (in Electron Framework) + 56396  [0x114db01dc]\\n 132 |     +                     ! : | + 1 _platform_memset  (in libsystem_platform.dylib) + 140  [0x19200800c]\\n 133 |     +                     ! : | 1 v8::String::ExternalStringResource::UpdateDataCache()  (in Electron Framework) + 56480  [0x114db0230]\\n 134 |     +                     ! : |   1 ElectronInitializeICUandStartNode  (in Electron Framework) + 158908  [0x11712757c]\\n 135 |     +                     ! : 1 v8::String::ExternalStringResource::UpdateDataCache()  (in Electron Framework) + 50012  [0x114dae8ec]\\n 136 |     +                     ! : | 1 v8::internal::ThreadIsolation::LookupJitPageLocked(unsigned long, unsigned long)  (in Electron Framework) + 16544  [0x115866a30]\\n 137 |     +                     ! : 1 v8::String::ExternalStringResource::UpdateDataCache()  (in Electron Framework) + 49868  [0x114dae85c]\\n 138 |     +                     ! 2 v8::ValueSerializer::Delegate::HasCustomHostObject(v8::Isolate*)  (in Electron Framework) + 3508  [0x114cdfd34]\\n 139 |     +                     ! : 2 v8::ValueSerializer::Delegate::HasCustomHostObject(v8::Isolate*)  (in Electron Framework) + 9416  [0x114ce1448]\\n 140 |     +                     ! :   1 v8::internal::ThreadIsolation::JitPageReference::Shrink(v8::internal::ThreadIsolation::JitPage*)  (in Electron Framework) + 66864  [0x1165a9dd4]\\n 141 |     +                     ! :   1 v8::internal::ThreadIsolation::RegisterJitAllocation(unsigned long, unsigned long, v8::internal::ThreadIsolation::JitAllocationType, bool)  (in Electron Framework) + 717848  [0x116fc5e88]\\n 142 |     +                     ! :     1 node::worker::TransferData::FinalizeTransferWrite(v8::Local<v8::Context>, v8::ValueSerializer*)  (in Electron Framework) + 3412  [0x11543ea34]\\n 143 |     +                     ! 1 v8::ValueSerializer::Delegate::HasCustomHostObject(v8::Isolate*)  (in Electron Framework) + 3312  [0x114cdfc70]\\n 144 |     +                     1 v8::ScriptCompiler::CompileModule(v8::Local<v8::Context>, v8::ScriptCompiler::StreamedSource*, v8::Local<v8::String>, v8::ScriptOrigin const&)  (in Electron Framework) + 74904  [0x115f44c5c]\\n 145 |     +                       1 uv_get_osfhandle  (in Electron Framework) + 86544  [0x114fb62b0]\\n 146 |     +                         1 objc_autoreleasePoolPop  (in libobjc.A.dylib) + 164  [0x191bdfff0]\\n 147 |     2445 Thread_959840: ThreadPoolServiceThread\\n 148 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 149 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 150 |     +     2445 v8::RegExp::New(v8::Local<v8::Context>, v8::Local<v8::String>, v8::RegExp::Flags)  (in Electron Framework) + 73760  [0x115bd9b78]\\n 151 |     +       2445 v8::CFunctionInfo::ArgumentInfo(unsigned int) const  (in Electron Framework) + 9032  [0x1163c44ac]\\n 152 |     +         2445 v8::V8::SetFlagsFromString(char const*, unsigned long)  (in Electron Framework) + 354000  [0x116468fc8]\\n 153 |     +           2445 v8::V8::SetFlagsFromString(char const*, unsigned long)  (in Electron Framework) + 354088  [0x116469020]\\n 154 |     +             2445 v8::V8::SetFlagsFromString(char const*, unsigned long)  (in Electron Framework) + 354248  [0x1164690c0]\\n 155 |     +               2445 node::PrincipalRealm::http2session_on_frame_error_function() const  (in Electron Framework) + 121004  [0x11621f004]\\n 156 |     +                 2445 cppgc::internal::PersistentRegionBase::PersistentRegionBase(cppgc::internal::FatalOutOfMemoryHandler const&)  (in Electron Framework) + 17540  [0x1160092dc]\\n 157 |     +                   2445 cppgc::internal::PersistentRegionBase::PersistentRegionBase(cppgc::internal::FatalOutOfMemoryHandler const&)  (in Electron Framework) + 20472  [0x116009e50]\\n 158 |     +                     2445 kevent64  (in libsystem_kernel.dylib) + 8  [0x191f9895c]\\n 159 |     2445 Thread_959841: ThreadPoolForegroundWorker\\n 160 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 161 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 162 |     +     2445 v8::RegExp::New(v8::Local<v8::Context>, v8::Local<v8::String>, v8::RegExp::Flags)  (in Electron Framework) + 73760  [0x115bd9b78]\\n 163 |     +       2445 uv_get_osfhandle  (in Electron Framework) + 84276  [0x114fb59d4]\\n 164 |     +         2445 uv_get_osfhandle  (in Electron Framework) + 84496  [0x114fb5ab0]\\n 165 |     +           2445 uv_get_osfhandle  (in Electron Framework) + 86304  [0x114fb61c0]\\n 166 |     +             2445 uv_get_osfhandle  (in Electron Framework) + 95800  [0x114fb86d8]\\n 167 |     +               2445 uv_get_osfhandle  (in Electron Framework) + 95888  [0x114fb8730]\\n 168 |     +                 2445 uv_get_osfhandle  (in Electron Framework) + 96416  [0x114fb8940]\\n 169 |     +                   2445 mach_msg  (in libsystem_kernel.dylib) + 24  [0x191f8cfa8]\\n 170 |     +                     2445 mach_msg_overwrite  (in libsystem_kernel.dylib) + 484  [0x191f95764]\\n 171 |     +                       2445 mach_msg2_internal  (in libsystem_kernel.dylib) + 76  [0x191f9f3a0]\\n 172 |     +                         2445 mach_msg2_trap  (in libsystem_kernel.dylib) + 8  [0x191f8cc34]\\n 173 |     2445 Thread_959842: ThreadPoolBackgroundWorker\\n 174 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 175 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 176 |     +     2445 v8::RegExp::New(v8::Local<v8::Context>, v8::Local<v8::String>, v8::RegExp::Flags)  (in Electron Framework) + 73760  [0x115bd9b78]\\n 177 |     +       2445 uv_get_osfhandle  (in Electron Framework) + 84328  [0x114fb5a08]\\n 178 |     +         2445 uv_get_osfhandle  (in Electron Framework) + 84624  [0x114fb5b30]\\n 179 |     +           2445 uv_get_osfhandle  (in Electron Framework) + 86304  [0x114fb61c0]\\n 180 |     +             2445 uv_get_osfhandle  (in Electron Framework) + 95800  [0x114fb86d8]\\n 181 |     +               2445 uv_get_osfhandle  (in Electron Framework) + 95888  [0x114fb8730]\\n 182 |     +                 2445 uv_get_osfhandle  (in Electron Framework) + 96416  [0x114fb8940]\\n 183 |     +                   2445 mach_msg  (in libsystem_kernel.dylib) + 24  [0x191f8cfa8]\\n 184 |     +                     2445 mach_msg_overwrite  (in libsystem_kernel.dylib) + 484  [0x191f95764]\\n 185 |     +                       2445 mach_msg2_internal  (in libsystem_kernel.dylib) + 76  [0x191f9f3a0]\\n 186 |     +                         2445 mach_msg2_trap  (in libsystem_kernel.dylib) + 8  [0x191f8cc34]\\n 187 |     2445 Thread_959843: ThreadPoolForegroundWorker\\n 188 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 189 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 190 |     +     2445 v8::RegExp::New(v8::Local<v8::Context>, v8::Local<v8::String>, v8::RegExp::Flags)  (in Electron Framework) + 73760  [0x115bd9b78]\\n 191 |     +       2445 uv_get_osfhandle  (in Electron Framework) + 84276  [0x114fb59d4]\\n 192 |     +         2445 uv_get_osfhandle  (in Electron Framework) + 84496  [0x114fb5ab0]\\n 193 |     +           2445 uv_get_osfhandle  (in Electron Framework) + 86304  [0x114fb61c0]\\n 194 |     +             2445 uv_get_osfhandle  (in Electron Framework) + 95800  [0x114fb86d8]\\n 195 |     +               2445 uv_get_osfhandle  (in Electron Framework) + 95888  [0x114fb8730]\\n 196 |     +                 2445 uv_get_osfhandle  (in Electron Framework) + 96416  [0x114fb8940]\\n 197 |     +                   2445 mach_msg  (in libsystem_kernel.dylib) + 24  [0x191f8cfa8]\\n 198 |     +                     2445 mach_msg_overwrite  (in libsystem_kernel.dylib) + 484  [0x191f95764]\\n 199 |     +                       2445 mach_msg2_internal  (in libsystem_kernel.dylib) + 76  [0x191f9f3a0]\\n 200 |     +                         2445 mach_msg2_trap  (in libsystem_kernel.dylib) + 8  [0x191f8cc34]\\n 201 |     2445 Thread_959844: Chrome_ChildIOThread\\n 202 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 203 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 204 |     +     2445 v8::RegExp::New(v8::Local<v8::Context>, v8::Local<v8::String>, v8::RegExp::Flags)  (in Electron Framework) + 73760  [0x115bd9b78]\\n 205 |     +       2445 v8::CFunctionInfo::ArgumentInfo(unsigned int) const  (in Electron Framework) + 9032  [0x1163c44ac]\\n 206 |     +         2445 v8::Array::New(v8::Isolate*, v8::Local<v8::Value>*, unsigned long)  (in Electron Framework) + 94112  [0x1165e1f80]\\n 207 |     +           2445 v8::V8::SetFlagsFromString(char const*, unsigned long)  (in Electron Framework) + 354088  [0x116469020]\\n 208 |     +             2445 v8::V8::SetFlagsFromString(char const*, unsigned long)  (in Electron Framework) + 354248  [0x1164690c0]\\n 209 |     +               2445 node::PrincipalRealm::http2session_on_frame_error_function() const  (in Electron Framework) + 121004  [0x11621f004]\\n 210 |     +                 2445 cppgc::internal::PersistentRegionBase::PersistentRegionBase(cppgc::internal::FatalOutOfMemoryHandler const&)  (in Electron Framework) + 17540  [0x1160092dc]\\n 211 |     +                   2445 cppgc::internal::PersistentRegionBase::PersistentRegionBase(cppgc::internal::FatalOutOfMemoryHandler const&)  (in Electron Framework) + 20472  [0x116009e50]\\n 212 |     +                     2445 kevent64  (in libsystem_kernel.dylib) + 8  [0x191f9895c]\\n 213 |     2445 Thread_959847\\n 214 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 215 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 216 |     +     2445 node::WorkerThreadsTaskRunner::DelayedTaskScheduler::Start()::'lambda'(void*)::__invoke(void*)  (in Electron Framework) + 112  [0x117479f7c]\\n 217 |     +       2445 uv_run  (in Electron Framework) + 376  [0x1170ee968]\\n 218 |     +         2445 uv__io_poll  (in Electron Framework) + 1112  [0x117100048]\\n 219 |     +           2445 kevent  (in libsystem_kernel.dylib) + 8  [0x191f92d04]\\n 220 |     2445 Thread_959848\\n 221 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 222 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 223 |     +     2445 node::WorkerThreadsTaskRunner::WorkerThreadsTaskRunner(int)  (in Electron Framework) + 1180  [0x117478058]\\n 224 |     +       2445 node::WorkerThreadsTaskRunner::DelayedTaskScheduler::FlushTasks(uv_async_s*)  (in Electron Framework) + 380  [0x11747a124]\\n 225 |     +         2445 uv_cond_wait  (in Electron Framework) + 40  [0x1170fb374]\\n 226 |     +           2445 _pthread_cond_wait  (in libsystem_pthread.dylib) + 984  [0x191fcf0e0]\\n 227 |     +             2445 __psynch_cvwait  (in libsystem_kernel.dylib) + 8  [0x191f903cc]\\n 228 |     2445 Thread_959849\\n 229 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 230 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 231 |     +     2445 node::WorkerThreadsTaskRunner::WorkerThreadsTaskRunner(int)  (in Electron Framework) + 1180  [0x117478058]\\n 232 |     +       2445 node::WorkerThreadsTaskRunner::DelayedTaskScheduler::FlushTasks(uv_async_s*)  (in Electron Framework) + 380  [0x11747a124]\\n 233 |     +         2445 uv_cond_wait  (in Electron Framework) + 40  [0x1170fb374]\\n 234 |     +           2445 _pthread_cond_wait  (in libsystem_pthread.dylib) + 984  [0x191fcf0e0]\\n 235 |     +             2445 __psynch_cvwait  (in libsystem_kernel.dylib) + 8  [0x191f903cc]\\n 236 |     2445 Thread_959850\\n 237 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 238 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 239 |     +     2445 node::WorkerThreadsTaskRunner::WorkerThreadsTaskRunner(int)  (in Electron Framework) + 1180  [0x117478058]\\n 240 |     +       2445 node::WorkerThreadsTaskRunner::DelayedTaskScheduler::FlushTasks(uv_async_s*)  (in Electron Framework) + 380  [0x11747a124]\\n 241 |     +         2445 uv_cond_wait  (in Electron Framework) + 40  [0x1170fb374]\\n 242 |     +           2445 _pthread_cond_wait  (in libsystem_pthread.dylib) + 984  [0x191fcf0e0]\\n 243 |     +             2445 __psynch_cvwait  (in libsystem_kernel.dylib) + 8  [0x191f903cc]\\n 244 |     2445 Thread_959852\\n 245 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 246 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 247 |     +     2445 node::inspector::Agent::GetWsUrl() const  (in Electron Framework) + 52  [0x1175d139c]\\n 248 |     +       2445 uv_sem_wait  (in Electron Framework) + 24  [0x1170fb1dc]\\n 249 |     +         2445 semaphore_wait_trap  (in libsystem_kernel.dylib) + 8  [0x191f8cbb0]\\n 250 |     2445 Thread_959855\\n 251 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 252 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 253 |     +     2445 uv_cancel  (in Electron Framework) + 680  [0x1170eb0c0]\\n 254 |     +       2445 uv_cond_wait  (in Electron Framework) + 40  [0x1170fb374]\\n 255 |     +         2445 _pthread_cond_wait  (in libsystem_pthread.dylib) + 984  [0x191fcf0e0]\\n 256 |     +           2445 __psynch_cvwait  (in libsystem_kernel.dylib) + 8  [0x191f903cc]\\n 257 |     2445 Thread_959856\\n 258 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 259 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 260 |     +     2445 uv_cancel  (in Electron Framework) + 680  [0x1170eb0c0]\\n 261 |     +       2445 uv_cond_wait  (in Electron Framework) + 40  [0x1170fb374]\\n 262 |     +         2445 _pthread_cond_wait  (in libsystem_pthread.dylib) + 984  [0x191fcf0e0]\\n 263 |     +           2445 __psynch_cvwait  (in libsystem_kernel.dylib) + 8  [0x191f903cc]\\n 264 |     2445 Thread_959857\\n 265 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 266 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 267 |     +     2445 uv_cancel  (in Electron Framework) + 680  [0x1170eb0c0]\\n 268 |     +       2445 uv_cond_wait  (in Electron Framework) + 40  [0x1170fb374]\\n 269 |     +         2445 _pthread_cond_wait  (in libsystem_pthread.dylib) + 984  [0x191fcf0e0]\\n 270 |     +           2445 __psynch_cvwait  (in libsystem_kernel.dylib) + 8  [0x191f903cc]\\n 271 |     2445 Thread_959858\\n 272 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 273 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 274 |     +     2445 uv_cancel  (in Electron Framework) + 680  [0x1170eb0c0]\\n 275 |     +       2445 uv_cond_wait  (in Electron Framework) + 40  [0x1170fb374]\\n 276 |     +         2445 _pthread_cond_wait  (in libsystem_pthread.dylib) + 984  [0x191fcf0e0]\\n 277 |     +           2445 __psynch_cvwait  (in libsystem_kernel.dylib) + 8  [0x191f903cc]\\n 278 |     2445 Thread_959859\\n 279 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 280 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 281 |     +     2387 node::PrincipalRealm::maybe_cache_generated_source_map() const  (in Electron Framework) + 319136  [0x1172814b0]\\n 282 |     +     ! 2387 node::PrincipalRealm::maybe_cache_generated_source_map() const  (in Electron Framework) + 726636  [0x1172e4c7c]\\n 283 |     +     !   2387 __select  (in libsystem_kernel.dylib) + 8  [0x191f97c2c]\\n 284 |     +     31 node::PrincipalRealm::maybe_cache_generated_source_map() const  (in Electron Framework) + 319160  [0x1172814c8]\\n 285 |     +     ! 31 uv_sem_wait  (in Electron Framework) + 24  [0x1170fb1dc]\\n 286 |     +     !   30 semaphore_wait_trap  (in libsystem_kernel.dylib) + 8  [0x191f8cbb0]\\n 287 |     +     !   1 semaphore_wait  (in libsystem_kernel.dylib) + 0  [0x191f8fc6c]\\n 288 |     +     27 node::PrincipalRealm::maybe_cache_generated_source_map() const  (in Electron Framework) + 319152  [0x1172814c0]\\n 289 |     +       22 node::PrincipalRealm::maybe_cache_generated_source_map() const  (in Electron Framework) + 320128  [0x117281890]\\n 290 |     +       : 22 node::PrincipalRealm::async_hooks_init_function() const  (in Electron Framework) + 2660  [0x1151f64e8]\\n 291 |     +       :   17 v8::TryCatch::SetCaptureMessage(bool)  (in Electron Framework) + 24968  [0x114d44d4c]\\n 292 |     +       :   | 14 v8::Value::IsObject() const  (in Electron Framework) + 120  [0x114e5d814]\\n 293 |     +       :   | + 14 v8::internal::ThreadIsolation::LookupJitPage(unsigned long, unsigned long)  (in Electron Framework) + 120396  [0x114ff3b98]\\n 294 |     +       :   | +   14 v8::internal::ThreadIsolation::LookupJitPage(unsigned long, unsigned long)  (in Electron Framework) + 120580  [0x114ff3c50]\\n 295 |     +       :   | +     13 mach_msg  (in libsystem_kernel.dylib) + 24  [0x191f8cfa8]\\n 296 |     +       :   | +     ! 12 mach_msg_overwrite  (in libsystem_kernel.dylib) + 484  [0x191f95764]\\n 297 |     +       :   | +     ! : 12 mach_msg2_internal  (in libsystem_kernel.dylib) + 76  [0x191f9f3a0]\\n 298 |     +       :   | +     ! :   12 mach_msg2_trap  (in libsystem_kernel.dylib) + 8  [0x191f8cc34]\\n 299 |     +       :   | +     ! 1 mach_msg_overwrite  (in libsystem_kernel.dylib) + 220  [0x191f9565c]\\n 300 |     +       :   | +     !   1 voucher_mach_msg_fill_aux  (in libdispatch.dylib) + 64  [0x191e3ed88]\\n 301 |     +       :   | +     1 mach_msg  (in libsystem_kernel.dylib) + 0  [0x191f8cf90]\\n 302 |     +       :   | 2 v8::internal::ThreadIsolation::RegisterJitAllocation(unsigned long, unsigned long, v8::internal::ThreadIsolation::JitAllocationType, bool)  (in Electron Framework) + 711176  [0x116fc4478]\\n 303 |     +       :   | 1 v8::internal::ThreadIsolation::LookupJitPage(unsigned long, unsigned long)  (in Electron Framework) + 120372  [0x114ff3b80]\\n 304 |     +       :   2 v8::TryCatch::SetCaptureMessage(bool)  (in Electron Framework) + 24256  [0x114d44a84]\\n 305 |     +       :   | 2 v8::IsolateGroup::IsolateGroup(v8::IsolateGroup&&)  (in Electron Framework) + 356  [0x114d45e10]\\n 306 |     +       :   1 v8::TryCatch::SetCaptureMessage(bool)  (in Electron Framework) + 24500  [0x114d44b78]\\n 307 |     +       :   | 1 v8::TryCatch::SetCaptureMessage(bool)  (in Electron Framework) + 24608  [0x114d44be4]\\n 308 |     +       :   1 v8::TryCatch::SetCaptureMessage(bool)  (in Electron Framework) + 24636  [0x114d44c00]\\n 309 |     +       :   | 1 v8::TryCatch::SetCaptureMessage(bool)  (in Electron Framework) + 28532  [0x114d45b38]\\n 310 |     +       :   |   1 v8::TryCatch::SetCaptureMessage(bool)  (in Electron Framework) + 28532  [0x114d45b38]\\n 311 |     +       :   |     1 v8::TryCatch::SetCaptureMessage(bool)  (in Electron Framework) + 28612  [0x114d45b88]\\n 312 |     +       :   1 v8::TryCatch::SetCaptureMessage(bool)  (in Electron Framework) + 24096  [0x114d449e4]\\n 313 |     +       3 node::PrincipalRealm::maybe_cache_generated_source_map() const  (in Electron Framework) + 320048  [0x117281840]\\n 314 |     +       : 3 v8::ValueSerializer::Delegate::HasCustomHostObject(v8::Isolate*)  (in Electron Framework) + 1364  [0x114cdf4d4]\\n 315 |     +       :   1 ares_llist_node_first  (in Electron Framework) + 3133444  [0x11986d478]\\n 316 |     +       :   | 1 pthread_getspecific  (in libsystem_pthread.dylib) + 0  [0x191fc9bf0]\\n 317 |     +       :   1 ares_llist_node_first  (in Electron Framework) + 3173628  [0x119877170]\\n 318 |     +       :   1 v8::ValueSerializer::Delegate::HasCustomHostObject(v8::Isolate*)  (in Electron Framework) + 1364  [0x114cdf4d4]\\n 319 |     +       :     1 ares_llist_node_first  (in Electron Framework) + 3133244  [0x11986d3b0]\\n 320 |     +       2 cppgc::LivenessBroker::IsHeapObjectAliveImpl(void const*) const  (in Electron Framework) + 1784,1804  [0x114cebd38,0x114cebd4c]\\n 321 |     2445 Thread_959860\\n 322 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 323 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 324 |     +     2445 _register_external_reference_worker(node::ExternalReferenceRegistry*)  (in Electron Framework) + 4152  [0x117524098]\\n 325 |     +       2445 node::worker::Worker::Run()  (in Electron Framework) + 2108  [0x11751fc10]\\n 326 |     +         2445 node::SpinEventLoopInternal(node::Environment*)  (in Electron Framework) + 356  [0x11731e814]\\n 327 |     +           2445 uv_run  (in Electron Framework) + 376  [0x1170ee968]\\n 328 |     +             2445 uv__io_poll  (in Electron Framework) + 1112  [0x117100048]\\n 329 |     +               2445 kevent  (in libsystem_kernel.dylib) + 8  [0x191f92d04]\\n 330 |     2445 Thread_960491\\n 331 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 332 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 333 |     +     2445 _register_external_reference_worker(node::ExternalReferenceRegistry*)  (in Electron Framework) + 4152  [0x117524098]\\n 334 |     +       2445 node::worker::Worker::Run()  (in Electron Framework) + 2108  [0x11751fc10]\\n 335 |     +         2445 node::SpinEventLoopInternal(node::Environment*)  (in Electron Framework) + 356  [0x11731e814]\\n 336 |     +           2445 uv_run  (in Electron Framework) + 376  [0x1170ee968]\\n 337 |     +             2445 uv__io_poll  (in Electron Framework) + 1112  [0x117100048]\\n 338 |     +               2445 kevent  (in libsystem_kernel.dylib) + 8  [0x191f92d04]\\n 339 |     2445 Thread_961032\\n 340 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 341 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 342 |     +     2445 uv__fsevents_close  (in Electron Framework) + 1408  [0x1170ff3c0]\\n 343 |     +       2445 CFRunLoopRun  (in CoreFoundation) + 64  [0x192131714]\\n 344 |     +         2445 CFRunLoopRunSpecific  (in CoreFoundation) + 572  [0x1920b7c58]\\n 345 |     +           2445 __CFRunLoopRun  (in CoreFoundation) + 1208  [0x1920b8798]\\n 346 |     +             2445 __CFRunLoopServiceMachPort  (in CoreFoundation) + 160  [0x1920b9e7c]\\n 347 |     +               2445 mach_msg  (in libsystem_kernel.dylib) + 24  [0x191f8cfa8]\\n 348 |     +                 2445 mach_msg_overwrite  (in libsystem_kernel.dylib) + 484  [0x191f95764]\\n 349 |     +                   2445 mach_msg2_internal  (in libsystem_kernel.dylib) + 76  [0x191f9f3a0]\\n 350 |     +                     2445 mach_msg2_trap  (in libsystem_kernel.dylib) + 8  [0x191f8cc34]\\n 351 |     2445 Thread_961301\\n 352 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 353 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 354 |     +     2445 _register_external_reference_worker(node::ExternalReferenceRegistry*)  (in Electron Framework) + 4152  [0x117524098]\\n 355 |     +       2445 node::worker::Worker::Run()  (in Electron Framework) + 2108  [0x11751fc10]\\n 356 |     +         2445 node::SpinEventLoopInternal(node::Environment*)  (in Electron Framework) + 356  [0x11731e814]\\n 357 |     +           2445 uv_run  (in Electron Framework) + 376  [0x1170ee968]\\n 358 |     +             2445 uv__io_poll  (in Electron Framework) + 1112  [0x117100048]\\n 359 |     +               2445 kevent  (in libsystem_kernel.dylib) + 8  [0x191f92d04]\\n 360 |     2445 Thread_1137129\\n 361 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 362 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 363 |     +     2445 _register_external_reference_worker(node::ExternalReferenceRegistry*)  (in Electron Framework) + 4152  [0x117524098]\\n 364 |     +       2445 node::worker::Worker::Run()  (in Electron Framework) + 2108  [0x11751fc10]\\n 365 |     +         2445 node::SpinEventLoopInternal(node::Environment*)  (in Electron Framework) + 356  [0x11731e814]\\n 366 |     +           2445 uv_run  (in Electron Framework) + 376  [0x1170ee968]\\n 367 |     +             2445 uv__io_poll  (in Electron Framework) + 1112  [0x117100048]\\n 368 |     +               2445 kevent  (in libsystem_kernel.dylib) + 8  [0x191f92d04]\\n 369 |     2445 Thread_6346458\\n 370 |     + 2445 thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 371 |     +   2445 _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 372 |     +     2445 _register_external_reference_worker(node::ExternalReferenceRegistry*)  (in Electron Framework) + 4152  [0x117524098]\\n 373 |     +       2445 node::worker::Worker::Run()  (in Electron Framework) + 2108  [0x11751fc10]\\n 374 |     +         2445 node::SpinEventLoopInternal(node::Environment*)  (in Electron Framework) + 356  [0x11731e814]\\n 375 |     +           2445 uv_run  (in Electron Framework) + 376  [0x1170ee968]\\n 376 |     +             2445 uv__io_poll  (in Electron Framework) + 1112  [0x117100048]\\n 377 |     +               2445 kevent  (in libsystem_kernel.dylib) + 8  [0x191f92d04]\\n 378 |     2445 Thread_7044705\\n 379 |       2445 start_wqthread  (in libsystem_pthread.dylib) + 8  [0x191fc9b74]\\n 380 |         2445 _pthread_wqthread  (in libsystem_pthread.dylib) + 368  [0x191fcaeb0]\\n 381 |           2445 __workq_kernreturn  (in libsystem_kernel.dylib) + 8  [0x191f8e8b0]\\n 382 | \\n 383 | Total number in stack (recursive counted multiple, when >=5):\\n 384 |         21       _pthread_start  (in libsystem_pthread.dylib) + 136  [0x191fcec0c]\\n 385 |         21       thread_start  (in libsystem_pthread.dylib) + 8  [0x191fc9b80]\\n 386 |         7       __psynch_cvwait  (in libsystem_kernel.dylib) + 0  [0x191f903c4]\\n 387 |         7       _pthread_cond_wait  (in libsystem_pthread.dylib) + 984  [0x191fcf0e0]\\n 388 |         7       kevent  (in libsystem_kernel.dylib) + 0  [0x191f92cfc]\\n 389 |         7       uv__io_poll  (in Electron Framework) + 1112  [0x117100048]\\n 390 |         7       uv_cond_wait  (in Electron Framework) + 40  [0x1170fb374]\\n 391 |         7       uv_run  (in Electron Framework) + 376  [0x1170ee968]\\n 392 |         6       mach_msg  (in libsystem_kernel.dylib) + 24  [0x191f8cfa8]\\n 393 |         6       mach_msg2_internal  (in libsystem_kernel.dylib) + 76  [0x191f9f3a0]\\n 394 |         6       mach_msg2_trap  (in libsystem_kernel.dylib) + 0  [0x191f8cc2c]\\n 395 |         6       mach_msg_overwrite  (in libsystem_kernel.dylib) + 484  [0x191f95764]\\n 396 |         5       _register_external_reference_worker(node::ExternalReferenceRegistry*)  (in Electron Framework) + 4152  [0x117524098]\\n 397 |         5       node::SpinEventLoopInternal(node::Environment*)  (in Electron Framework) + 356  [0x11731e814]\\n 398 |         5       node::worker::Worker::Run()  (in Electron Framework) + 2108  [0x11751fc10]\\n 399 |         5       v8::RegExp::New(v8::Local<v8::Context>, v8::Local<v8::String>, v8::RegExp::Flags)  (in Electron Framework) + 73760  [0x115bd9b78]\\n 400 | \\n 401 | Sort by top of stack, same collapsed (when >= 5):\\n 402 |         __psynch_cvwait  (in libsystem_kernel.dylib)        17115\\n 403 |         kevent  (in libsystem_kernel.dylib)        14672\\n 404 |         mach_msg2_trap  (in libsystem_kernel.dylib)        12207\\n 405 |         kevent64  (in libsystem_kernel.dylib)        4890\\n 406 |         semaphore_wait_trap  (in libsystem_kernel.dylib)        2475\\n 407 |         __workq_kernreturn  (in libsystem_kernel.dylib)        2445\\n 408 |         __select  (in libsystem_kernel.dylib)        2387\\n 409 | \\n 410 | Binary Images:\\n 411 |        0x10097c000 -        0x1009829df +com.github.Electron.helper (???) <4C4C44A5-5555-3144-A1CA-6645EC9E057A> /Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)\\n 412 |        0x1009f4000 -        0x100a085af +com.github.Squirrel (1.0 - 1) <4C4C4474-5555-3144-A194-2FB0734B47B5> /Applications/Visual Studio Code.app/Contents/Frameworks/Squirrel.framework/Versions/A/Squirrel\\n 413 |        0x100a20000 -        0x100a2b1df +org.mantle.Mantle (1.0 - 0.0.0) <4C4C440B-5555-3144-A1EE-C84C9CA368AE> /Applications/Visual Studio Code.app/Contents/Frameworks/Mantle.framework/Versions/A/Mantle\\n 414 |        0x100aec000 -        0x100b2d17f +com.electron.reactive (3.1.0 - 0.0.0) <4C4C44AF-5555-3144-A16C-F764CEEAAF4C> /Applications/Visual Studio Code.app/Contents/Frameworks/ReactiveObjC.framework/Versions/A/ReactiveObjC\\n 415 |        0x100f54000 -        0x100f93ffb +spdlog.node (0) <4D65103D-EC27-3E5C-9674-596B9E0D640A> /Applications/Visual Studio Code.app/Contents/Resources/app/node_modules/@vscode/spdlog/build/Release/spdlog.node\\n 416 |        0x101110000 -        0x1012a8547 +libffmpeg.dylib (0) <4C4C44DE-5555-3144-A18E-EB8E806A8599> /Applications/Visual Studio Code.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Libraries/libffmpeg.dylib\\n 417 |        0x114cd8000 -        0x11db977b7 +com.github.Electron.framework (35.5.1) <4C4C4468-5555-3144-A1B6-5CED84853409> /Applications/Visual Studio Code.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Electron Framework\\n 418 |        0x191bd4000 -        0x191c27893  libobjc.A.dylib (940.4) <4966864D-C147-33D3-BB18-1E3979590B6D> /usr/lib/libobjc.A.dylib\\n 419 |        0x191c28000 -        0x191cc34cf  dyld (1.0.0 - 1285.19) <9CF0401A-A938-389E-A77D-9E9608076CCF> /usr/lib/dyld\\n 420 |        0x191cc4000 -        0x191cc7168  libsystem_blocks.dylib (96) <4740F383-F4C0-328E-8753-41E8982EC94A> /usr/lib/system/libsystem_blocks.dylib\\n 421 |        0x191cc8000 -        0x191d128ff  libxpc.dylib (2894.121.3) <3B8AE373-80D2-36A4-A628-0CF6CF083703> /usr/lib/system/libxpc.dylib\\n 422 |        0x191d13000 -        0x191d2e7ff  libsystem_trace.dylib (1643.120.5) <C8ED43C4-C077-34E9-AB26-F34231C895BE> /usr/lib/system/libsystem_trace.dylib\\n 423 |        0x191d2f000 -        0x191dcd4b7  libcorecrypto.dylib (1736.120.5) <7EFCF3FE-36A2-3423-BA6A-CD23D0697006> /usr/lib/system/libcorecrypto.dylib\\n 424 |        0x191dce000 -        0x191e14fef  libsystem_malloc.dylib (715.120.13) <E2C4CBE4-6195-3328-B87C-2DFA4A6AD039> /usr/lib/system/libsystem_malloc.dylib\\n 425 |        0x191e15000 -        0x191e5b73f  libdispatch.dylib (1521.120.4) <8BF83CDA-8DB1-3D46-94B0-D811BD77E078> /usr/lib/system/libdispatch.dylib\\n 426 |        0x191e5c000 -        0x191e5ed1b  libsystem_featureflags.dylib (97) <05344457-BD89-3B9D-89E2-5A6B3DE3F8B0> /usr/lib/system/libsystem_featureflags.dylib\\n 427 |        0x191e5f000 -        0x191ee046f  libsystem_c.dylib (1698.100.8) <F4529D5E-24F3-3BBB-BD3C-984856875FC8> /usr/lib/system/libsystem_c.dylib\\n 428 |        0x191ee1000 -        0x191f6dff7  libc++.1.dylib (1900.180) <875203A1-087B-33A6-93A5-928BB7E9114C> /usr/lib/libc++.1.dylib\\n 429 |        0x191f6e000 -        0x191f8bfff  libc++abi.dylib (1900.180) <4DB4AC5C-E091-3A2E-A149-B7955B8AF852> /usr/lib/libc++abi.dylib\\n 430 |        0x191f8c000 -        0x191fc7653  libsystem_kernel.dylib (11417.121.6) <60485B6F-67E5-38C1-AEC9-EFD6031FF166> /usr/lib/system/libsystem_kernel.dylib\\n 431 |        0x191fc8000 -        0x191fd4a47  libsystem_pthread.dylib (536) <647B91FC-96D3-3BBB-AF08-970DF45257C8> /usr/lib/system/libsystem_pthread.dylib\\n 432 |        0x191fd5000 -        0x192004ba3  libdyld.dylib (1285.19) <C62596FE-63C5-3502-BBD1-14147A7CA921> /usr/lib/system/libdyld.dylib\\n 433 |        0x192005000 -        0x19200cb4f  libsystem_platform.dylib (349) <519F3220-7499-334A-96D6-CCF3DC28F986> /usr/lib/system/libsystem_platform.dylib\\n 434 |        0x19200d000 -        0x19203c6f7  libsystem_info.dylib (597) <33D79A98-6726-3AE0-8D55-4EDD853ABC71> /usr/lib/system/libsystem_info.dylib\\n 435 |        0x19203d000 -        0x19257bfff  com.apple.CoreFoundation (6.9 - 3502.1.401) <DF489A59-B4F6-32B8-9BB4-9B832960AA52> /System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation\\n 436 |        0x19257c000 -        0x19286e09f  com.apple.LaunchServices (1141.1 - 1141.1) <04A26E59-1424-3B9E-AD82-A69C0D0F0167> /System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices\\n 437 |        0x19286f000 -        0x1929f357f  com.apple.gpusw.MetalTools (1.0 - 1) <8646C6D4-10C4-3CE7-8327-FB8E7B5956EB> /System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools\\n 438 |        0x1929f4000 -        0x19308772f  libBLAS.dylib (1545) <EBCE9EDA-EE69-3FD3-BCE4-EB52416FEF0B> /System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib\\n 439 |        0x193088000 -        0x19319413f  com.apple.Lexicon-framework (1.0 - 173.9) <8B82635D-E766-3B62-AF43-CD46AED1DC42> /System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon\\n 440 |        0x193195000 -        0x193290bd7  libSparse.dylib (127.120.9) <47F4BD3B-9778-3112-AD82-6ED816CF615C> /System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib\\n 441 |        0x193291000 -        0x19332491f  com.apple.SystemConfiguration (1.21 - 1.21) <AA3EDCA7-B7C7-3843-B008-2EC21EA870D5> /System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration\\n 442 |        0x193325000 -        0x193359bb7  libCRFSuite.dylib (55) <C786D91E-7365-35C7-A0AA-04D1AD369FCE> /usr/lib/libCRFSuite.dylib\\n 443 |        0x19335a000 -        0x19362ce7f  libmecabra.dylib (1093.501) <056DBF53-0C84-3584-BBB8-88172D9EADDA> /usr/lib/libmecabra.dylib\\n 444 |        0x19362d000 -        0x1944162ff  com.apple.Foundation (6.9 - 3502.1.401) <E8F6A451-0ACC-3E05-B18F-FEC6618CE44A> /System/Library/Frameworks/Foundation.framework/Versions/C/Foundation\\n 445 |        0x194417000 -        0x1945d117f  com.apple.LanguageModeling (1.0 - 425.2) <CB6833CA-1545-3822-8F90-AC5D484ED3D2> /System/Library/PrivateFrameworks/LanguageModeling.framework/Versions/A/LanguageModeling\\n 446 |        0x1945d2000 -        0x1946f271f  com.apple.CoreDisplay (288 - 288) <2895A1C4-45B2-358D-B390-66217DE02BF9> /System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay\\n 447 |        0x1946f3000 -        0x194aaa33f  com.apple.audio.AudioToolboxCore (1.0 - 1456.607) <21A66285-E29C-3271-8F0A-EA8408087872> /System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore\\n 448 |        0x194aab000 -        0x194cc951f  com.apple.CoreText (844.5.0.6 - 844.5.0.6) <D6090899-926C-3D69-ABA4-DA4AF56142CE> /System/Library/Frameworks/CoreText.framework/Versions/A/CoreText\\n 449 |        0x194cca000 -        0x1953fe25f  com.apple.audio.CoreAudio (5.0 - 5.0) <B2B97B04-6275-3031-8A89-BE6105F1E8F3> /System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio\\n 450 |        0x1953ff000 -        0x19580903f  com.apple.security (7.0 - 61439.120.27) <EE968448-1658-300F-A0E6-A9B6F91792E3> /System/Library/Frameworks/Security.framework/Versions/A/Security\\n 451 |        0x19580a000 -        0x195ad5bdb  libicucore.A.dylib (76104.3) <CC5A85AF-F508-34E0-AC7A-C37D806BCF48> /usr/lib/libicucore.A.dylib\\n 452 |        0x195ad6000 -        0x195ae0387  libsystem_darwin.dylib (1698.100.8) <106CE5FD-0965-3702-ADE3-B5641ACE0234> /usr/lib/system/libsystem_darwin.dylib\\n 453 |        0x195ae1000 -        0x195dd3cbf  com.apple.CoreServices.CarbonCore (1333 - 1333) <6434A7BE-A5CD-3DF4-BA18-4D1359333044> /System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore\\n 454 |        0x195dd4000 -        0x195e1377b  com.apple.CoreServicesInternal (505 - 505) <41B7034F-E48D-3345-8C25-5C86ED812750> /System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal\\n 455 |        0x195e14000 -        0x195e544df  com.apple.CSStore (1141.1 - 1141.1) <5064202B-D8C3-3E4A-9346-99D78CE23F12> /System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore\\n 456 |        0x195e55000 -        0x195f3733f  com.apple.framework.IOKit (2.0.2 - 100150.120.2) <D1BA721B-4309-3CAF-81C7-595AEF32F6A9> /System/Library/Frameworks/IOKit.framework/Versions/A/IOKit\\n 457 |        0x195f38000 -        0x195f49feb  libsystem_notify.dylib (342) <45949DA9-086F-3E8C-9C8F-25B76BE65AEF> /usr/lib/system/libsystem_notify.dylib\\n 458 |        0x195f4a000 -        0x195fa49ff  libsandbox.1.dylib (2401.121.3) <97F3E747-64D0-3EEB-83C7-A48BB7B582F3> /usr/lib/libsandbox.1.dylib\\n 459 |        0x195fa5000 -        0x197436c7f  com.apple.AppKit (6.9 - 2575.60.5) <5D0DA1BD-412C-3ED8-84E9-40CA62FE7B42> /System/Library/Frameworks/AppKit.framework/Versions/C/AppKit\\n 460 |        0x197437000 -        0x1975e859f  com.apple.UIFoundation (1.0 - 970.5) <7F441048-8F2A-3FC8-B56F-9CBC78097B72> /System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation\\n 461 |        0x1975e9000 -        0x1975fecff  com.apple.UniformTypeIdentifiers (709 - 709) <1DE2937E-80C8-3993-A692-9CCD3F6D4749> /System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers\\n 462 |        0x1975ff000 -        0x19780e83f  com.apple.desktopservices (15.5 - 1732.6.1) <A1DBE863-ABFF-3848-A0DB-B28E414A8077> /System/Library/PrivateFrameworks/DesktopServicesPriv.framework/Versions/A/DesktopServicesPriv\\n 463 |        0x19786a000 -        0x197a9f57f  com.apple.CoreDuet (1.0 - 1) <8EB34F61-C1E1-31EC-BAEB-ECAA2D36B1DC> /System/Library/PrivateFrameworks/CoreDuet.framework/Versions/A/CoreDuet\\n 464 |        0x197aa0000 -        0x197b7029f  libboringssl.dylib (486.121.1) <0EFF1A5D-B1CB-3FC8-9C98-FE28C051811E> /usr/lib/libboringssl.dylib\\n 465 |        0x197b71000 -        0x197b713cf  libnetwork.dylib (4277.121.5) <E9A46C9D-0D6F-3D00-B2B7-1357B66EA6A0> /usr/lib/libnetwork.dylib\\n 466 |        0x197b72000 -        0x197f3c1bf  com.apple.CFNetwork (1.0 - 3826.500.131) <C2FD723B-4E94-3D53-97DD-6BF958117F98> /System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork\\n 467 |        0x197f3d000 -        0x197f57e8f  libsystem_networkextension.dylib (2063.120.19) <F8BFD14F-1B36-3492-8DD3-7300D44A13CC> /usr/lib/system/libsystem_networkextension.dylib\\n 468 |        0x197f58000 -        0x197f59067  libenergytrace.dylib (23) <F4C82817-3ED4-3A98-BA4C-A1A2A752F8FD> /usr/lib/libenergytrace.dylib\\n 469 |        0x197f5a000 -        0x197fd3dff  libMobileGestalt.dylib (1414.120.6) <31156393-9F0B-36A7-9410-F5AB6EEA1246> /usr/lib/libMobileGestalt.dylib\\n 470 |        0x197fd4000 -        0x197febfdb  libsystem_asl.dylib (402) <D6F3BCA9-3E5E-39BF-AB7B-EB88587ADC57> /usr/lib/system/libsystem_asl.dylib\\n 471 |        0x197fec000 -        0x19800e113  com.apple.TCC (1.0 - 1) <F7349BA9-18C1-37FA-A9D9-0972AC38F4D1> /System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC\\n 472 |        0x19800f000 -        0x1985342ff  com.apple.SkyLight (1.600.0 - 891.5) <3A386772-A7FE-3359-AAC4-1AE9C903CB49> /System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight\\n 473 |        0x198535000 -        0x198c64ebf  com.apple.CoreGraphics (2.0 - 1889.5.3) <7F9C410A-960E-39BA-B3D3-DF02110E796D> /System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics\\n 474 |        0x198c65000 -        0x198e1ad97  com.apple.ColorSync (4.13.0 - 3777.4.1) <CE8AEB19-17C1-3599-B10E-5BA8FC1ABC58> /System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync\\n 475 |        0x198e1b000 -        0x198e8739f  com.apple.HIServices (1.22 - 807.3) <9F96148E-F999-34D5-A7D6-BB6D8C75DAE1> /System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices\\n 476 |        0x198f86000 -        0x199199a9f  com.apple.Montreal (1.0 - 178) <B913B0D6-5942-3BEE-983A-D28C91C08C4D> /System/Library/PrivateFrameworks/Montreal.framework/Versions/A/Montreal\\n 477 |        0x19919a000 -        0x199293dbf  com.apple.NLP (1.0 - 233) <F900AFF2-8CD9-3AF9-998E-31A05CCC4008> /System/Library/PrivateFrameworks/NLP.framework/Versions/A/NLP\\n 478 |        0x199294000 -        0x1996eec9f  com.apple.CoreData (120 - 1447.3) <6E420D68-C8A1-391F-A6C9-6B5420519130> /System/Library/Frameworks/CoreData.framework/Versions/A/CoreData\\n 479 |        0x1996ef000 -        0x1997090ff  com.apple.ProtocolBuffer (1 - 303.24.9.12.1) <C5C38EA5-B58B-3C6E-BE96-144B6DD79CC8> /System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer\\n 480 |        0x19970a000 -        0x1998beccf  libsqlite3.dylib (360.1) <7950E4E9-74D1-3439-AA79-1CFC4965FD42> /usr/lib/libsqlite3.dylib\\n 481 |        0x1998bf000 -        0x19994369f  com.apple.Accounts (113 - 113) <AB07CF01-248F-3CBC-8FDE-20A3B59774D9> /System/Library/Frameworks/Accounts.framework/Versions/A/Accounts\\n 482 |        0x199944000 -        0x19995a05f  com.apple.commonutilities (8.0 - 900) <0357246C-3A8C-3557-A5C6-A68F17BE0335> /System/Library/PrivateFrameworks/CommonUtilities.framework/Versions/A/CommonUtilities\\n 483 |        0x19995b000 -        0x199a3fa7f  com.apple.BaseBoard (694.5.5 - 694.5.5) <547956C9-C8A6-3C3D-85D4-D0A58B668B07> /System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard\\n 484 |        0x199a40000 -        0x199aa81ff  com.apple.RunningBoardServices (1.0 - 961.100.17) <8C98CC59-FD4B-33B1-9088-5794C61CAE6D> /System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices\\n 485 |        0x199aa9000 -        0x199b1c787  com.apple.AE (944 - 944) <F452E7DE-ADC7-3044-975C-83E5ED97D1B8> /System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE\\n 486 |        0x199b1d000 -        0x199b2d23f  libdns_services.dylib (2600.120.12) <AD05B9C1-EE7B-3F34-8744-6E8C0CBF4271> /usr/lib/libdns_services.dylib\\n 487 |        0x199b2e000 -        0x199b363a7  libsystem_symptoms.dylib (2022.120.2) <E19D8302-3CFC-3767-99BB-C1B2930163E7> /usr/lib/system/libsystem_symptoms.dylib\\n 488 |        0x199b37000 -        0x19abd0f5f  com.apple.Network (1.0 - 4277.121.5) <6B6A0540-6732-3F51-B093-4A991690A1FE> /System/Library/Frameworks/Network.framework/Versions/A/Network\\n 489 |        0x19abd1000 -        0x19ac012ff  com.apple.analyticsd (1.0 - 1) <9CCC6142-F4CF-39EE-BD04-34D814B3A4F3> /System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics\\n 490 |        0x19ac02000 -        0x19ac038d3  libDiagnosticMessagesClient.dylib (113) <F65ACC81-A3E0-3EDA-9C5F-3683687C0CAD> /usr/lib/libDiagnosticMessagesClient.dylib\\n 491 |        0x19ac04000 -        0x19ac64e5f  com.apple.spotlight.metadata.utilities (1.0 - 2333.50.1) <7DB1610E-DF26-3F1B-9DFA-33850F4CEAD8> /System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities\\n 492 |        0x19ac65000 -        0x19acef13f  com.apple.Metadata (15.5 - 2333.50.1) <713FB6E3-7D7E-309F-B0DB-18CD679C93A8> /System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata\\n 493 |        0x19acf0000 -        0x19acf895b  com.apple.DiskArbitration (2.7 - 2.7) <22D3BA9E-2340-3454-B0F1-91BF5082EB79> /System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration\\n 494 |        0x19acf9000 -        0x19b108613  com.apple.vImage (8.1 - 611.100.8) <4C0BA193-DBE0-309B-9179-C2A586D99FC2> /System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage\\n 495 |        0x19b109000 -        0x19b4dcc7f  com.apple.QuartzCore (1.11 - 1156.18) <E0D9F378-DC87-33F8-93A5-3C62AD30EA19> /System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore\\n 496 |        0x19b4dd000 -        0x19b52eadf  libFontRegistry.dylib (391) <E64146A1-9F8C-34A8-845F-B0981E1809C5> /System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib\\n 497 |        0x19b52f000 -        0x19b687c3f  com.apple.coreui (2.1 - 918.3) <34C4AA66-74D2-3E8D-B0C5-574A68DBD73B> /System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI\\n 498 |        0x19b688000 -        0x19b7b805f  com.apple.ViewBridge (770.1 - 770.1) <449A7E66-751F-3ABE-B882-61D79606C698> /System/Library/PrivateFrameworks/ViewBridge.framework/Versions/A/ViewBridge\\n 499 |        0x19b7b9000 -        0x19b7c2d3f  com.apple.PerformanceAnalysis (1.385.14 - 385.14) <220AF15E-AEA6-3D7B-B92E-CF1F18795A8B> /System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis\\n 500 |        0x19b7c3000 -        0x19b7d0a7f  com.apple.OpenDirectory (15.5 - 643) <7034F925-2E05-3FC3-8644-E96CAA6A9BF7> /System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory\\n 501 |        0x19b7d1000 -        0x19b7ef053  com.apple.CFOpenDirectory (15.5 - 643) <648535BF-AEFD-336F-A04F-7B292237389E> /System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory\\n 502 |        0x19b7f0000 -        0x19b7fd83b  com.apple.CoreServices.FSEvents (1400.100.1 - 1400.100.1) <C68C8509-14F2-31AC-9C03-C3BC15ECF0B6> /System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents\\n 503 |        0x19b7fe000 -        0x19b82783f  com.apple.coreservices.SharedFileList (225 - 225) <336B275D-EFD3-3390-A3D6-A41A0F1124BC> /System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList\\n 504 |        0x19b828000 -        0x19b82b0af  libapp_launch_measurement.dylib (17) <4AE98D8B-DA17-3E7B-A698-3D7BF0B9B8B8> /usr/lib/libapp_launch_measurement.dylib\\n 505 |        0x19b82c000 -        0x19b87555f  com.apple.CoreAutoLayout (1.0 - 33) <954D325B-2A35-310F-A73F-A11840C81DD7> /System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout\\n 506 |        0x19b876000 -        0x19b95fa9b  libxml2.2.dylib (38.11) <DB30818F-8040-3C9A-8C17-FCD615FC3A2F> /usr/lib/libxml2.2.dylib\\n 507 |        0x19b960000 -        0x19b9c0ddf  com.apple.CoreVideo (1.8 - 682.6) <54956113-D920-3D30-B023-F4F1419E7441> /System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo\\n 508 |        0x19b9c1000 -        0x19b9c3e7f  com.apple.loginsupport (3.0 - 259.5.1) <BBC11F3B-0363-3371-A8D3-F2FAE12B6482> /System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport\\n 509 |        0x19b9c4000 -        0x19ba0001f  com.apple.aps.framework (4.0 - 4.0) <4BFC94F2-1A04-3BAE-91F2-67D722DE1D42> /System/Library/PrivateFrameworks/ApplePushService.framework/Versions/A/ApplePushService\\n 510 |        0x19ba01000 -        0x19ba2b57f  com.apple.UserManagement (1.0 - 1) <08FFD466-26A2-3E31-86CB-54A6683FFF7B> /System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement\\n 511 |        0x19ba2c000 -        0x19bde50bf  com.apple.cloudkit.CloudKit (2260.10.3 - 2260.10.3) <31C2A55D-6BE4-3CCD-813C-A5F316D9F61A> /System/Library/Frameworks/CloudKit.framework/Versions/A/CloudKit\\n 512 |        0x19bde6000 -        0x19bec559f  com.apple.CloudDocs (1.0 - 3437.120.20) <1BF44561-6A22-30AD-8781-44FD4193B42A> /System/Library/PrivateFrameworks/CloudDocs.framework/Versions/A/CloudDocs\\n 513 |        0x19bec6000 -        0x19c67941f  com.apple.CoreML (1.0 - 3405.2.1) <77EB6802-4A97-305B-BBE4-920ABCF5140B> /System/Library/Frameworks/CoreML.framework/Versions/A/CoreML\\n 514 |        0x19c67a000 -        0x19d21be7f  libwebrtc.dylib (621.********) <2967A0FB-B347-336C-A74D-B30977DF3189> /System/Library/Frameworks/WebKit.framework/Versions/A/Frameworks/WebCore.framework/Versions/A/Frameworks/libwebrtc.dylib\\n 515 |        0x19d21c000 -        0x19d36a21f  com.apple.corelocation (2964.0.4 - 2964.0.4) <DC62DBA1-C3C6-3BCE-94D6-11E89D508EC2> /System/Library/Frameworks/CoreLocation.framework/Versions/A/CoreLocation\\n 516 |        0x19d36b000 -        0x19d39b38f  libsystem_containermanager.dylib (689.100.6) <12FA61D3-0D2D-3205-B55D-D84717CC2817> /usr/lib/system/libsystem_containermanager.dylib\\n 517 |        0x19d39c000 -        0x19d3b4edf  com.apple.IOSurface (372.5.2 - 372.5.2) <2B84E9FB-3FCE-3520-AE9B-6211CD70AD88> /System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface\\n 518 |        0x19d3b5000 -        0x19d3bee9f  com.apple.IOAccelerator (485 - 485) <C00E3028-E7A9-3A9B-B1B1-45F55F8FEC3F> /System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator\\n 519 |        0x19d3bf000 -        0x19d63187f  com.apple.Metal (368.12 - 368.12) <FFD1877F-0A94-301E-A18B-4F03C319646C> /System/Library/Frameworks/Metal.framework/Versions/A/Metal\\n 520 |        0x19d632000 -        0x19d659ddf  com.apple.audio.caulk (1.0 - 181.503) <5CE1B98F-C512-379D-9A42-5CCE5923BF9D> /System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk\\n 521 |        0x19d65a000 -        0x19d7b531f  com.apple.CoreMedia (1.0 - 3225.7.1) <BE820B19-8395-3C72-BF60-CA73C3245ADA> /System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia\\n 522 |        0x19d7b6000 -        0x19da881bf  libFontParser.dylib (423.5.0.1) <908A9ABB-42EE-3477-9FFD-E691CEBB0531> /System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib\\n 523 |        0x19da89000 -        0x19dd8ffdf  com.apple.HIToolbox (2.1.1 - 1220.2) <9286E29F-FCEE-31D0-ACEA-2842EA23BEDF> /System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox\\n 524 |        0x19dd90000 -        0x19dda44bf  com.apple.framework.DFRFoundation (1.0 - 287) <FE73ADA0-D14E-363A-A36A-216FF5100811> /System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation\\n 525 |        0x19dda5000 -        0x19ddaa35f  com.apple.dt.XCTTargetBootstrap (15.5 - 23790) <89692B40-C7E8-349F-A208-464B4F240527> /System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap\\n 526 |        0x19ddab000 -        0x19dde56ff  com.apple.CoreSVG (1.0 - 341) <FB0EFC3E-3862-3641-9039-5C8A8F628FC4> /System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG\\n 527 |        0x19dde6000 -        0x19e16491f  com.apple.ImageIO (3.3.0 - 2661.5.2) <CE22A5A5-CEAF-3C7D-857C-7A0B9B4187C7> /System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO\\n 528 |        0x19e165000 -        0x19e51f67f  com.apple.CoreImage (19.0.0 - 1510.120.2) <FBED68CD-1334-378E-9B96-2FF2BFFF0017> /System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage\\n 529 |        0x19e520000 -        0x19e5cfd7f  com.apple.MetalPerformanceShaders.MPSCore (1.0 - 1) <5CCE6E9A-C18A-384E-8DE6-AAB8D776D888> /System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore\\n 530 |        0x19e5d0000 -        0x19e5d45df  libsystem_configuration.dylib (1351.120.3) <A9CC0F35-6BB6-3E73-8496-8F14D7AC6F24> /usr/lib/system/libsystem_configuration.dylib\\n 531 |        0x19e5d5000 -        0x19e5db47f  libsystem_sandbox.dylib (2401.121.3) <99E69314-53F0-3D91-A3C0-CF0DA7BAE494> /usr/lib/system/libsystem_sandbox.dylib\\n 532 |        0x19e5dc000 -        0x19e5dd17f  com.apple.AggregateDictionary (1.0 - 1) <09BA78A3-8DC5-346F-AD8B-CB9A34F15E0D> /System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary\\n 533 |        0x19e5de000 -        0x19e5e259b  com.apple.AppleSystemInfo (3.1.5 - 3.1.5) <B188EE31-D3DA-3050-838F-DE451E3114B6> /System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo\\n 534 |        0x19e5e3000 -        0x19e5e444f  liblangid.dylib (140) <F5E0B083-735F-3AE7-A162-BCC2408EBB0A> /usr/lib/liblangid.dylib\\n 535 |        0x19e5e5000 -        0x19e701eff  com.apple.CoreNLP (1.0 - 313) <408DD4AA-CF13-3D43-8221-8287848396EB> /System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP\\n 536 |        0x19e702000 -        0x19e70855f  com.apple.LinguisticData (1.0 - 483.10) <4F256AF6-A7AA-3027-BE0C-014DB652B961> /System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData\\n 537 |        0x19e709000 -        0x19f1b5bb7  libBNNS.dylib (1497.120.5) <2A349F1E-AC49-30A4-BA8F-493A581B7EB7> /System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib\\n 538 |        0x19f1b6000 -        0x19f2a7cff  libvDSP.dylib (1068.100.12) <9501D6AF-5FE8-3820-B0DA-5F500DD1483E> /System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib\\n 539 |        0x19f2a8000 -        0x19f2da59f  com.apple.CoreEmoji (1.0 - 239.218) <D520A89E-D3F3-3DAB-9A3E-003D3F1886DB> /System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji\\n 540 |        0x19f2db000 -        0x19f2eb7e7  com.apple.IOMobileFramebuffer (343.0.0 - 343.0.0) <7EEF8FE9-7F36-3B01-AB29-A7BAD6337A00> /System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer\\n 541 |        0x19f2ec000 -        0x19f36ba9f  com.apple.framework.CoreWLAN (16.0 - 1657) <BC93167C-20B7-3380-807D-47AEC8BA153D> /System/Library/Frameworks/CoreWLAN.framework/Versions/A/CoreWLAN\\n 542 |        0x19f36c000 -        0x19f4e6bbf  com.apple.CoreUtils (7.8 - 780.15) <0D6961C9-23EA-303D-A630-9F0F770E65C6> /System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils\\n 543 |        0x19f4e7000 -        0x19f503ddf  com.apple.MobileKeyBag (2.0 - 1.0) <530BFB39-CFC6-30BB-BC3E-3B8B9CFAF492> /System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag\\n 544 |        0x19f504000 -        0x19f51215f  com.apple.AssertionServices (1.0 - 961.100.17) <A693BBC1-C5FD-3071-91FE-C5ABB666F607> /System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices\\n 545 |        0x19f513000 -        0x19f5a19df  com.apple.securityfoundation (6.0 - 55290) <9A65AE37-5B5C-3368-A0BC-F77E293AA429> /System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation\\n 546 |        0x19f5a2000 -        0x19f5d0c7f  com.apple.coreservices.BackgroundTaskManagement (1.0 - 104) <939E6E27-48E7-34E2-95B6-ACD5456E7100> /System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement\\n 547 |        0x19f5d1000 -        0x19f5db85f  com.apple.xpc.ServiceManagement (1.0 - 1) <C5991041-6268-34D9-9233-2FC34DFA7D82> /System/Library/Frameworks/ServiceManagement.framework/Versions/A/ServiceManagement\\n 548 |        0x19f5dc000 -        0x19f5defe3  libquarantine.dylib (181.120.2) <EC0732A5-DAEE-37B9-B705-F6BF5AC6DA24> /usr/lib/system/libquarantine.dylib\\n 549 |        0x19f5df000 -        0x19f5ea32f  libCheckFix.dylib (33) <2DDAF91E-ECC3-30CC-9839-B2844A46FFC6> /usr/lib/libCheckFix.dylib\\n 550 |        0x19f5eb000 -        0x19f60240f  libcoretls.dylib (186) <64D9C0F7-8B44-38F0-BBF8-BBEBF0ED0ADC> /usr/lib/libcoretls.dylib\\n 551 |        0x19f603000 -        0x19f61426b  libbsm.0.dylib (89) <32F464FF-2342-3018-9B7D-090258389BA4> /usr/lib/libbsm.0.dylib\\n 552 |        0x19f615000 -        0x19f67465b  libmecab.dylib (1093.501) <3890BDB0-1E77-3A86-843F-FC5AC55A3751> /usr/lib/libmecab.dylib\\n 553 |        0x19f675000 -        0x19f67744b  libgermantok.dylib (31) <39879501-C5A7-3018-8445-5C004C1A6600> /usr/lib/libgermantok.dylib\\n 554 |        0x19f678000 -        0x19f68ca5f  libLinearAlgebra.dylib (1545) <21D4D32D-1675-3B98-8368-679213DFD36C> /System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib\\n 555 |        0x19f68d000 -        0x19f8d819f  com.apple.MetalPerformanceShaders.MPSNeuralNetwork (1.0 - 1) <840BE1AC-7611-3296-A1CB-885C5E2E188F> /System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork\\n 556 |        0x19f8d9000 -        0x19f92d77f  com.apple.MetalPerformanceShaders.MPSRayIntersector (1.0 - 1) <1055FBD7-4252-3CA2-BC7C-01D4C63EFA13> /System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector\\n 557 |        0x19f92e000 -        0x19fac313f  com.apple.MLCompute (1.0 - 1) <81C34B47-064A-3FC2-9CC7-60C438906B80> /System/Library/Frameworks/MLCompute.framework/Versions/A/MLCompute\\n 558 |        0x19fac4000 -        0x19faf51ff  com.apple.MetalPerformanceShaders.MPSMatrix (1.0 - 1) <7688A242-172E-3A41-8CE3-4F42A1A6A9D9> /System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix\\n 559 |        0x19faf6000 -        0x19fc3d23f  com.apple.MetalPerformanceShaders.MPSNDArray (1.0 - 1) <82E4F895-5F79-3108-938B-8ADA4460258C> /System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray\\n 560 |        0x19fc3e000 -        0x19fcd20bf  com.apple.MetalPerformanceShaders.MPSImage (1.0 - 1) <7380A128-16F8-3664-805F-224FB6DDC20D> /System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage\\n 561 |        0x19fcd3000 -        0x19fcde16b  com.apple.AppleFSCompression (170 - 1.0) <0592EB70-E012-3D0A-A6B7-6EDBC377F2B7> /System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression\\n 562 |        0x19fcdf000 -        0x19fceb56b  libbz2.1.0.dylib (46) <6B80002B-B5B7-364A-9D2E-8F87546BF5AC> /usr/lib/libbz2.1.0.dylib\\n 563 |        0x19fcec000 -        0x19fcf27a3  libsystem_coreservices.dylib (178) <4A9BA38A-4D47-3713-A839-DA773D8A8B54> /usr/lib/system/libsystem_coreservices.dylib\\n 564 |        0x19fcf3000 -        0x19fd2529f  com.apple.CoreServices.OSServices (1141.1 - 1141.1) <0F67EC13-8473-3A76-BBB3-F2D7747205A6> /System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices\\n 565 |        0x19fd26000 -        0x19ffc995f  com.apple.AuthKit (1.0 - 1) <F173F4BE-5227-31D9-A1FE-402203C89A87> /System/Library/PrivateFrameworks/AuthKit.framework/Versions/A/AuthKit\\n 566 |        0x19ffca000 -        0x1a001721f  com.apple.UserNotifications (1.0 - 579.6.4) <A7C9C69F-6624-3A80-8CB4-DF17C82613C4> /System/Library/Frameworks/UserNotifications.framework/Versions/A/UserNotifications\\n 567 |        0x1a0018000 -        0x1a015aa3f  com.apple.CoreSpotlight (1.0 - 2333.50.1) <462477A2-628F-34CA-867F-0DBA227F0F58> /System/Library/Frameworks/CoreSpotlight.framework/Versions/A/CoreSpotlight\\n 568 |        0x1a015b000 -        0x1a0169e97  libz.1.dylib (98) <C19E381C-97B9-364E-87A3-14F26E16169C> /usr/lib/libz.1.dylib\\n 569 |        0x1a016a000 -        0x1a01a72b7  libsystem_m.dylib (3291.100.4) <F3A77909-62A9-3E86-9C40-4F0DE713F490> /usr/lib/system/libsystem_m.dylib\\n 570 |        0x1a01a8000 -        0x1a01a8c9b  libcharset.1.dylib (109.100.2) <87BE8D6D-8604-38B2-95DC-20B69B60D27A> /usr/lib/libcharset.1.dylib\\n 571 |        0x1a01a9000 -        0x1a01ac4e7  libmacho.dylib (1024.3) <4EAD3AF2-CDB9-376A-9A67-6F0037E60D1D> /usr/lib/system/libmacho.dylib\\n 572 |        0x1a01ad000 -        0x1a01c5dfb  libkxld.dylib (11417.121.6) <93182D6C-3E08-3FE8-84A3-3618F15CC634> /usr/lib/system/libkxld.dylib\\n 573 |        0x1a01c6000 -        0x1a01d35ef  libcommonCrypto.dylib (600035) <A40B703D-F173-391D-9AC6-910F78158DA5> /usr/lib/system/libcommonCrypto.dylib\\n 574 |        0x1a01d4000 -        0x1a01ddad7  libunwind.dylib (1900.125) <0E7B8D5B-31BA-3BE8-BF25-E10666EDE277> /usr/lib/system/libunwind.dylib\\n 575 |        0x1a01de000 -        0x1a01e5109  liboah.dylib (349) <EEB10206-44AA-3304-B4BA-1EE3D29854AF> /usr/lib/liboah.dylib\\n 576 |        0x1a01e6000 -        0x1a01f05bf  libcopyfile.dylib (221.121.1) <46F69CD7-7F96-371B-8296-568D482A6411> /usr/lib/system/libcopyfile.dylib\\n 577 |        0x1a01f1000 -        0x1a01f498f  libcompiler_rt.dylib (103.3) <AF97F2CC-03A4-31F0-8C72-587BEFB97632> /usr/lib/system/libcompiler_rt.dylib\\n 578 |        0x1a01f5000 -        0x1a01f9a1b  libsystem_collections.dylib (1698.100.8) <03421A5A-565F-39F2-B27E-15EBB24AF62D> /usr/lib/system/libsystem_collections.dylib\\n 579 |        0x1a01fa000 -        0x1a01fd2c7  libsystem_secinit.dylib (153.100.10) <9EAF2187-33C8-3FFD-8B01-6EB7C76891EB> /usr/lib/system/libsystem_secinit.dylib\\n 580 |        0x1a01fe000 -        0x1a0200b6f  libremovefile.dylib (81) <DB9BEFA1-2FB7-3082-8733-97D3322278AA> /usr/lib/system/libremovefile.dylib\\n 581 |        0x1a0201000 -        0x1a0201f27  libkeymgr.dylib (31) <0C24AE24-7B28-3C0A-A6F9-42B21C3D4408> /usr/lib/system/libkeymgr.dylib\\n 582 |        0x1a0202000 -        0x1a020ad57  libsystem_dnssd.dylib (2600.120.12) <82C69999-6EDE-35A7-A08E-79714AA45517> /usr/lib/system/libsystem_dnssd.dylib\\n 583 |        0x1a020b000 -        0x1a02100b3  libcache.dylib (95) <ACCBADB6-3349-3020-9E59-BED2AA2DB0B1> /usr/lib/system/libcache.dylib\\n 584 |        0x1a0211000 -        0x1a0212ca3  libSystem.B.dylib (1351) <F7E5BDDC-FB12-33EF-921F-C93FEF0D41D7> /usr/lib/libSystem.B.dylib\\n 585 |        0x1a0213000 -        0x1a021503f  libfakelink.dylib (5) <A9803649-3512-3308-8451-CBFD929E623B> /usr/lib/libfakelink.dylib\\n 586 |        0x1a0216000 -        0x1a0216a63  com.apple.SoftLinking (1.0 - 65) <4ECD014D-EBE4-3BD0-B9A2-3707DD9990A9> /System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking\\n 587 |        0x1a024d000 -        0x1a02541eb  libiconv.2.dylib (109.100.2) <76321CF7-68AB-329D-AB5A-CC542D166316> /usr/lib/libiconv.2.dylib\\n 588 |        0x1a0255000 -        0x1a0267587  libcmph.dylib (9) <C7A341BE-F4BB-3837-AA5D-703420F789B7> /usr/lib/libcmph.dylib\\n 589 |        0x1a0268000 -        0x1a035f1d3  libarchive.2.dylib (149) <F3127C41-7701-3BD3-A5E1-187954E89611> /usr/lib/libarchive.2.dylib\\n 590 |        0x1a0360000 -        0x1a03c757f  com.apple.SearchKit (1.4.2 - 1.4.2) <35E0660B-E5FD-3FEA-9316-4FB9D1B5BD22> /System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit\\n 591 |        0x1a03c8000 -        0x1a03cfff7  libThaiTokenizer.dylib (28) <6554DF35-7836-32AC-8664-0DEE20384EFA> /usr/lib/libThaiTokenizer.dylib\\n 592 |        0x1a03d0000 -        0x1a03f4187  com.apple.applesauce (1.0 - 16.62) <472C17B7-B578-3DE4-8B67-6947E970075E> /System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce\\n 593 |        0x1a03f5000 -        0x1a040e9ff  libapple_nghttp2.dylib (29) <56F3FE25-CEFC-3653-B27D-9FB82E46DC56> /usr/lib/libapple_nghttp2.dylib\\n 594 |        0x1a040f000 -        0x1a0435ac3  libSparseBLAS.dylib (1545) <5A30E704-9AA0-32BE-B9A3-3D2532E05768> /System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib\\n 595 |        0x1a0436000 -        0x1a043759f  com.apple.MetalPerformanceShaders.MetalPerformanceShaders (1.0 - 1) <A605C0CA-3CBC-3E19-BE93-1344BA67486B> /System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders\\n 596 |        0x1a0438000 -        0x1a043df7f  libpam.2.dylib (34) <A2954E78-34C2-3CDA-93BE-1B45B66A8B3B> /usr/lib/libpam.2.dylib\\n 597 |        0x1a043e000 -        0x1a050e84f  libcompression.dylib (181.100.1) <CD13CC52-05B3-3E5A-B147-820602B94231> /usr/lib/libcompression.dylib\\n 598 |        0x1a050f000 -        0x1a05131e7  libQuadrature.dylib (8) <A35AF27B-05F3-3244-B378-15465AD04D6E> /System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib\\n 599 |        0x1a0514000 -        0x1a17752df  libLAPACK.dylib (1545) <DDA3AB7F-4CD1-34BB-B780-F52116E8A8F9> /System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib\\n 600 |        0x1a1776000 -        0x1a17cc07f  com.apple.DictionaryServices (1.2 - 368.203) <E37D4D5A-DF97-35CA-AD4E-014AFD05A503> /System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices\\n 601 |        0x1a17cd000 -        0x1a17eb8bf  liblzma.5.dylib (20) <BC19AE1E-213F-32B3-9915-58EF7A3710F7> /usr/lib/liblzma.5.dylib\\n 602 |        0x1a17ec000 -        0x1a17ed867  libcoretls_cfhelpers.dylib (186) <C8190B89-A2AA-3FF3-B1D0-567A40E78A89> /usr/lib/libcoretls_cfhelpers.dylib\\n 603 |        0x1a17ee000 -        0x1a185f433  com.apple.APFS (2332.120.31 - 2332.120.31) <D3C0DC53-6325-35DC-A227-CA07432775EF> /System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS\\n 604 |        0x1a1860000 -        0x1a186e68b  libxar.1.dylib (501) <51613D9F-D330-376F-AF1A-685CBE94AFFC> /usr/lib/libxar.1.dylib\\n 605 |        0x1a186f000 -        0x1a18727fb  libutil.dylib (72) <38D6A13C-D2C5-3067-8200-5988592D7D9E> /usr/lib/libutil.dylib\\n 606 |        0x1a1873000 -        0x1a189e46f  libxslt.1.dylib (21.7) <6219F351-EB84-38FB-8973-CB6C916E634B> /usr/lib/libxslt.1.dylib\\n 607 |        0x1a189f000 -        0x1a18a6077  libChineseTokenizer.dylib (44) <42C52902-C16B-3A7F-8F41-1DA4C91D386B> /usr/lib/libChineseTokenizer.dylib\\n 608 |        0x1a18a7000 -        0x1a19207c7  libvMisc.dylib (1068.100.12) <94836789-C8C0-3FDF-A3FD-CDAD2DEE930F> /System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib\\n 609 |        0x1a1921000 -        0x1a19b13bf  libate.dylib (3.0.9) <63C8A125-E17D-36AC-872B-CC710AFFAF94> /usr/lib/libate.dylib\\n 610 |        0x1a19b2000 -        0x1a19ba9eb  libIOReport.dylib (107) <67F15B69-8212-39C0-AE6A-FE69645CAD2F> /usr/lib/libIOReport.dylib\\n 611 |        0x1a19bb000 -        0x1a19ce91f  com.apple.CrashReporterSupport (10.13 - 15131) <EDDE3131-FB13-3CE4-B536-8DEAC7BB3024> /System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport\\n 612 |        0x1a19cf000 -        0x1a19eee9f  com.apple.AppSSOCore (1.0 - 1) <3150CD52-4677-3FCB-821C-2CE3BEDA8987> /System/Library/PrivateFrameworks/AppSSOCore.framework/Versions/A/AppSSOCore\\n 613 |        0x1a19ef000 -        0x1a1aeac3f  com.apple.CVNLP (1.0 - 119) <654331AC-1200-3232-A72B-D3D81E741374> /System/Library/PrivateFrameworks/CVNLP.framework/Versions/A/CVNLP\\n 614 |        0x1a1aeb000 -        0x1a1b0ebdf  com.apple.SharedWebCredentials (1001 - 1021.2) <5A29AB46-6DBE-3BF2-840D-AD51CBD30A4B> /System/Library/PrivateFrameworks/SharedWebCredentials.framework/Versions/A/SharedWebCredentials\\n 615 |        0x1a1b0f000 -        0x1a1b504bf  com.apple.pluginkit.framework (1.0 - 1) <ACF9CC99-6FEE-31FD-B778-F3C2908B0A71> /System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit\\n 616 |        0x1a1b51000 -        0x1a1b5861b  libMatch.1.dylib (48) <7AC80737-9688-3217-9592-AB902F2C5CAC> /usr/lib/libMatch.1.dylib\\n 617 |        0x1a1b59000 -        0x1a1bc6947  libCoreStorage.dylib (566) <9A8F6A4F-9A6D-3C64-9CC9-519405E04816> /usr/lib/libCoreStorage.dylib\\n 618 |        0x1a1bc7000 -        0x1a1c0fef7  com.apple.AppleVAFramework (6.2.10 - 6.2.10) <80114CA5-F586-3D4E-9496-57399E9AD634> /System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA\\n 619 |        0x1a1c10000 -        0x1a1c2a2a7  libexpat.1.dylib (43) <26BE1018-1DEB-37F9-93A2-97D8218BE67E> /usr/lib/libexpat.1.dylib\\n 620 |        0x1a1c2b000 -        0x1a1c34bab  libheimdal-asn1.dylib (693.100.10) <265FE068-F681-3DD4-A60E-7DAD8E14C337> /usr/lib/libheimdal-asn1.dylib\\n 621 |        0x1a1c35000 -        0x1a1c8147f  com.apple.IconFoundation (493 - 493) <A1DD5B20-7D47-3BF7-BEBE-0CE2F0DD6E53> /System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation\\n 622 |        0x1a1c82000 -        0x1a1d1485f  com.apple.IconServices (493 - 493) <97A544F0-1578-3F47-9B7B-62FC73D2AB30> /System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices\\n 623 |        0x1a1d15000 -        0x1a1dbb8df  com.apple.MediaExperience (1.0 - 1) <6B80B97B-6A61-3341-9086-DAB89B024265> /System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience\\n 624 |        0x1a1dbc000 -        0x1a1de74bf  com.apple.persistentconnection (1.0 - 1.0) <E2BCEE20-8F34-34C7-8CF8-68A4117CB9D9> /System/Library/PrivateFrameworks/PersistentConnection.framework/Versions/A/PersistentConnection\\n 625 |        0x1a1de8000 -        0x1a1df3eff  com.apple.GraphVisualizer (1.0 - 120.1) <E507FFA3-B408-343F-888E-DF3268453999> /System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer\\n 626 |        0x1a1df4000 -        0x1a1e3479f  com.apple.OTSVG (1.0 - 844.5.0.6) <D32A7A49-F582-3E70-8D6F-8E396F67AE95> /System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG\\n 627 |        0x1a1e35000 -        0x1a1e3ea9f  com.apple.xpc.AppServerSupport (1.0 - 2894.121.3) <B63CB4A9-D25A-341C-802A-F3C94411980F> /System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport\\n 628 |        0x1a1e3f000 -        0x1a1e4567f  libspindump.dylib (383.8) <4B644844-109C-3027-9F2F-740975D9C09E> /usr/lib/libspindump.dylib\\n 629 |        0x1a1e46000 -        0x1a1f06c3f  com.apple.Heimdal (4.0 - 2.0) <94254441-E7C9-3009-A24E-FE9A881DE469> /System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal\\n 630 |        0x1a1f07000 -        0x1a1f2a3ff  com.apple.login (3.0 - 3.0) <864D22D0-4D70-3E6A-B599-6F6B38274A62> /System/Library/PrivateFrameworks/login.framework/Versions/A/login\\n 631 |        0x1a1f2b000 -        0x1a204a49f  com.apple.corebrightness (1.0 - 1) <4A172553-0727-3945-8B44-B34267B4ADC7> /System/Library/PrivateFrameworks/CoreBrightness.framework/Versions/A/CoreBrightness\\n 632 |        0x1a20f7000 -        0x1a20f8207  libodfde.dylib (26) <9548FEFC-ABB8-3C69-BA47-7BE2B53119E6> /usr/lib/libodfde.dylib\\n 633 |        0x1a20f9000 -        0x1a216e573  com.apple.bom (14.0 - 269) <F98BE4B9-C4CC-34DF-A384-B8B0F7B1C6DA> /System/Library/PrivateFrameworks/Bom.framework/Versions/A/Bom\\n 634 |        0x1a216f000 -        0x1a21b437f  com.apple.AppleJPEG (1.0 - 1) <0CF21CA0-338D-3B02-BD85-F250BC5B7FBB> /System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG\\n 635 |        0x1a21b5000 -        0x1a22a525f  libJP2.dylib (2661.5.2) <89897D69-1C24-354E-932E-A126031F35FE> /System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib\\n 636 |        0x1a22a6000 -        0x1a22a7d53  com.apple.WatchdogClient.framework (1.0 - 299.120.2) <53AE78CB-8936-35C4-AA71-B17CF10C05ED> /System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient\\n 637 |        0x1a22a8000 -        0x1a22ed29f  com.apple.MultitouchSupport.framework (8440.1 - 8440.1) <25CEB48E-3F34-3BBF-8C06-13ED70A3A23F> /System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport\\n 638 |        0x1a22ee000 -        0x1a2763edf  com.apple.VideoToolbox (1.0 - 3225.7.1) <DEE7AFB2-28C4-35A5-B8FB-34AD8290B68F> /System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox\\n 639 |        0x1a2764000 -        0x1a279963f  libAudioToolboxUtility.dylib (1456.607) <A50EFBCC-C530-33D3-817D-C921BB01E811> /usr/lib/libAudioToolboxUtility.dylib\\n 640 |        0x1a279a000 -        0x1a27c44c7  libPng.dylib (2661.5.2) <2CF1A27F-ABE4-3254-B545-0D3BFA3A4149> /System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib\\n 641 |        0x1a27c5000 -        0x1a2827083  libTIFF.dylib (2661.5.2) <32E1DAC3-F545-3233-90D8-C1F769CC747D> /System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib\\n 642 |        0x1a2828000 -        0x1a2847983  com.apple.IOPresentment (67 - 67) <23623389-69A2-371B-8C8C-5366C9F97390> /System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment\\n 643 |        0x1a2848000 -        0x1a284c7e3  com.apple.GPUWrangler (8.1.10 - 8.1.10) <D69857E9-ED00-3AE4-B82D-7C511190D070> /System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler\\n 644 |        0x1a284d000 -        0x1a284f94b  libRadiance.dylib (2661.5.2) <D1BE85E3-9F3E-3FD0-BFCE-F08CECBFBFAC> /System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib\\n 645 |        0x1a2850000 -        0x1a285564b  com.apple.DSExternalDisplay (3.1 - 380) <7C249A82-3F0D-335D-BD26-DAD7CD41D9DC> /System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay\\n 646 |        0x1a2856000 -        0x1a2880e87  libJPEG.dylib (2661.5.2) <747B84C2-7F22-302A-99E1-4364B6509DCD> /System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib\\n 647 |        0x1a2881000 -        0x1a28ae6e7  com.apple.ATSUI (1.0 - 1) <1BF9C26E-9BA1-381B-AC50-1F019F4F2FDF> /System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI\\n 648 |        0x1a28af000 -        0x1a28b47a3  libGIF.dylib (2661.5.2) <721CA595-45C8-3EA5-BB4B-AA4BCECB2EAC> /System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib\\n 649 |        0x1a28b5000 -        0x1a28c3d1f  com.apple.CMCaptureCore (1.0 - 587.*********) <75241CF8-9050-35E5-AEC2-F787255D97BF> /System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore\\n 650 |        0x1a28c4000 -        0x1a293a5bf  com.apple.print.framework.PrintCore (19 - 592.3) <13938405-8473-32DF-89A3-7836076924D9> /System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore\\n 651 |        0x1a293b000 -        0x1a29da0bf  com.apple.TextureIO (3.10.11 - 3.10.11) <F3EBA20D-CCD8-3A2C-8C02-D50A44C85028> /System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO\\n 652 |        0x1a29db000 -        0x1a2ce2c3f  com.apple.InternationalSupport (1.0 - 74) <0B98FE9C-D70B-3CB1-A5E6-B3871CD4107C> /System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport\\n 653 |        0x1a2ce3000 -        0x1a2d3161f  com.apple.datadetectorscore (8.0 - 801.2) <5E28404E-CEF6-35A4-A648-F1F6D83A21B7> /System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore\\n 654 |        0x1a2d32000 -        0x1a2d9fb9f  com.apple.UserActivity (551 - 551) <0633DB9A-AE01-3CC2-B0F8-6E65D9833DB4> /System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity\\n 655 |        0x1a2da0000 -        0x1a36dccdf  com.apple.MediaToolbox (1.0 - 3225.7.1) <B8D65F6E-8CBB-39AB-87E8-34BE27D83D32> /System/Library/Frameworks/MediaToolbox.framework/Versions/A/MediaToolbox\\n 656 |        0x1a36dd000 -        0x1a374d37b  libusrtcp.dylib (4277.121.5) <A6D7EE2A-1D10-3231-A668-DD0266F0C860> /usr/lib/libusrtcp.dylib\\n 657 |        0x1a374e000 -        0x1a3cbce3f  libswiftCore.dylib (6.1.2 - *******.2) <26919DE5-6960-3FDC-BBDC-4E6C8C8470B1> /usr/lib/swift/libswiftCore.dylib\\n 658 |        0x1a3cbd000 -        0x1a3d36abf  com.apple.imfoundation (10.0 - 1000) <D1C2407F-80EA-3645-B6F6-C78929357E8C> /System/Library/PrivateFrameworks/IMFoundation.framework/Versions/A/IMFoundation\\n 659 |        0x1a3d37000 -        0x1a3d7121f  com.apple.locationsupport (2964.0.4 - 2964.0.4) <D83D605E-9E71-39CE-9042-530CDCB149BE> /System/Library/PrivateFrameworks/LocationSupport.framework/Versions/A/LocationSupport\\n 660 |        0x1a3d72000 -        0x1a3db8e3f  libSessionUtility.dylib (321.603) <27635C71-3B00-3C6F-A0D9-433F579C9634> /System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib\\n 661 |        0x1a3db9000 -        0x1a3f6337f  com.apple.audio.toolbox.AudioToolbox (1.14 - 1.14) <C86E940B-25C8-3A06-BEFC-80F22121A030> /System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox\\n 662 |        0x1a3f64000 -        0x1a3fc0c7f  com.apple.audio.AudioSession (1.0 - 321.603) <4E45A79C-19D7-33D7-A729-0B113017F8A3> /System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession\\n 663 |        0x1a3fc1000 -        0x1a3fd989f  libAudioStatistics.dylib (235.601) <0E53702A-D736-3DF5-A0E3-7020E7933642> /usr/lib/libAudioStatistics.dylib\\n 664 |        0x1a3fda000 -        0x1a400723f  com.apple.speech.synthesis.framework (9.2.22 - 9.2.22) <FC61324A-C5F6-3A51-910C-984CB49F16BE> /System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis\\n 665 |        0x1a4008000 -        0x1a408587f  com.apple.ApplicationServices.ATS (377 - 580.3.0.1) <F286A8F9-2E84-3DEB-A332-9767313BB7CB> /System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS\\n 666 |        0x1a4086000 -        0x1a40a2003  libresolv.9.dylib (91) <9904E580-1240-38DF-9D3C-3B28442F04A2> /usr/lib/libresolv.9.dylib\\n 667 |        0x1a40a3000 -        0x1a40b56a7  libsasl2.2.dylib (215) <FCC77905-EA0D-3330-B97C-25C94B9ED4CC> /usr/lib/libsasl2.2.dylib\\n 668 |        0x1a40b6000 -        0x1a40c30df  com.apple.multiverse (1.0 - 115) <82554613-573D-3134-BFCE-E901BC5539A6> /System/Library/PrivateFrameworks/MultiverseSupport.framework/Versions/A/MultiverseSupport\\n 669 |        0x1a40c4000 -        0x1a412b46f  libParallelCompression.dylib (423.100.6) <F6384537-AB97-392B-887F-870EF9607E2A> /usr/lib/libParallelCompression.dylib\\n 670 |        0x1a412c000 -        0x1a416411f  com.apple.securityinterface (10.0 - 55206) <9067A92B-3CF8-3E22-A54E-932C712C5233> /System/Library/Frameworks/SecurityInterface.framework/Versions/A/SecurityInterface\\n 671 |        0x1a4165000 -        0x1a41970bf  com.apple.CoreFollowUp-OSX (1.0 - 256) <EBEF8370-C1F4-35BD-A8F7-322570D07250> /System/Library/PrivateFrameworks/CoreFollowUp.framework/Versions/A/CoreFollowUp\\n 672 |        0x1a4198000 -        0x1a42a41df  com.apple.CoreMediaIO (1000.0 - 5590.122.2.0.1) <4AC9AF0C-006D-3587-A054-3183074FB269> /System/Library/Frameworks/CoreMediaIO.framework/Versions/A/CoreMediaIO\\n 673 |        0x1a42a5000 -        0x1a4385a0f  libSMC.dylib (38) <8914C435-86B7-3F2C-B248-ED6A92E4F1F2> /usr/lib/libSMC.dylib\\n 674 |        0x1a4386000 -        0x1a43e407f  libcups.2.dylib (513.7) <EB4D6EDE-0BCE-3B5C-8398-784F17B2471D> /usr/lib/libcups.2.dylib\\n 675 |        0x1a43e5000 -        0x1a43f1a17  com.apple.NetAuth (6.2 - 6.2) <A848CDB5-EB92-3A52-8904-D77C31FBCDEA> /System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth\\n 676 |        0x1a43f2000 -        0x1a43faddf  com.apple.ColorSyncLegacy (4.13.0 - 1) <887061F8-AF71-30C8-99C8-87EF47D81D09> /System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy\\n 677 |        0x1a43fb000 -        0x1a4403d6f  com.apple.QD (4.0 - 451) <DE99DCC4-B2AE-3DC0-80D1-6D126BA11514> /System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD\\n 678 |        0x1a4404000 -        0x1a441149f  com.apple.perfdata (1.0 - 121) <50AD3529-88B4-3DB3-99E1-BB40FD901592> /System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata\\n 679 |        0x1a4412000 -        0x1a441fcdf  libperfcheck.dylib (46) <B392A2EF-BEF8-33A0-8E9E-AB8FB4569184> /usr/lib/libperfcheck.dylib\\n 680 |        0x1a4420000 -        0x1a4430fbf  com.apple.Kerberos (3.0 - 1) <5B2718D2-F6DA-3748-9F9F-6D841981710E> /System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos\\n 681 |        0x1a4431000 -        0x1a4482a17  com.apple.GSS (4.0 - 2.0) <5AA59A5A-A393-3A35-AEBF-3E9A8B12EED4> /System/Library/Frameworks/GSS.framework/Versions/A/GSS\\n 682 |        0x1a4483000 -        0x1a4493da7  com.apple.CommonAuth (4.0 - 2.0) <2604B96B-953F-36A1-962F-C83BFB44DB32> /System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth\\n 683 |        0x1a4494000 -        0x1a456811f  com.apple.MobileAssets (1.0 - 1487.120.62) <5089A229-7373-3239-86F4-6A857A9A3B6A> /System/Library/PrivateFrameworks/MobileAsset.framework/Versions/A/MobileAsset\\n 684 |        0x1a4569000 -        0x1a45b4c1f  com.apple.CacheDelete (1.0 - 1) <980D3AFB-5B2B-3E25-90AC-09A7E3A9E04D> /System/Library/PrivateFrameworks/CacheDelete.framework/Versions/A/CacheDelete\\n 685 |        0x1a45b5000 -        0x1a45f5c3f  com.apple.security.KeychainCircle.KeychainCircle (1.0 - 1) <CEF85624-9667-3C80-957B-7E2FAE4119F7> /System/Library/PrivateFrameworks/KeychainCircle.framework/Versions/A/KeychainCircle\\n 686 |        0x1a45f6000 -        0x1a45ff4c4  com.apple.CorePhoneNumbers (1.0 - 1) <224DDE02-40D5-314E-BC52-5DD7F2D6EC92> /System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers\\n 687 |        0x1a4600000 -        0x1a468807f  libTelephonyUtilDynamic.dylib (6161) <9C60746F-CC94-313F-A3C0-D5D109745E77> /usr/lib/libTelephonyUtilDynamic.dylib\\n 688 |        0x1a4ddb000 -        0x1a5051cbf  com.apple.NetworkExtension (1.0 - 1) <3F168339-C50C-3E57-AF35-15823D7D72CE> /System/Library/Frameworks/NetworkExtension.framework/Versions/A/NetworkExtension\\n 689 |        0x1a5052000 -        0x1a521e53f  com.apple.ids (10.0 - 1000) <6193AC1E-0726-3DD4-9011-855C41097F0D> /System/Library/PrivateFrameworks/IDS.framework/Versions/A/IDS\\n 690 |        0x1a521f000 -        0x1a54b257f  com.apple.idsfoundation (10.0 - 1000) <5DAEE751-5B38-352D-8647-73CC13560F56> /System/Library/PrivateFrameworks/IDSFoundation.framework/Versions/A/IDSFoundation\\n 691 |        0x1a54b3000 -        0x1a589a21f  com.apple.Sharing (2060.60.41.1.2 - 2060.60.41.1.2) <1F67775B-B161-3A6E-A0C9-139364AD49E5> /System/Library/PrivateFrameworks/Sharing.framework/Versions/A/Sharing\\n 692 |        0x1a589b000 -        0x1a59538bf  com.apple.Bluetooth (1.0 - 1) <0DF0192E-3CD6-3ABA-B436-8C11E79C69CF> /System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth\\n 693 |        0x1a5972000 -        0x1a59fc7df  com.apple.ProtectedCloudStorage (1.0 - 1) <CBBFADE8-B8E9-301E-97A3-892033ED0DEB> /System/Library/PrivateFrameworks/ProtectedCloudStorage.framework/Versions/A/ProtectedCloudStorage\\n 694 |        0x1a59fd000 -        0x1a5a653df  com.apple.QuickLookFramework (5.0 - 1002.5.2) <D763369D-B2C1-381F-A57B-79B274C22CFE> /System/Library/Frameworks/QuickLook.framework/Versions/A/QuickLook\\n 695 |        0x1a5a66000 -        0x1a5a8383f  com.apple.MetalKit (168.6 - 168.6) <5D5116A7-5556-366E-8DC7-33645C9C21ED> /System/Library/Frameworks/MetalKit.framework/Versions/A/MetalKit\\n 696 |        0x1a5a84000 -        0x1a5a889df  libxcselect.dylib (2409) <07953122-D89C-354B-8D1D-699EC0F74FD6> /usr/lib/libxcselect.dylib\\n 697 |        0x1a5b2c000 -        0x1a5b326df  libdscsym.dylib (385.14) <4EFA9545-1101-3F84-A014-F5A1AE04EAB4> /usr/lib/libdscsym.dylib\\n 698 |        0x1a5b33000 -        0x1a5c4a477  com.apple.combine (1.0 - 3022) <4C18F11B-386E-3C15-9665-264F8BD8651F> /System/Library/Frameworks/Combine.framework/Versions/A/Combine\\n 699 |        0x1a5c4b000 -        0x1a77f777f  com.apple.GeoServices (1.0 - 1986.25.3.6.3) <5F52018E-E7F1-3B4B-AC2E-93310BA8E60F> /System/Library/PrivateFrameworks/GeoServices.framework/Versions/A/GeoServices\\n 700 |        0x1a77f8000 -        0x1a78035d7  com.apple.DirectoryService.Framework (15.5 - 643) <6FFBD519-6713-3961-9F68-D6568564E91F> /System/Library/Frameworks/DirectoryService.framework/Versions/A/DirectoryService\\n 701 |        0x1a7804000 -        0x1a782cf1f  com.apple.RemoteViewServices (2.0 - 181.2) <D02C3CED-5AC9-3E98-A850-00779CF84622> /System/Library/PrivateFrameworks/RemoteViewServices.framework/Versions/A/RemoteViewServices\\n 702 |        0x1a782d000 -        0x1a783f54f  com.apple.SpeechRecognitionCore (6.3.30.10 - 6.3.30.10) <511FAF9C-FEFA-32B3-852C-11FEFE031C1A> /System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore\\n 703 |        0x1a7840000 -        0x1a7846f67  com.apple.speech.recognition.framework (6.0.4 - 6.0.4) <0F55F064-921C-38D7-B317-2B5DA873E6E8> /System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition\\n 704 |        0x1a7847000 -        0x1a78585bd  com.apple.AppleLDAP (15.5 - 63) <0FF5C7DA-F6F2-3770-9895-9BDA20F4A488> /System/Library/PrivateFrameworks/AppleLDAP.framework/Versions/A/AppleLDAP\\n 705 |        0x1a7859000 -        0x1a7aff35f  com.apple.MapKit (1.0 - 2464.25.3.6.6) <9CA434C6-0801-3E32-92D6-3551D1F7C730> /System/Library/Frameworks/MapKit.framework/Versions/A/MapKit\\n 706 |        0x1a7b0c000 -        0x1a7b11d17  com.apple.IOPlatformPluginFamily (1.0 - 1) <281B3596-A9A5-36F5-8FA7-AC0D5DD4E0ED> /System/Library/PrivateFrameworks/IOPlatformPluginFamily.framework/Versions/A/IOPlatformPluginFamily\\n 707 |        0x1a7b44000 -        0x1a7b6c51f  com.apple.GLKit (129 - 129) <9BEE5924-E3F1-366D-A19D-7CAAE1A4F21F> /System/Library/Frameworks/GLKit.framework/Versions/A/GLKit\\n 708 |        0x1a7b7e000 -        0x1a7bb7c5f  libnetworkextension.dylib (2063.120.19) <5FDC40DE-0F94-39BC-AA46-69F3C1703F4D> /usr/lib/libnetworkextension.dylib\\n 709 |        0x1a7bb8000 -        0x1a7bd7bff  com.apple.Accessibility (1.0 - 1) <21D24769-21C7-3C0E-BC90-167D95B3DF39> /System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility\\n 710 |        0x1a7c10000 -        0x1a7c119d3  libCTGreenTeaLogger.dylib (12409) <1C359AB5-8C8C-36FD-9B25-EA850744F90A> /usr/lib/libCTGreenTeaLogger.dylib\\n 711 |        0x1a7c12000 -        0x1a7c1286f  com.apple.Accelerate.vecLib (3.11 - vecLib 3.11) <CBAE1EB3-FCD9-3EEF-9FF5-3FD8AFB32A17> /System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib\\n 712 |        0x1a7c37000 -        0x1a7c3798f  com.apple.CoreServices (1226 - 1226) <7DFF50B2-8461-3231-B348-9138012F505E> /System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices\\n 713 |        0x1a7c38000 -        0x1a7cc5e1f  com.apple.CoreAppleCVA (4.1.2 - 4.1.2) <C92820C8-846E-34A9-AD23-BBF5ECE6218C> /System/Library/PrivateFrameworks/CoreAppleCVA.framework/Versions/A/CoreAppleCVA\\n 714 |        0x1a7ee3000 -        0x1a7ee3417  com.apple.Accelerate (1.11 - Accelerate 1.11) <3B8CB844-AEA9-37CA-901A-0BDC04DC268D> /System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate\\n 715 |        0x1a7f12000 -        0x1a7f329bf  com.apple.AssetCacheServices (135.1 - 135.1) <390A2CD6-0E3E-3703-83D0-98CD7A7FE762> /System/Library/PrivateFrameworks/AssetCacheServices.framework/Versions/A/AssetCacheServices\\n 716 |        0x1a7f33000 -        0x1a7f47c5f  com.apple.MediaAccessibility (1.0 - 153) <29C27CA3-680F-30FB-9B0A-1766E801E159> /System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility\\n 717 |        0x1a7f67000 -        0x1a7f6c56b  com.apple.AppleSRP (5.0 - 1) <EF58A37A-1943-3444-BF38-2449FE6D8645> /System/Library/PrivateFrameworks/AppleSRP.framework/Versions/A/AppleSRP\\n 718 |        0x1a7f6d000 -        0x1a7fab2bf  com.apple.framework.SystemAdministration (1.0 - 1.0) <03813723-47ED-3364-AD16-1BAB7D54467E> /System/Library/PrivateFrameworks/SystemAdministration.framework/Versions/A/SystemAdministration\\n 719 |        0x1a7fac000 -        0x1a8560f1f  com.apple.VN (8.0.78 - 8.0.78) <FB1DE8AA-E7EB-3C9F-99E6-5F2CEDABB979> /System/Library/Frameworks/Vision.framework/Versions/A/Vision\\n 720 |        0x1a8561000 -        0x1a85613ef  libswiftFoundation.dylib (2000) <D6CAEFA3-9D0D-3A49-83B2-6AE3785C04D8> /usr/lib/swift/libswiftFoundation.dylib\\n 721 |        0x1a8562000 -        0x1a862ea1f  com.apple.AddressBook.ContactsFoundation (8.0 - 1368.500.71) <F4E3833E-FD56-36E2-BE0B-5D762470FE16> /System/Library/PrivateFrameworks/ContactsFoundation.framework/Versions/A/ContactsFoundation\\n 722 |        0x1a862f000 -        0x1a86ad09f  com.apple.contacts.ContactsPersistence (1.0 - 3770.600.1) <8696A92A-1894-35A2-A0B4-B881E9000C67> /System/Library/PrivateFrameworks/ContactsPersistence.framework/Versions/A/ContactsPersistence\\n 723 |        0x1a86ae000 -        0x1a8811f1f  com.apple.AddressBook.core (1.0 - 2695.500.71) <EF8FF495-5ADD-3F3B-86BE-E7C516BEBD18> /System/Library/PrivateFrameworks/AddressBookCore.framework/Versions/A/AddressBookCore\\n 724 |        0x1a8812000 -        0x1a8a3183f  com.apple.contacts (1.0 - 3770.600.1) <412AE45D-FE92-38B9-9859-282F663A7069> /System/Library/Frameworks/Contacts.framework/Versions/A/Contacts\\n 725 |        0x1a8a32000 -        0x1a8a4253f  com.apple.PersonaKit (1.0 - 1) <D5C35812-272E-3CF9-8071-1D32CEB461CE> /System/Library/PrivateFrameworks/PersonaKit.framework/Versions/A/PersonaKit\\n 726 |        0x1a8a43000 -        0x1a8a48c3f  com.apple.communicationsfilter (10.0 - 1000) <C561D82C-9FF1-3816-B6ED-CC9C6D0DC85A> /System/Library/PrivateFrameworks/CommunicationsFilter.framework/Versions/A/CommunicationsFilter\\n 727 |        0x1a8a49000 -        0x1a8af845f  com.apple.FamilyCircle (1.0 - 226.476) <9F8048F7-408C-30C9-B3A0-54A7424B15C3> /System/Library/PrivateFrameworks/FamilyCircle.framework/Versions/A/FamilyCircle\\n 728 |        0x1a8af9000 -        0x1a8bdf4bf  com.apple.CoreBluetooth (185.7.2) <A03F05D6-9EE2-314D-9D57-802E12A31831> /System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth\\n 729 |        0x1a8be0000 -        0x1a8bef4bf  com.apple.SymptomDiagnosticReporter (1.0 - 383.120.2) <4CECA7E6-6F90-39BE-890E-BE9FA227E543> /System/Library/PrivateFrameworks/SymptomDiagnosticReporter.framework/Versions/A/SymptomDiagnosticReporter\\n 730 |        0x1a8bf0000 -        0x1a8c1e87f  com.apple.PowerLog (1.0 - 1) <7EA34C58-BF5C-3190-8DBC-736F98C720A6> /System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog\\n 731 |        0x1a8c1f000 -        0x1a8c2bedf  com.apple.AppleIDAuthSupport (1.0 - 1) <1C0079B7-B2B0-3E1E-9992-D71B46A98A6A> /System/Library/PrivateFrameworks/AppleIDAuthSupport.framework/Versions/A/AppleIDAuthSupport\\n 732 |        0x1a8c2c000 -        0x1a8ce031f  com.apple.DiscRecording (9.0.3 - 9030.4.5) <BABB03EA-C1B5-3B8B-B2C1-A6929A06BA53> /System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording\\n 733 |        0x1a8ce1000 -        0x1a8d1225f  com.apple.MediaKit (16 - 938) <97598508-8740-3E99-8C96-63303B0A70C4> /System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit\\n 734 |        0x1a8d13000 -        0x1a8df633f  com.apple.DiskManagement (15.0 - 934.120.4) <20B8751F-F02F-3775-928A-80DCB85F348B> /System/Library/PrivateFrameworks/DiskManagement.framework/Versions/A/DiskManagement\\n 735 |        0x1a8df7000 -        0x1a8e0311f  com.apple.CoreAUC (568.2 - 568.2) <E0B6DCC9-A62E-3912-BEC8-2B1311EB71E6> /System/Library/PrivateFrameworks/CoreAUC.framework/Versions/A/CoreAUC\\n 736 |        0x1a8e04000 -        0x1a8e0777b  com.apple.Mangrove (1.0 - 25) <8ABCA01E-6BDC-3F35-816A-033CF8D91E53> /System/Library/PrivateFrameworks/Mangrove.framework/Versions/A/Mangrove\\n 737 |        0x1a8e08000 -        0x1a8e36f0f  com.apple.CoreAVCHD (6.0.0 - 6202.1) <7C321073-A024-3028-A8D5-177199225661> /System/Library/PrivateFrameworks/CoreAVCHD.framework/Versions/A/CoreAVCHD\\n 738 |        0x1a8e37000 -        0x1a900405f  com.apple.FileProvider (2882.120.74 - 2882.120.74) <0E47D7B8-52F9-3071-864A-C730C8EFE62E> /System/Library/Frameworks/FileProvider.framework/Versions/A/FileProvider\\n 739 |        0x1a9005000 -        0x1a902b01f  com.apple.GenerationalStorage (2.0 - 384.100.2) <C5B0CA63-7818-3DDB-84A8-2F2A754BEBE3> /System/Library/PrivateFrameworks/GenerationalStorage.framework/Versions/A/GenerationalStorage\\n 740 |        0x1a902c000 -        0x1a905d9df  com.apple.security.octagontrust (1.0 - 1) <8710CC08-2D16-3CD8-81D3-DEF29FC7799E> /System/Library/PrivateFrameworks/OctagonTrust.framework/Versions/A/OctagonTrust\\n 741 |        0x1a905e000 -        0x1a907e25f  com.apple.CPAnalytics (1.0 - 760.6.150) <937A2BA3-3120-3F05-AD30-7CF60FFE0D53> /System/Library/PrivateFrameworks/CPAnalytics.framework/Versions/A/CPAnalytics\\n 742 |        0x1a9563000 -        0x1a971cdff  com.apple.CoreTelephony (113 - 12409) <8C493BF2-8CBA-320E-9C00-EDBB17B5C5D3> /System/Library/Frameworks/CoreTelephony.framework/Versions/A/CoreTelephony\\n 743 |        0x1a972f000 -        0x1a9745af8  libswiftDispatch.dylib (54) <190358E5-F936-31FE-99EA-22851A289468> /usr/lib/swift/libswiftDispatch.dylib\\n 744 |        0x1a9746000 -        0x1a99c655f  com.apple.AVFCore (1.0 - 2330.7.1) <F97BB260-C0DC-3BFE-ADEB-02F07BE28798> /System/Library/PrivateFrameworks/AVFCore.framework/Versions/A/AVFCore\\n 745 |        0x1a99c7000 -        0x1a9a933bf  com.apple.FrontBoardServices (943.6.1 - 943.6.1) <D7443AC9-F1C1-36C9-9BED-F8C3539A7283> /System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices\\n 746 |        0x1a9a94000 -        0x1a9b02abf  com.apple.BoardServices (1.0 - 694.5.5) <C1118BB9-9257-3826-A452-640E8E9310DD> /System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices\\n 747 |        0x1a9b03000 -        0x1a9b40aff  com.apple.contacts.vCard (1.0 - 3770.600.1) <38122E78-C73F-381F-BF31-727415575D12> /System/Library/PrivateFrameworks/vCard.framework/Versions/A/vCard\\n 748 |        0x1a9b41000 -        0x1a9b4eb1f  com.apple.GraphicsServices (1.0 - 1.0) <D3836F35-35EA-3D74-9C83-11F55714BA45> /System/Library/PrivateFrameworks/GraphicsServices.framework/Versions/A/GraphicsServices\\n 749 |        0x1a9b53000 -        0x1a9bb631f  com.apple.CryptoTokenKit (1.0 - 1) <F7E9985A-D9DA-35AE-A3A8-EBA7C773355C> /System/Library/Frameworks/CryptoTokenKit.framework/Versions/A/CryptoTokenKit\\n 750 |        0x1a9bb7000 -        0x1a9c0cdff  com.apple.LocalAuthentication (1.0 - 1656.120.6) <F563A5BB-468F-31BE-B963-5F64E65743FA> /System/Library/Frameworks/LocalAuthentication.framework/Versions/A/LocalAuthentication\\n 751 |        0x1a9c0d000 -        0x1a9c3993f  com.apple.CoreAuthentication.SharedUtils (1.0 - 1656.120.6) <62CA6EEB-D80A-3335-AA85-6C8EEBFD1171> /System/Library/Frameworks/LocalAuthentication.framework/Support/SharedUtils.framework/Versions/A/SharedUtils\\n 752 |        0x1a9c3a000 -        0x1a9d280df  com.apple.avfoundationcf (2.0 - 710.7.1) <45BCA9D8-2E8E-3D2C-B780-8D51F8F05FE1> /System/Library/PrivateFrameworks/AVFoundationCF.framework/Versions/A/AVFoundationCF\\n 753 |        0x1a9d29000 -        0x1a9d9c45f  com.apple.CoreHaptics (1.0 - 1) <DCA5B8A0-78F3-3B6F-9D3B-77BDE352AC2F> /System/Library/Frameworks/CoreHaptics.framework/Versions/A/CoreHaptics\\n 754 |        0x1a9de0000 -        0x1a9ea02bf  com.apple.SAObjects (1.0 - 1) <BDA9AA94-A4FB-39E4-B8B4-B03E92ED5EF9> /System/Library/PrivateFrameworks/SAObjects.framework/Versions/A/SAObjects\\n 755 |        0x1a9ea1000 -        0x1a9ec4681  com.apple.DebugSymbols (216 - 217) <90D11A72-3D0C-3CF3-97C6-313DC4262EF7> /System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols\\n 756 |        0x1a9ec5000 -        0x1a9fd1e8f  com.apple.CoreSymbolication (16.0 - 64570.35.1) <BE0207BB-6AF9-3BFD-99D7-F24C34650057> /System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication\\n 757 |        0x1a9fd2000 -        0x1a9fdc17f  com.apple.CoreTime (334.0.4.1) <30621420-0A57-30B8-AD67-C3D588D472C0> /System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime\\n 758 |        0x1a9fdd000 -        0x1aa0cf1bf  com.apple.Rapport (6.6.0 - 660.5.1) <C5E018EF-5D75-3B47-B877-0FA3D4EBEC4D> /System/Library/PrivateFrameworks/Rapport.framework/Versions/A/Rapport\\n 759 |        0x1aa0d0000 -        0x1aad745ff  com.apple.private.EmbeddedAcousticRecognition (1.0 - 1) <285A1178-6547-37AB-A20C-7A6B3833FA7F> /System/Library/PrivateFrameworks/EmbeddedAcousticRecognition.framework/Versions/A/EmbeddedAcousticRecognition\\n 760 |        0x1aad75000 -        0x1aadca9df  com.apple.coreduetcontext (1.0 - 1) <DD171E63-D6CD-3625-B2A1-D99DFFA74231> /System/Library/PrivateFrameworks/CoreDuetContext.framework/Versions/A/CoreDuetContext\\n 761 |        0x1aadcb000 -        0x1ab41a7df  com.apple.Intents (1.0 - 1) <8529DBAE-4FE7-30BB-81B0-D506FB338468> /System/Library/Frameworks/Intents.framework/Versions/A/Intents\\n 762 |        0x1ab41b000 -        0x1ab41bebf  com.apple.framework.Apple80211 (17.0 - 1728) <7E9CCEAD-2CF3-3559-B492-993C7E4F5DA6> /System/Library/PrivateFrameworks/Apple80211.framework/Versions/A/Apple80211\\n 763 |        0x1ab41c000 -        0x1ab6e35ff  com.apple.CoreWiFi (1.0 - 875.45) <20EDCF40-48BA-3374-838D-D7DFA34D3513> /System/Library/PrivateFrameworks/CoreWiFi.framework/Versions/A/CoreWiFi\\n 764 |        0x1ab6e4000 -        0x1ab73f65f  com.apple.BackBoardServices (1.0 - 1.0) <ECA7262E-4F22-37F7-AFE5-D726AD294A04> /System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices\\n 765 |        0x1ab740000 -        0x1ab77b967  com.apple.LDAPFramework (2.4.28 - 194.5) <FEE2CBBB-5380-34BD-B538-D9574C35FCDE> /System/Library/Frameworks/LDAP.framework/Versions/A/LDAP\\n 766 |        0x1ab77c000 -        0x1ab77d7a7  com.apple.TrustEvaluationAgent (2.0 - 38) <F7D708E8-9F16-3E98-98E8-4D6DF020F4FE> /System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent\\n 767 |        0x1ab8a9000 -        0x1ab96697f  com.apple.DiskImagesFramework (671.100.2 - 671.100.2) <3EA15A42-7BB5-349A-85DA-5E16BD74BE3F> /System/Library/PrivateFrameworks/DiskImages.framework/Versions/A/DiskImages\\n 768 |        0x1ab967000 -        0x1ab9a62df  com.apple.SystemConfiguration.EAP8021X (14.0.0 - 14.0) <981089BC-CD31-341E-AF5D-0DC1E92E3C97> /System/Library/PrivateFrameworks/EAP8021X.framework/Versions/A/EAP8021X\\n 769 |        0x1ab9a7000 -        0x1ab9bc35f  com.apple.RemoteServiceDiscovery (1.0 - 172.100.9) <0FEAC71F-49C3-3F77-8879-5CBC15372166> /System/Library/PrivateFrameworks/RemoteServiceDiscovery.framework/Versions/A/RemoteServiceDiscovery\\n 770 |        0x1ab9bd000 -        0x1ab9d3a1f  com.apple.xpc.RemoteXPC (1.0 - 2894.121.3) <C6B33D80-0453-3E15-B353-7BBCE13562CE> /System/Library/PrivateFrameworks/RemoteXPC.framework/Versions/A/RemoteXPC\\n 771 |        0x1aba52000 -        0x1aba5565b  com.apple.help (1.3.8 - 81) <4692C45E-4043-344C-9D13-F76E6C3F5E32> /System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/Help.framework/Versions/A/Help\\n 772 |        0x1aba56000 -        0x1aba5a2ff  com.apple.EFILogin (2.0 - 2) <3B150003-FEED-313D-BFC7-06E481F60A3D> /System/Library/PrivateFrameworks/EFILogin.framework/Versions/A/EFILogin\\n 773 |        0x1aba5b000 -        0x1aba669bf  libcsfde.dylib (566) <460FD2D4-3A8E-36B5-9794-996DD79D1D44> /usr/lib/libcsfde.dylib\\n 774 |        0x1aba67000 -        0x1abae8153  libcurl.4.dylib (160) <5819F5B4-3448-31A1-B07B-239021538867> /usr/lib/libcurl.4.dylib\\n 775 |        0x1abae9000 -        0x1abaee3b7  com.apple.LoginUICore (4.0 - 4.0) <D8D14073-59DD-3347-A5D6-CC2992417178> /System/Library/PrivateFrameworks/LoginUIKit.framework/Versions/A/Frameworks/LoginUICore.framework/Versions/A/LoginUICore\\n 776 |        0x1abaef000 -        0x1abb3235f  com.apple.AppSupport (1.0.0 - 29) <0AFDD21C-1952-39EC-9985-019F12C17141> /System/Library/PrivateFrameworks/AppSupport.framework/Versions/A/AppSupport\\n 777 |        0x1abb33000 -        0x1abb787df  com.apple.AppSSO (1.0 - 1) <EBF4478A-E33E-3E90-A051-0303892F22A9> /System/Library/PrivateFrameworks/AppSSO.framework/Versions/A/AppSSO\\n 778 |        0x1abc92000 -        0x1abded35f  com.apple.GameController (12.5.3) <8CCA7AE2-5D31-3B62-9A44-83FE9954FBD0> /System/Library/Frameworks/GameController.framework/Versions/A/GameController\\n 779 |        0x1abdf6000 -        0x1abdf6927  com.apple.ApplicationServices (48 - 65) <2B6FABDE-4A4F-3A72-9F46-93C4CDD39FC6> /System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices\\n 780 |        0x1abdf7000 -        0x1abdfa03f  com.apple.InternationalTextSearch (1.0 - 1) <8EE50900-1649-32CB-BC0C-7C78E6F03095> /System/Library/PrivateFrameworks/InternationalTextSearch.framework/Versions/A/InternationalTextSearch\\n 781 |        0x1abdfb000 -        0x1abec12ff  com.apple.ClassKit (1.2 - 150.4.10) <3F9F1003-0AE5-354B-AA9F-91032905E1ED> /System/Library/Frameworks/ClassKit.framework/Versions/A/ClassKit\\n 782 |        0x1abec2000 -        0x1ac11669f  com.apple.AppleAccount (1.0 - 1.0) <CD2EFDBF-C247-368E-B27C-381638CECD0E> /System/Library/PrivateFrameworks/AppleAccount.framework/Versions/A/AppleAccount\\n 783 |        0x1ac117000 -        0x1ac1532ff  com.apple.AppleIDSSOAuthentication (1.0 - 1) <C0A54BCE-4329-3472-84C5-97BA13722EBD> /System/Library/PrivateFrameworks/AppleIDSSOAuthentication.framework/Versions/A/AppleIDSSOAuthentication\\n 784 |        0x1ac154000 -        0x1ac1ad65f  com.apple.CorePrediction (1.0 - 1) <7BE76717-C8D2-3348-9310-CF80CDDAD170> /System/Library/PrivateFrameworks/CorePrediction.framework/Versions/A/CorePrediction\\n 785 |        0x1ac1ae000 -        0x1ac28b9ff  com.apple.AuthKitUI (1.0 - 1) <8F01A5CA-DDD4-3318-8157-3EC09072D8C8> /System/Library/PrivateFrameworks/AuthKitUI.framework/Versions/A/AuthKitUI\\n 786 |        0x1ac2ef000 -        0x1ac2f2d1f  com.apple.security.CryptoKit-C-Bridging (1.0 - 1) <B76254CE-E8A2-3EB9-AD40-F6ABDF05A145> /System/Library/PrivateFrameworks/CryptoKitCBridging.framework/Versions/A/CryptoKitCBridging\\n 787 |        0x1ac2f3000 -        0x1ac2f33f7  libHeimdalProxy.dylib (87.100.1) <B439D911-87DD-30BD-82E6-617947CCA5C5> /System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib\\n 788 |        0x1ac2f4000 -        0x1ac2f4512  com.apple.audio.units.AudioUnit (1.14 - 1.14) <52991A9F-5CE3-34FA-82F5-41B1C1771FE9> /System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit\\n 789 |        0x1ac2fc000 -        0x1ac301c63  com.apple.NetFSServer (2.0 - 1) <CBD20973-5F4A-39B5-AACC-CC6B72D6DFCE> /System/Library/PrivateFrameworks/NetFSServer.framework/Versions/A/NetFSServer\\n 790 |        0x1ac317000 -        0x1ac350b7f  com.apple.StreamingZip (1.0 - 1) <1015B371-2ADB-3EA4-84F3-8BE0DAFEC342> /System/Library/PrivateFrameworks/StreamingZip.framework/Versions/A/StreamingZip\\n 791 |        0x1ac351000 -        0x1ac39bbff  com.apple.DuetActivityScheduler (1.0 - 1) <1BF29142-188D-342E-93A8-9D5496088430> /System/Library/PrivateFrameworks/DuetActivityScheduler.framework/Versions/A/DuetActivityScheduler\\n 792 |        0x1ac39c000 -        0x1ac39fbdd  libswiftObjectiveC.dylib (940.4) <B91EF9E2-A96E-3529-976A-50110A318D1D> /usr/lib/swift/libswiftObjectiveC.dylib\\n 793 |        0x1ac3a0000 -        0x1ac3b91ff  libswiftos.dylib (1062.100.1) <5DCF3610-7961-3E86-B633-2333DCC8BC87> /usr/lib/swift/libswiftos.dylib\\n 794 |        0x1ac3ba000 -        0x1ac3ca6ff  com.apple.IntentsFoundation (1.0 - 1) <7D7A1F79-8C70-3AD6-A8F1-3242BC4086C5> /System/Library/PrivateFrameworks/IntentsFoundation.framework/Versions/A/IntentsFoundation\\n 795 |        0x1ac3cb000 -        0x1ac3d3e1f  com.apple.PushKit (1.0 - 1) <DB0084F6-ECDC-3BA2-96E3-86F8A1F9258E> /System/Library/Frameworks/PushKit.framework/Versions/A/PushKit\\n 796 |        0x1ac3d4000 -        0x1ac42d43f  com.apple.C2 (1.3 - 2250.16) <2DEAA05C-ECD5-3A01-80E8-CEE26F13D09A> /System/Library/PrivateFrameworks/C2.framework/Versions/A/C2\\n 797 |        0x1ac42e000 -        0x1ac479d9f  com.apple.QuickLookThumbnailing (1.0 - 199.5.3) <31D74859-B899-362F-9E2A-B2F408E1295E> /System/Library/Frameworks/QuickLookThumbnailing.framework/Versions/A/QuickLookThumbnailing\\n 798 |        0x1ac47a000 -        0x1ad33b21f  com.apple.vision.EspressoFramework (1.0 - 3405.6.2) <B94C7B0B-A089-3437-A474-6B05B4C85FB4> /System/Library/PrivateFrameworks/Espresso.framework/Versions/A/Espresso\\n 799 |        0x1ad33c000 -        0x1ad36901f  com.apple.ANEServices (8.600 - 8.600) <0E7035E1-B347-3F5C-B4A9-97EB8FB2638F> /System/Library/PrivateFrameworks/ANEServices.framework/Versions/A/ANEServices\\n 800 |        0x1ad36a000 -        0x1ad3f95ff  com.apple.proactive.support.ProactiveSupport (1.0 - 408) <D49A3A89-9BB1-3D6C-9748-3CC50F283123> /System/Library/PrivateFrameworks/ProactiveSupport.framework/Versions/A/ProactiveSupport\\n 801 |        0x1ad3fa000 -        0x1ad40a0df  com.apple.corerecents (1.0 - 1) <5B667160-FB6D-391F-9118-4AFEF073E075> /System/Library/PrivateFrameworks/CoreRecents.framework/Versions/A/CoreRecents\\n 802 |        0x1ad40b000 -        0x1ad44e55f  com.apple.iCalendar (7.0 - 1167) <23DFAC3B-7168-3634-A810-DF0883C99F85> /System/Library/PrivateFrameworks/iCalendar.framework/Versions/A/iCalendar\\n 803 |        0x1ad44f000 -        0x1ad4e3edf  com.apple.CalendarFoundation (8.0 - 1575.5.3) <C77E8FE5-663D-3789-A60D-A360B7850FFE> /System/Library/PrivateFrameworks/CalendarFoundation.framework/Versions/A/CalendarFoundation\\n 804 |        0x1ad4e4000 -        0x1ad4e4527  com.apple.CoreDuetDaemonProtocol (1.0 - 1) <85479070-8BBD-34BC-83AE-E4DDF60A2A65> /System/Library/PrivateFrameworks/CoreDuetDaemonProtocol.framework/Versions/A/CoreDuetDaemonProtocol\\n 805 |        0x1ad4e5000 -        0x1ad5363df  com.apple.ASEProcessing (1.41.50 - 1.41.50) <DB37EDA0-88D0-385C-B5AC-08ED2AD26A7A> /System/Library/PrivateFrameworks/ASEProcessing.framework/Versions/A/ASEProcessing\\n 806 |        0x1ad537000 -        0x1ad5d7bff  com.apple.framework.ConfigurationProfiles (17.1 - 1722) <42417AC2-5342-3295-ADA6-C84135D13491> /System/Library/PrivateFrameworks/ConfigurationProfiles.framework/Versions/A/ConfigurationProfiles\\n 807 |        0x1ad800000 -        0x1ad8592ff  com.apple.AOSAccounts (1.3.1 - 197.450) <03AE535C-07F1-3B57-B884-************> /System/Library/PrivateFrameworks/AOSAccounts.framework/Versions/A/AOSAccounts\\n 808 |        0x1add3e000 -        0x1add9a8c7  com.apple.ChunkingLibrary (2200.114.1 - 2200.114.1) <A3D3046D-4C0D-3C2D-BFA7-9C0DD7E565DE> /System/Library/PrivateFrameworks/ChunkingLibrary.framework/Versions/A/ChunkingLibrary\\n 809 |        0x1aded5000 -        0x1adee843f  com.apple.MediaLibrary (1.11.0 - 806.2) <5D6B4FDC-3B7B-326F-AE7B-355984DC1C95> /System/Library/Frameworks/MediaLibrary.framework/Versions/A/MediaLibrary\\n 810 |        0x1adee9000 -        0x1adf4785f  com.apple.CalDAV (8.0 - 1149) <83994B07-207E-378E-9FFD-50D337957373> /System/Library/PrivateFrameworks/CalDAV.framework/Versions/A/CalDAV\\n 811 |        0x1adf48000 -        0x1ae01355f  com.apple.CoreSuggestions (1.0 - 1276.17.0.1) <663E0015-BB6A-3FAC-A085-39BC4E0B9020> /System/Library/PrivateFrameworks/CoreSuggestions.framework/Versions/A/CoreSuggestions\\n 812 |        0x1ae014000 -        0x1ae1ef8ff  com.apple.eventkit (3.0 - 1902.6.3.1) <D2C31D87-B536-3C5C-81CF-A328C7DD58A0> /System/Library/Frameworks/EventKit.framework/Versions/A/EventKit\\n 813 |        0x1ae1f0000 -        0x1ae21b03f  com.apple.RTCReporting (13.1.47 - 120.1) <CA254E03-FE62-38BF-90FA-8C43BB02300E> /System/Library/PrivateFrameworks/RTCReporting.framework/Versions/A/RTCReporting\\n 814 |        0x1ae21c000 -        0x1ae43415f  com.apple.WebKitLegacy (20621 - 20621.********) <52CDC6C0-E3AA-3436-A3B8-44C92D26D6C7> /System/Library/Frameworks/WebKit.framework/Versions/A/Frameworks/WebKitLegacy.framework/Versions/A/WebKitLegacy\\n 815 |        0x1ae482000 -        0x1ae4e0adf  com.apple.CoreML.AppleNeuralEngine (1.0 - 1) <BEB1DC28-631F-3A3A-AD4F-82C6DD00AEE6> /System/Library/PrivateFrameworks/AppleNeuralEngine.framework/Versions/A/AppleNeuralEngine\\n 816 |        0x1ae60b000 -        0x1ae6c5d1f  com.apple.audio.midi.CoreMIDI (2.0 - 88) <CF441EC3-9851-3874-8FE1-17D470031EA5> /System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI\\n 817 |        0x1ae7de000 -        0x1ae7de467  com.apple.Cocoa (6.11 - 24) <ED530B3F-48BA-34CB-931F-A81186AA7488> /System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa\\n 818 |        0x1ae818000 -        0x1ae8250ff  com.apple.AskPermission (1.0 - 1) <CC2BF71D-E594-3041-936B-AC2DE781BB89> /System/Library/PrivateFrameworks/AskPermission.framework/Versions/A/AskPermission\\n 819 |        0x1ae826000 -        0x1af27ee3f  com.apple.AppleMediaServices (1.0 - 1) <E84F588B-7EA4-3F05-B72B-CA0BFACF9C7A> /System/Library/PrivateFrameworks/AppleMediaServices.framework/Versions/A/AppleMediaServices\\n 820 |        0x1af27f000 -        0x1af283507  com.apple.IOSurfaceAccelerator (1.0.0 - 1.0.0) <A9C976EF-1597-330A-ACBD-B7FC8C03B052> /System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator\\n 821 |        0x1af284000 -        0x1af2890bf  libUAPreferences.dylib (696.5.6) <CB9B385E-3AFB-3FA3-B90F-819899AC5327> /System/Library/PrivateFrameworks/UniversalAccess.framework/Versions/A/Libraries/libUAPreferences.dylib\\n 822 |        0x1af28a000 -        0x1af2ad65f  com.apple.framework.familycontrols (4.1 - 410) <6E83B836-9640-3EEE-8FB9-F4B043F09974> /System/Library/PrivateFrameworks/FamilyControls.framework/Versions/A/FamilyControls\\n 823 |        0x1af2ae000 -        0x1af2ba3df  com.apple.CommerceCore (1.0 - 715.5.1) <02763F9D-F3D4-3B2A-B00F-A340FD775A3D> /System/Library/PrivateFrameworks/CommerceKit.framework/Versions/A/Frameworks/CommerceCore.framework/Versions/A/CommerceCore\\n 824 |        0x1af2bb000 -        0x1af681abf  com.apple.MediaRemote (1.0 - 1) <AC457B92-6D20-3E98-BBAD-A2E3DF535A0D> /System/Library/PrivateFrameworks/MediaRemote.framework/Versions/A/MediaRemote\\n 825 |        0x1af682000 -        0x1af91eaff  com.apple.AssistantServices (1.0 - 1) <44F4AED7-1F02-3D86-A58D-807389E77D51> /System/Library/PrivateFrameworks/AssistantServices.framework/Versions/A/AssistantServices\\n 826 |        0x1af91f000 -        0x1af92f5df  com.apple.PhotoFoundation (1.0 - 760.6.150) <D45CCCE4-3A89-320F-8E2B-FDA8A1ABE4AB> /System/Library/PrivateFrameworks/PhotoFoundation.framework/Versions/A/PhotoFoundation\\n 827 |        0x1af930000 -        0x1afca7c7f  com.apple.imsharedutilities (10.0 - 1000) <F2D86FD0-C11D-3814-8DCA-AA0CADDD5BF6> /System/Library/PrivateFrameworks/IMSharedUtilities.framework/Versions/A/IMSharedUtilities\\n 828 |        0x1afcb1000 -        0x1afcb5b7f  com.apple.DisplayServicesFW (3.1 - 380) <3D7166AF-3992-3E06-9FDD-912C209C5B61> /System/Library/PrivateFrameworks/DisplayServices.framework/Versions/A/DisplayServices\\n 829 |        0x1afd8c000 -        0x1afdabbbf  com.apple.icloud.FMCoreLite (1.0 - 1) <0FA2E6E7-CAB3-3727-ACB8-F36F4165140D> /System/Library/PrivateFrameworks/FMCoreLite.framework/Versions/A/FMCoreLite\\n 830 |        0x1afdac000 -        0x1b080c73f  com.apple.PassKitCore (1.0 - 1) <8A6CD799-FB3E-3E5B-897E-3252A5556505> /System/Library/PrivateFrameworks/PassKitCore.framework/Versions/A/PassKitCore\\n 831 |        0x1b0c16000 -        0x1b0c49467  libtidy.A.dylib (20.1) <404303D9-2FFF-3233-B781-4476AEAB2964> /usr/lib/libtidy.A.dylib\\n 832 |        0x1b0c4a000 -        0x1b0c759bf  com.apple.MarkupUI (1.0 - 522.200) <0520C3E2-5607-3437-8BB1-E6B938A5DE3D> /System/Library/PrivateFrameworks/MarkupUI.framework/Versions/A/MarkupUI\\n 833 |        0x1b0c76000 -        0x1b0c8d93f  com.apple.Engram (1.0 - 1) <089BD83F-5F43-30F6-A1E3-1DC24DB2D74D> /System/Library/PrivateFrameworks/Engram.framework/Versions/A/Engram\\n 834 |        0x1b0c8e000 -        0x1b0ca9dff  com.apple.openscripting (1.7 - 194.2) <9CBF8113-55C6-3C78-9B17-1545C794C18D> /System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/OpenScripting.framework/Versions/A/OpenScripting\\n 835 |        0x1b0caa000 -        0x1b0cacd1f  com.apple.securityhi (9.0 - 55010) <D7EAF1E7-AFC2-31A7-B8BD-D052A7DC4620> /System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SecurityHI.framework/Versions/A/SecurityHI\\n 836 |        0x1b0cad000 -        0x1b0cad863  com.apple.ink.framework (10.15 - 227) <41161EC1-9F8C-3BA7-AD06-9FEB90D23C57> /System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/Ink.framework/Versions/A/Ink\\n 837 |        0x1b0cae000 -        0x1b0cb1bf7  com.apple.CommonPanels (1.2.6 - 106.1) <0D0AD7CC-42E1-38E4-A867-6F8F3D393AE0> /System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/CommonPanels.framework/Versions/A/CommonPanels\\n 838 |        0x1b0cb2000 -        0x1b0cb41fb  com.apple.ImageCapture (1936.4.3 - 1936.4.3) <A30829B6-751C-3F29-BE28-1A9796D3F962> /System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/ImageCapture.framework/Versions/A/ImageCapture\\n 839 |        0x1b0cb5000 -        0x1b26ebc7f  com.apple.JavaScriptCore (20621 - 20621.********) <1BCE3526-51CF-3C0F-9DC2-6DB21A78EF5B> /System/Library/Frameworks/JavaScriptCore.framework/Versions/A/JavaScriptCore\\n 840 |        0x1b26ec000 -        0x1b26ed69f  com.apple.PowerlogControl (1.0 - 1) <E387BF0B-D896-33E1-9AF2-E139833CAE49> /System/Library/PrivateFrameworks/PowerlogControl.framework/Versions/A/PowerlogControl\\n 841 |        0x1b26ee000 -        0x1b26f521f  libUniversalAccess.dylib (696.5.6) <4052C234-55AB-32EB-AE33-1B63E7DD6079> /usr/lib/libUniversalAccess.dylib\\n 842 |        0x1b2754000 -        0x1b27d1a3f  com.apple.CoreCDP-OSX (1.0 - 1) <9D25705E-4FF5-36F7-BB09-EF5B6FCE4718> /System/Library/PrivateFrameworks/CoreCDP.framework/Versions/A/CoreCDP\\n 843 |        0x1b27d2000 -        0x1b27e1cdf  com.apple.SetupAssistantFramework (1.0 - 1) <FF7CCD79-79DD-31BD-9302-831641364EF2> /System/Library/PrivateFrameworks/SetupAssistantFramework.framework/Versions/A/SetupAssistantFramework\\n 844 |        0x1b27e2000 -        0x1b29f961f  com.apple.AVFCapture (1.0 - 587.*********) <AD828548-C9C8-3401-8F14-C7A04D6CF006> /System/Library/PrivateFrameworks/AVFCapture.framework/Versions/A/AVFCapture\\n 845 |        0x1b29fa000 -        0x1b2b2c77f  com.apple.Quagga (177 - 177) <38744AA5-1F00-3FF6-83AF-D970E1D5ADA6> /System/Library/PrivateFrameworks/Quagga.framework/Versions/A/Quagga\\n 846 |        0x1b2b2d000 -        0x1b30f215f  com.apple.CMCapture (1.0 - 587.*********) <9873E64C-3A1A-3C0A-955A-D35C97CC9ECE> /System/Library/PrivateFrameworks/CMCapture.framework/Versions/A/CMCapture\\n 847 |        0x1b30f3000 -        0x1b3237b5f  com.apple.RenderBox (6.5.1 - 6.5.1) <AB38D4A8-4250-36E3-BFDE-A4D60AD3AF99> /System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox\\n 848 |        0x1b36a1000 -        0x1b374ecff  com.apple.accounts.AccountsDaemon (113 - 113) <AD03468C-1081-3357-ABD9-F5B66C6D7587> /System/Library/PrivateFrameworks/AccountsDaemon.framework/Versions/A/AccountsDaemon\\n 849 |        0x1b374f000 -        0x1b375203f  com.apple.OAuth (25 - 25) <C06137D9-5D67-3342-922A-EF4FA3668718> /System/Library/PrivateFrameworks/OAuth.framework/Versions/A/OAuth\\n 850 |        0x1b3d58000 -        0x1b3d9343f  com.apple.CloudServices (1.0 - 638.100.48) <C4283D3C-A5EE-397C-A49C-5B3BB08840CF> /System/Library/PrivateFrameworks/CloudServices.framework/Versions/A/CloudServices\\n 851 |        0x1b3d94000 -        0x1b3da4dbf  com.apple.HID (1.0 - 1) <C877B15E-6DEC-3CFF-9E6E-32EBFC211557> /System/Library/PrivateFrameworks/HID.framework/Versions/A/HID\\n 852 |        0x1b3e07000 -        0x1b3e87b1f  com.apple.coredav (1.0.1 - 1230.5.2) <49C1B79B-900E-30A5-9EFE-4EF860FD7A69> /System/Library/PrivateFrameworks/CoreDAV.framework/Versions/A/CoreDAV\\n 853 |        0x1b3ef3000 -        0x1b3f7463f  com.apple.MediaServices (1.0 - 4024.500.30) <4A10E625-7D11-3ACC-914E-4B7AB35268B5> /System/Library/PrivateFrameworks/MediaServices.framework/Versions/A/MediaServices\\n 854 |        0x1b3f75000 -        0x1b3f9b0ff  com.apple.IASUtilities (1.0 - 659) <E3FE3DDB-2627-3840-88D0-3DAEA66E208B> /System/Library/PrivateFrameworks/IASUtilities.framework/Versions/A/IASUtilities\\n 855 |        0x1b403d000 -        0x1b4077b1f  com.apple.VirtualGarage (1.0 - 1) <4A468074-3104-3FBF-9BF3-D3DF6A67711E> /System/Library/PrivateFrameworks/VirtualGarage.framework/Versions/A/VirtualGarage\\n 856 |        0x1b4087000 -        0x1b4087847  com.apple.marco (10.0 - 1000) <7A749EB9-0053-34D0-8540-844FC7E00896> /System/Library/PrivateFrameworks/Marco.framework/Versions/A/Marco\\n 857 |        0x1b40d2000 -        0x1b414c21f  com.apple.CoreRecognition (1.3 - 157) <430A6A84-2382-3003-BE2B-5717147417A6> /System/Library/PrivateFrameworks/CoreRecognition.framework/Versions/A/CoreRecognition\\n 858 |        0x1b414d000 -        0x1b415eeff  com.apple.PersonaUI (1.0 - 1) <65181694-FECD-39E4-B120-19CD10EE89E1> /System/Library/PrivateFrameworks/PersonaUI.framework/Versions/A/PersonaUI\\n 859 |        0x1b415f000 -        0x1b426275f  com.apple.Contacts.ContactsUICore (1.0 - 3647.600.1) <2A1D621D-91E8-3908-9351-D477458B8CA5> /System/Library/PrivateFrameworks/ContactsUICore.framework/Versions/A/ContactsUICore\\n 860 |        0x1b4263000 -        0x1b43c3b5f  com.apple.ContactsUI (14.0 - 2695.500.71) <C3EDB1EE-F76B-398E-A5AA-2581A636C785> /System/Library/Frameworks/ContactsUI.framework/Versions/A/ContactsUI\\n 861 |        0x1b449c000 -        0x1b44d405f  com.apple.proactive.support.ProactiveEventTracker (1.0 - 408) <287D4071-551D-31E7-9376-5E7DE44D06EE> /System/Library/PrivateFrameworks/ProactiveEventTracker.framework/Versions/A/ProactiveEventTracker\\n 862 |        0x1b45d5000 -        0x1b4801aff  com.apple.framework.calculate (1.4 - 17) <FE490DE6-4F45-3D62-AEE5-06D14B21631B> /System/Library/PrivateFrameworks/Calculate.framework/Versions/A/Calculate\\n 863 |        0x1b4aad000 -        0x1b4aae17f  com.apple.PhoneNumbers (1.0 - 1) <772004B9-A474-3B2A-BAD0-DF8B47BA9828> /System/Library/PrivateFrameworks/PhoneNumbers.framework/Versions/A/PhoneNumbers\\n 864 |        0x1b4aaf000 -        0x1b4ab861f  com.apple.URLFormatting (272 - 272.13) <68EBC836-E8B5-35FA-B087-BB71C075FBA6> /System/Library/PrivateFrameworks/URLFormatting.framework/Versions/A/URLFormatting\\n 865 |        0x1b4ab9000 -        0x1b4b744bf  com.apple.accessibility.AXCoreUtilities (1.0 - 1) <121EF518-1FA2-38B6-B229-DBEBDB453587> /System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities\\n 866 |        0x1b4b75000 -        0x1b4ba5b7f  libAccessibility.dylib (3148.15.26) <92426E6B-6E28-3E15-9B2F-239BBF5508B5> /usr/lib/libAccessibility.dylib\\n 867 |        0x1b4ba6000 -        0x1b8266b1f  com.apple.WebCore (20621 - 20621.********) <C4635CE3-24CB-3F3E-89E1-A44F977C7A49> /System/Library/Frameworks/WebKit.framework/Versions/A/Frameworks/WebCore.framework/Versions/A/WebCore\\n 868 |        0x1b8267000 -        0x1b83321ff  com.apple.PackageKit (3.0 - 1474.120.3) <44A801FF-FF9D-3705-BCD2-17E9829B7EB5> /System/Library/PrivateFrameworks/PackageKit.framework/Versions/A/PackageKit\\n 869 |        0x1b8333000 -        0x1b834253f  com.apple.NetFS (6.0 - 4.0) <8B596B00-449D-373D-A98F-967E46A0403A> /System/Library/Frameworks/NetFS.framework/Versions/A/NetFS\\n 870 |        0x1b8499000 -        0x1b84c8d5f  com.apple.quicklook.QuickLookSupport (1.0 - 199.5.3) <1D71CCE1-E401-3D91-B6C1-D2EEDC056401> /System/Library/PrivateFrameworks/QuickLookSupport.framework/Versions/A/QuickLookSupport\\n 871 |        0x1b84c9000 -        0x1b85cd7df  com.apple.siri.parsec.CoreParsec (1.0 - 3405.13.1) <F9800132-36F8-3836-94B1-C52E293E9C2F> /System/Library/PrivateFrameworks/CoreParsec.framework/Versions/A/CoreParsec\\n 872 |        0x1b85ce000 -        0x1b87a935f  com.apple.TelephonyUtilities (1.0 - 1.0) <96526BF4-6993-381C-A71F-74AE0E2E414C> /System/Library/PrivateFrameworks/TelephonyUtilities.framework/Versions/A/TelephonyUtilities\\n 873 |        0x1b87aa000 -        0x1b8808adf  com.apple.DeviceManagement (1.0 - 221.5.1) <F123BA52-EE68-397E-AB69-C5D0A0610BD0> /System/Library/PrivateFrameworks/DeviceManagement.framework/Versions/A/DeviceManagement\\n 874 |        0x1b8809000 -        0x1b8809357  libswiftCoreGraphics.dylib (17) <F8B3AF9F-EF21-3B54-8EE0-232E7F473E4F> /usr/lib/swift/libswiftCoreGraphics.dylib\\n 875 |        0x1b880a000 -        0x1b880bd3b  libswiftDarwin.dylib (310.100.8.0.1) <FA580F4F-9BC2-33A7-AE81-EAD55E5A98FC> /usr/lib/swift/libswiftDarwin.dylib\\n 876 |        0x1b8871000 -        0x1b88e4fdf  com.apple.Safari.SafeBrowsing (621 - 621.********) <85FB632B-7C80-3E56-A71D-35185B66FADE> /System/Library/PrivateFrameworks/SafariSafeBrowsing.framework/Versions/A/SafariSafeBrowsing\\n 877 |        0x1b88e5000 -        0x1b9c9059f  com.apple.WebKit (20621 - 20621.********) <338298CE-86BA-31AA-AAE4-462D11357B71> /System/Library/Frameworks/WebKit.framework/Versions/A/WebKit\\n 878 |        0x1b9fb0000 -        0x1b9fb0517  com.apple.CorePDF (4.0 - 555) <8A1E0987-5939-3139-A3D2-B0CD15C28E6F> /System/Library/PrivateFrameworks/CorePDF.framework/Versions/A/CorePDF\\n 879 |        0x1b9fb1000 -        0x1b9fb1817  com.apple.Carbon (160 - 170) <427311EF-485D-34BA-AB30-E0E1220F2421> /System/Library/Frameworks/Carbon.framework/Versions/A/Carbon\\n 880 |        0x1b9fb2000 -        0x1ba31f59f  com.apple.coremotion (2964.0.4 - 2964.0.4) <79509AF9-7855-38DD-9A49-59B2E2206022> /System/Library/Frameworks/CoreMotion.framework/Versions/A/CoreMotion\\n 881 |        0x1ba389000 -        0x1ba389547  com.apple.avfoundation (2.0 - 2330.7.1) <51706321-65F2-31C0-8A23-0FAA8A9D537C> /System/Library/Frameworks/AVFoundation.framework/Versions/A/AVFoundation\\n 882 |        0x1ba495000 -        0x1ba583e5f  libquic.dylib (4277.121.5) <9E1D9C06-313A-3A29-947A-592F06ED8BE6> /usr/lib/libquic.dylib\\n 883 |        0x1ba587000 -        0x1ba58d9af  com.apple.EmbeddedOSSupportHost (1.0 - 1) <A4F4A3AD-880C-3784-9445-8B779A0EA271> /System/Library/PrivateFrameworks/EmbeddedOSSupportHost.framework/Versions/A/EmbeddedOSSupportHost\\n 884 |        0x1ba58e000 -        0x1ba5b33ff  com.apple.private.SystemPolicy (1.0 - 1) <DAF37D2B-C265-3F14-B8E5-CD31D38925E5> /System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy\\n 885 |        0x1ba5b4000 -        0x1ba5d811f  com.apple.icloud.FindMyDevice (1.0 - 1) <D03F6125-3BC9-3348-A925-0E1190C86700> /System/Library/PrivateFrameworks/FindMyDevice.framework/Versions/A/FindMyDevice\\n 886 |        0x1ba881000 -        0x1ba8a853f  com.apple.sidecar-core (1.0 - 342.4.4) <C1221F7E-22D8-3767-AFD0-966EB0AE14A2> /System/Library/PrivateFrameworks/SidecarCore.framework/Versions/A/SidecarCore\\n 887 |        0x1ba8a9000 -        0x1ba8aeaff  com.apple.QuickLookNonBaseSystem (1.0 - 1) <7DB875DA-4CDC-3295-85E0-77C05DF03B96> /System/Library/PrivateFrameworks/QuickLookNonBaseSystem.framework/Versions/A/QuickLookNonBaseSystem\\n 888 |        0x1ba8af000 -        0x1ba8eb6ff  com.apple.datadetectors (5.0 - 463) <B7CA297D-E85E-3462-8680-B78AE1F2ACFD> /System/Library/PrivateFrameworks/DataDetectors.framework/Versions/A/DataDetectors\\n 889 |        0x1baacc000 -        0x1bab2b5ff  com.apple.CoreRoutine (1.0 - 990.0.10) <94599927-9A8F-3C35-B260-9B37C3047F5E> /System/Library/PrivateFrameworks/CoreRoutine.framework/Versions/A/CoreRoutine\\n 890 |        0x1bab2c000 -        0x1bab4ef3f  com.apple.mediastream (1.0 - 760.6.150) <D76E17E6-A104-3443-86A8-525520ADA157> /System/Library/PrivateFrameworks/MediaStream.framework/Versions/A/MediaStream\\n 891 |        0x1bae87000 -        0x1bae8c057  com.apple.CoreOptimization (1.0 - 1) <9868741C-B95C-3AE9-980B-C4D1BEC6C93D> /System/Library/PrivateFrameworks/CoreOptimization.framework/Versions/A/CoreOptimization\\n 892 |        0x1bae8d000 -        0x1baea0adf  com.apple.FeatureFlagsSupport (1.0 - 97) <87F2FACD-0F16-3288-B146-37C153069E6B> /System/Library/PrivateFrameworks/FeatureFlagsSupport.framework/Versions/A/FeatureFlagsSupport\\n 893 |        0x1baea1000 -        0x1baea697f  com.apple.incomingcallfilter (10.0 - 1000) <1419EBEC-10DE-3459-BA51-E8C0D59FB71B> /System/Library/PrivateFrameworks/IncomingCallFilter.framework/Versions/A/IncomingCallFilter\\n 894 |        0x1baea7000 -        0x1baeff45f  com.apple.facetimeservices (10.0 - 1000) <A0D5984A-47DE-3AAD-948B-01B841F50897> /System/Library/PrivateFrameworks/FTServices.framework/Versions/A/FTServices\\n 895 |        0x1baf00000 -        0x1baf15a5f  com.apple.CoreSDB (10.0 - 1000) <B9E85770-A126-3860-A6B4-CB318F398143> /System/Library/PrivateFrameworks/CoreSDB.framework/Versions/A/CoreSDB\\n 896 |        0x1bafa8000 -        0x1bafc3c7f  com.apple.contacts.donation (1.0 - 1112.600.1) <838908FA-5689-36F9-8C46-6BAE7CAE0922> /System/Library/PrivateFrameworks/ContactsDonation.framework/Versions/A/ContactsDonation\\n 897 |        0x1bafc4000 -        0x1bb03581f  com.apple.NaturalLanguage (1.0 - 114) <96EF4B11-7617-31CD-BA42-06A2C5616BFF> /System/Library/Frameworks/NaturalLanguage.framework/Versions/A/NaturalLanguage\\n 898 |        0x1bb036000 -        0x1bb05edbf  com.apple.SafariServices.framework (20621 - 20621.********) <6F7BCBD2-D6BF-3840-B2D1-42473F21363F> /System/Library/Frameworks/SafariServices.framework/Versions/A/SafariServices\\n 899 |        0x1bb077000 -        0x1bb0fb3bf  com.apple.Catalyst (1.0 - 11.5) <71CB7A0F-166F-3122-B0B0-F327AD5D68CC> /System/Library/PrivateFrameworks/Catalyst.framework/Versions/A/Catalyst\\n 900 |        0x1bb11c000 -        0x1bb168dff  com.apple.LocalAuthentication.DaemonUtils (1.0 - 1656.120.6) <6FDC73A3-E9D6-395E-86D7-20959E0BCB49> /System/Library/Frameworks/LocalAuthentication.framework/Support/DaemonUtils.framework/Versions/A/DaemonUtils\\n 901 |        0x1bb169000 -        0x1bb1af01f  com.apple.BiometricKit (1.0 - 511.100.15) <D4D41A70-67EB-3215-B918-1CA29260A38E> /System/Library/PrivateFrameworks/BiometricKit.framework/Versions/A/BiometricKit\\n 902 |        0x1bb2e1000 -        0x1bb3695ff  com.apple.LoggingSupport (1.0 - 1643.120.5) <AF33035D-BDDC-3655-94FA-D8D3CABD8B61> /System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport\\n 903 |        0x1bb36a000 -        0x1bb37662b  com.apple.MallocStackLogging (1.0 - 64570.58.1) <BE8E0E40-4155-3DD2-B7A4-A25F9F958C24> /System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging\\n 904 |        0x1bb37a000 -        0x1bb39b57f  com.apple.StorageManagement (1.0 - 1) <4D5A8135-B3C5-3DE3-A1F2-B315EEA3F9A6> /System/Library/PrivateFrameworks/StorageManagement.framework/Versions/A/StorageManagement\\n 905 |        0x1bb39c000 -        0x1bb3d047f  libmis.dylib (436.100.18) <7B339342-7155-3CA1-872D-07C3EF618CB5> /usr/lib/libmis.dylib\\n 906 |        0x1bb3d1000 -        0x1bb3d4c7f  com.apple.gpusw.GPURawCounter (34 - 34) <3A08EC13-7543-3ECE-A2EB-088EF0D21211> /System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter\\n 907 |        0x1bb3d5000 -        0x1bb3f5143  libswiftCoreAudio.dylib (328.606) <1EDFD858-2938-37B3-9D92-7F46E6D0D2EF> /usr/lib/swift/libswiftCoreAudio.dylib\\n 908 |        0x1bb3f6000 -        0x1bb3fcfff  libswiftCoreFoundation.dylib (2401) <FEB3B535-00FF-3EA4-B8E3-50973CEE4072> /usr/lib/swift/libswiftCoreFoundation.dylib\\n 909 |        0x1bb3fd000 -        0x1bb407b04  libswiftIntents.dylib (12) <5FA3BB06-E245-3E8C-92BD-8463FC287161> /usr/lib/swift/libswiftIntents.dylib\\n 910 |        0x1bb408000 -        0x1bb44f6aa  libswiftXPC.dylib (69.120.3) <8583BB16-896E-3567-92AA-0C22A5A98C72> /usr/lib/swift/libswiftXPC.dylib\\n 911 |        0x1bb450000 -        0x1bb4509cf  libswiftCoreImage.dylib (2) <C3E3A88C-C427-3B23-BA74-91F3D4C5F154> /usr/lib/swift/libswiftCoreImage.dylib\\n 912 |        0x1bb451000 -        0x1bb451ab3  libswiftIOKit.dylib (1) <27327773-AC1A-3E41-8E58-ACA5F682713A> /usr/lib/swift/libswiftIOKit.dylib\\n 913 |        0x1bb452000 -        0x1bb85e65f  com.apple.CoreHandwriting (161 - 1.2) <AA8CC481-2DA6-328D-A3FD-79D264EB908C> /System/Library/PrivateFrameworks/CoreHandwriting.framework/Versions/A/CoreHandwriting\\n 914 |        0x1bb85f000 -        0x1bbaa493f  com.apple.imageKit (3.0 - 1178.4.3) <B5B53E4D-AE58-3E9D-AA80-FDCE0AE76099> /System/Library/Frameworks/Quartz.framework/Versions/A/Frameworks/ImageKit.framework/Versions/A/ImageKit\\n 915 |        0x1bbaa5000 -        0x1bbc593ff  com.apple.PencilKit (1.0 - 1) <6AF18682-B0BB-3D0B-938D-07055E438F86> /System/Library/Frameworks/PencilKit.framework/Versions/A/PencilKit\\n 916 |        0x1bbc5a000 -        0x1bbc6e77f  com.apple.sidecar-ui (1.0 - 342.4.4) <C118A1F3-1E9E-33D1-9571-F15C8B6BC2E3> /System/Library/PrivateFrameworks/SidecarUI.framework/Versions/A/SidecarUI\\n 917 |        0x1bbc6f000 -        0x1bbc7d17f  com.apple.performance.SignpostCollection (1.151.8 - 151.8) <0AE27FD3-52CB-349A-B6ED-7C418BE204C2> /System/Library/PrivateFrameworks/SignpostCollection.framework/Versions/A/SignpostCollection\\n 918 |        0x1bbc7e000 -        0x1bbc7e2c7  com.apple.WebInspectorUI (20621 - 20621.********) <445D910F-D9DB-39EE-8E1D-B9BD72DFEB27> /System/Library/PrivateFrameworks/WebInspectorUI.framework/Versions/A/WebInspectorUI\\n 919 |        0x1bbc7f000 -        0x1bbcb5c7f  com.apple.OnBoardingKit (1.0 - 1) <C6536C5E-C365-3AE5-AF29-31939D83D9F7> /System/Library/PrivateFrameworks/OnBoardingKit.framework/Versions/A/OnBoardingKit\\n 920 |        0x1bbd57000 -        0x1bbd67cff  com.apple.CoreKDL (1.0 - 1) <13525501-9886-383E-86E9-8589545B06ED> /System/Library/PrivateFrameworks/CoreKDL.framework/Versions/A/CoreKDL\\n 921 |        0x1bbd68000 -        0x1bbde541f  com.apple.TrialProto (1.0 - 429.20.2) <A21F93FB-8896-3468-9B9E-058E9EA05E68> /System/Library/PrivateFrameworks/TrialProto.framework/Versions/A/TrialProto\\n 922 |        0x1bbde6000 -        0x1bbeab4df  com.apple.trial (1.0 - 429.20.2) <9001A0E6-196F-36C3-A91F-B9B5C8718664> /System/Library/PrivateFrameworks/Trial.framework/Versions/A/Trial\\n 923 |        0x1bbeac000 -        0x1bc330b3f  com.apple.SearchFoundation (1.0 - 3405.13.1) <********-4D30-3FFB-9302-F41121EFDD50> /System/Library/PrivateFrameworks/SearchFoundation.framework/Versions/A/SearchFoundation\\n 924 |        0x1bc331000 -        0x1bc6e7b3f  com.apple.Photos (1.0 - 760.6.150) <455E3CC6-4EB0-3A06-8BF6-AD285116A260> /System/Library/Frameworks/Photos.framework/Versions/A/Photos\\n 925 |        0x1bc6e8000 -        0x1bc7773bf  com.apple.ImageCaptureCore (1936.4.3 - 1936.4.3) <88FD40B3-F090-3CCC-B1E0-B023E6827589> /System/Library/Frameworks/ImageCaptureCore.framework/Versions/A/ImageCaptureCore\\n 926 |        0x1bc778000 -        0x1bc7a0d3f  com.apple.quartzfilters (1.10.0 - 103) <2045AA83-2F4C-3FD3-92B8-3E536806D776> /System/Library/Frameworks/Quartz.framework/Versions/A/Frameworks/QuartzFilters.framework/Versions/A/QuartzFilters\\n 927 |        0x1bc7a1000 -        0x1bc7e691f  com.apple.IntlPreferences (2.0 - 454.500) <EFECEB28-43BB-309F-B4F1-945CE0B355B8> /System/Library/PrivateFrameworks/IntlPreferences.framework/Versions/A/IntlPreferences\\n 928 |        0x1bc7e7000 -        0x1bc8204ff  com.apple.ToneLibrary (1.0 - 1) <7DA5BE6E-E180-3B4F-A826-6ACCAA968EB5> /System/Library/PrivateFrameworks/ToneLibrary.framework/Versions/A/ToneLibrary\\n 929 |        0x1bc821000 -        0x1bc829d7f  com.apple.CoreFollowUpUI (1.0 - 1) <DBD0AE9A-1A7B-3757-8FA5-EAB990756A5C> /System/Library/PrivateFrameworks/CoreFollowUpUI.framework/Versions/A/CoreFollowUpUI\\n 930 |        0x1bc8ec000 -        0x1bc98dd5f  com.apple.CallKit (1.0 - 1) <9E35035A-979C-31A1-9E0D-8E05B8C018D3> /System/Library/Frameworks/CallKit.framework/Versions/A/CallKit\\n 931 |        0x1bc98e000 -        0x1bca9593f  com.apple.ScreenTimeCore (3.0 - 537.5.7) <DF31908B-117B-33B0-8232-C9DBF6D11815> /System/Library/PrivateFrameworks/ScreenTimeCore.framework/Versions/A/ScreenTimeCore\\n 932 |        0x1bcaa0000 -        0x1bcab9d5f  com.apple.contextkit.ContextKit (1.0 - 1) <96310645-B2D7-3202-A915-6F2BAFED0F78> /System/Library/PrivateFrameworks/ContextKit.framework/Versions/A/ContextKit\\n 933 |        0x1bcaba000 -        0x1bcc42d1f  com.apple.Safari.Core (20621 - 20621.********) <E7E3000F-06F2-3F47-838A-7B42F7641227> /System/Library/PrivateFrameworks/SafariCore.framework/Versions/A/SafariCore\\n 934 |        0x1bcc43000 -        0x1bcefa93f  com.apple.MediaPlayer (1.0 - 4024.600.8) <81356350-6D3D-3F18-A7B3-77142EF52AD3> /System/Library/Frameworks/MediaPlayer.framework/Versions/A/MediaPlayer\\n 935 |        0x1bcefb000 -        0x1bd2b04ff  com.apple.iTunesCloud (1.0 - 4024.600.5) <30735CF7-4B7E-3570-B383-1B7785D45623> /System/Library/PrivateFrameworks/iTunesCloud.framework/Versions/A/iTunesCloud\\n 936 |        0x1bd2b7000 -        0x1bd2fb52b  libbootpolicy.dylib (265.100.17) <7A5E0FE5-658F-3732-BA39-49D13B575D62> /usr/lib/libbootpolicy.dylib\\n 937 |        0x1bd2fc000 -        0x1bd43aeff  com.apple.AnnotationKit (1.0 - 522.200) <D3AB1424-8912-3A63-B873-CAD0F3A8CA6B> /System/Library/PrivateFrameworks/AnnotationKit.framework/Versions/A/AnnotationKit\\n 938 |        0x1bd43b000 -        0x1bd92675f  com.apple.QuartzComposer (5.1 - 386) <182FE7B5-13A6-312B-AEFE-DF1CF5D81B50> /System/Library/Frameworks/Quartz.framework/Versions/A/Frameworks/QuartzComposer.framework/Versions/A/QuartzComposer\\n 939 |        0x1bd927000 -        0x1bda4dc3f  com.apple.PDFKit (1.0 - 1332.5.2) <1F3351E9-DBA2-3D27-8341-6E1D3FFF4F71> /System/Library/Frameworks/PDFKit.framework/Versions/A/PDFKit\\n 940 |        0x1bda4e000 -        0x1be0975df  com.apple.SceneKit (1.0 - 603.500) <AF594F94-17EA-3D16-B3E7-57E4E0E37E76> /System/Library/Frameworks/SceneKit.framework/Versions/A/SceneKit\\n 941 |        0x1be383000 -        0x1be38debf  com.apple.BridgeXPC (1.0 - 39) <94BC509C-9823-3E1C-BF57-6ADAFBA0FA98> /System/Library/PrivateFrameworks/BridgeXPC.framework/Versions/A/BridgeXPC\\n 942 |        0x1be38e000 -        0x1be3bbe5f  com.apple.skp.FeedbackLogger (1.0 - 1) <FA470749-7547-3CCF-B3D2-52A6923B4EF8> /System/Library/PrivateFrameworks/FeedbackLogger.framework/Versions/A/FeedbackLogger\\n 943 |        0x1be3bc000 -        0x1be5c04ff  com.apple.AppleMediaServicesUI (1.0 - 1) <37DC2C45-486F-3E55-9D07-6F6B7B35CDB7> /System/Library/PrivateFrameworks/AppleMediaServicesUI.framework/Versions/A/AppleMediaServicesUI\\n 944 |        0x1be5c1000 -        0x1be5e2d7f  com.apple.DistributionKit (700 - 1000) <A1B89BDB-D96B-3E81-BC32-4E54C5F72DD9> /System/Library/PrivateFrameworks/Install.framework/Frameworks/DistributionKit.framework/Versions/A/DistributionKit\\n 945 |        0x1be5e3000 -        0x1be631c7f  com.apple.OSPersonalization (1.0 - 149) <103811A9-3088-3B49-9F5B-35E5A8CF3E75> /System/Library/PrivateFrameworks/OSPersonalization.framework/Versions/A/OSPersonalization\\n 946 |        0x1beaee000 -        0x1beb4cb7f  com.apple.CommerceKit (1.2.0 - 715.5.1) <E398C6AB-5D89-39A1-9A1E-7D56F8CC5D84> /System/Library/PrivateFrameworks/CommerceKit.framework/Versions/A/CommerceKit\\n 947 |        0x1beb4d000 -        0x1beb52bbf  com.apple.ServerInformation (2.0 - 1) <037CCBC7-4F53-3CF8-A12E-356B24224BBE> /System/Library/PrivateFrameworks/ServerInformation.framework/Versions/A/ServerInformation\\n 948 |        0x1beb53000 -        0x1beb7765f  com.apple.icloud.FMCore (1.0 - 1) <E27FB584-CEC3-3367-B2EE-EA44CADC6DF0> /System/Library/PrivateFrameworks/FMCore.framework/Versions/A/FMCore\\n 949 |        0x1beca4000 -        0x1becbba4b  libswiftsimd.dylib (21) <195DAB0B-CF70-3BDF-B13D-908936E5C65D> /usr/lib/swift/libswiftsimd.dylib\\n 950 |        0x1bed18000 -        0x1bed3e91f  com.apple.MobileInstallation (2.0 - 1.0) <C2DFAF55-F3A7-3C7C-BFB7-A659D6718AFB> /System/Library/PrivateFrameworks/MobileInstallation.framework/Versions/A/MobileInstallation\\n 951 |        0x1bed4d000 -        0x1bee26aff  com.apple.TextInput (1.0 - 1.0) <6B862A3F-1DEB-3AD5-A6B6-C1243BCA196F> /System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput\\n 952 |        0x1befe4000 -        0x1bf9a0e3f  com.apple.PhotoLibraryServices (1.0 - 760.6.150) <117EA50E-9F6A-3B64-959F-8326A5C75610> /System/Library/PrivateFrameworks/PhotoLibraryServices.framework/Versions/A/PhotoLibraryServices\\n 953 |        0x1bfb8e000 -        0x1bfb9b6ff  com.apple.CaptiveNetworkSupport (13.0 - 1) <1EF1AD76-2FC0-3EAD-9A7A-75FD1466AC50> /System/Library/PrivateFrameworks/CaptiveNetwork.framework/Versions/A/CaptiveNetwork\\n 954 |        0x1bfcdf000 -        0x1bfd444df  com.apple.StoreFoundation (1.0 - 715.5.1) <FB1EADF1-D19A-3715-80F9-990332EC30F1> /System/Library/PrivateFrameworks/StoreFoundation.framework/Versions/A/StoreFoundation\\n 955 |        0x1bfff2000 -        0x1c000c13f  com.apple.LookupFramework (1.2 - 312.1) <6BBCC0BE-A6A0-37EF-8AED-D306C212A3E5> /System/Library/PrivateFrameworks/Lookup.framework/Versions/A/Lookup\\n 956 |        0x1c000d000 -        0x1c004923f  libncurses.5.4.dylib (79) <79DED6DB-F92F-38CE-8C75-DF07192580E1> /usr/lib/libncurses.5.4.dylib\\n 957 |        0x1c004a000 -        0x1c005329f  com.apple.IOAccelMemoryInfo (1.0 - 1) <AD7AD549-2CB1-3E7B-8916-0D979641E95F> /System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo\\n 958 |        0x1c00d5000 -        0x1c00d5677  com.apple.quartzframework (1.5 - 26) <A1C1A614-E025-3C63-908F-1C6654823C02> /System/Library/Frameworks/Quartz.framework/Versions/A/Quartz\\n 959 |        0x1c015a000 -        0x1c01790df  com.apple.IntentsCore (1.0 - 1) <0FFD6566-BDAE-3F09-ABCB-55E0A083E498> /System/Library/PrivateFrameworks/IntentsCore.framework/Versions/A/IntentsCore\\n 960 |        0x1c017a000 -        0x1c02388bf  com.apple.NearField (355.4 - 355.4) <6E56BC58-6380-323B-BFBC-7BBBD3A2BEC4> /System/Library/PrivateFrameworks/NearField.framework/Versions/A/NearField\\n 961 |        0x1c0239000 -        0x1c024a647  com.apple.GPUInfo (1.3.14 - 1.3.14) <02DC33B9-3658-388A-BA11-1DDC7EC7EAC9> /System/Library/PrivateFrameworks/GPUInfo.framework/Versions/A/GPUInfo\\n 962 |        0x1c024b000 -        0x1c0253e1f  com.apple.BridgeOSSoftwareUpdate (1.0 - 1) <FC339835-9B29-39A8-A0D6-D930F66FC37A> /System/Library/PrivateFrameworks/BridgeOSSoftwareUpdate.framework/Versions/A/BridgeOSSoftwareUpdate\\n 963 |        0x1c0254000 -        0x1c0263d5f  com.apple.InstallerDiagnostics (1.0 - 1) <05480FBD-6539-3A13-A4DE-604FC89048BB> /System/Library/PrivateFrameworks/InstallerDiagnostics.framework/Versions/A/InstallerDiagnostics\\n 964 |        0x1c0298000 -        0x1c045cbbf  com.apple.AOSKit (1.07 - 282) <8E02E992-CC49-3388-8761-AE83C1B996C6> /System/Library/PrivateFrameworks/AOSKit.framework/Versions/A/AOSKit\\n 965 |        0x1c06ee000 -        0x1c145e79f  com.apple.siri.SiriInstrumentation (1.0 - 1) <083EA419-E2CC-3183-8A19-D012AE9F41E2> /System/Library/PrivateFrameworks/SiriInstrumentation.framework/Versions/A/SiriInstrumentation\\n 966 |        0x1c147a000 -        0x1c148f5f9  libswiftCoreLocation.dylib (44.0.1) <B05C5E6E-C3CD-31B5-8407-38CE16E35C1B> /usr/lib/swift/libswiftCoreLocation.dylib\\n 967 |        0x1c1490000 -        0x1c1498faf  libswiftCoreMIDI.dylib (6) <230E7C7F-0864-3124-8BBB-FFFAD8357C08> /usr/lib/swift/libswiftCoreMIDI.dylib\\n 968 |        0x1c14d3000 -        0x1c169251f  com.apple.IMDPersistence (10.0 - 1000) <7BAD18F9-806A-3129-88E3-68B5FEB5F005> /System/Library/PrivateFrameworks/IMDPersistence.framework/Versions/A/IMDPersistence\\n 969 |        0x1c1693000 -        0x1c169967f  com.apple.idskvstore (10.0 - 1000) <B5113C07-9D7F-3F71-BF2E-3CD4F89BFC05> /System/Library/PrivateFrameworks/IDSKVStore.framework/Versions/A/IDSKVStore\\n 970 |        0x1c169a000 -        0x1c16d785f  com.apple.LocalAuthenticationUI (1.0 - 1656.120.6) <DBC7CD17-3751-3849-92A2-94149C5BC776> /System/Library/PrivateFrameworks/LocalAuthenticationUI.framework/Versions/A/LocalAuthenticationUI\\n 971 |        0x1c1792000 -        0x1c19147df  com.apple.AddressBook.framework (14.0 - 2695.500.71) <AB9C4DF2-93C0-3A96-BF3C-1B7027D15EBC> /System/Library/Frameworks/AddressBook.framework/Versions/A/AddressBook\\n 972 |        0x1c1915000 -        0x1c193309f  com.apple.ToneKit (1.0 - 1) <71248377-4E0E-3F5A-86F3-C273E33A489B> /System/Library/PrivateFrameworks/ToneKit.framework/Versions/A/ToneKit\\n 973 |        0x1c1934000 -        0x1c19c795f  com.apple.AppStoreDaemon (1.0 - 1) <F4D99680-7A03-3A18-B0E7-E21FC1415929> /System/Library/PrivateFrameworks/AppStoreDaemon.framework/Versions/A/AppStoreDaemon\\n 974 |        0x1c1ab9000 -        0x1c1abfbdf  com.apple.MSUDataAccessor (1.0 - 1) <C2E8F7E2-0B98-3E42-9414-75A17C6A8708> /System/Library/PrivateFrameworks/MSUDataAccessor.framework/Versions/A/MSUDataAccessor\\n 975 |        0x1c1b50000 -        0x1c1b85d9f  libnfshared.dylib (355.4) <3B210206-CFF6-3647-9E69-72B1165230F2> /usr/lib/libnfshared.dylib\\n 976 |        0x1c1b86000 -        0x1c1cfe3df  com.apple.Navigation (1.0 - 1) <BC06FC3A-17F8-39AD-9611-1042E028B98F> /System/Library/PrivateFrameworks/Navigation.framework/Versions/A/Navigation\\n 977 |        0x1c1cff000 -        0x1c1d189ff  com.apple.ExternalAccessory (1.0.0 - 1.0) <6E939FE5-0457-3F1C-8B98-1B8C14EB4AFB> /System/Library/Frameworks/ExternalAccessory.framework/ExternalAccessory\\n 978 |        0x1c1d19000 -        0x1c1d3325f  com.apple.IAP (1.0 - 1.0.0) <92328775-036E-3899-8187-3BB11347C0FC> /System/Library/PrivateFrameworks/IAP.framework/Versions/A/IAP\\n 979 |        0x1c1e33000 -        0x1c1e93adf  com.apple.SoftwareUpdateCoreSupport (1.0 - 1) <6E0377B7-A837-32BB-A20A-A15AF8C9EC7B> /System/Library/PrivateFrameworks/SoftwareUpdateCoreSupport.framework/Versions/A/SoftwareUpdateCoreSupport\\n 980 |        0x1c1eaf000 -        0x1c1eb265f  com.apple.ftclientservices (10.0 - 1000) <6BE62C58-12AD-3CC5-85F4-C70C4436B7EA> /System/Library/PrivateFrameworks/FTClientServices.framework/Versions/A/FTClientServices\\n 981 |        0x1c1eb3000 -        0x1c1f3db9f  com.apple.CharacterPicker (1.0 - 198) <EB7201D2-035F-3D53-A0E9-6158142A5A7A> /System/Library/PrivateFrameworks/CharacterPicker.framework/Versions/A/CharacterPicker\\n 982 |        0x1c2087000 -        0x1c22364df  com.apple.AOSUI (1.2 - 876.475) <A2C18C78-7EDF-320E-9B18-98310EE08BFD> /System/Library/PrivateFrameworks/AOSUI.framework/Versions/A/AOSUI\\n 983 |        0x1c2237000 -        0x1c224469f  com.apple.icloud.FindMyDeviceUI (1.0 - 18) <1FC2200B-3D15-394D-A1C8-6BEA1BB21DD4> /System/Library/PrivateFrameworks/FindMyDeviceUI.framework/Versions/A/FindMyDeviceUI\\n 984 |        0x1c228a000 -        0x1c22beaff  libtailspin.dylib (218.2) <94AC4671-73A2-38DE-ACC2-56FFFFD34F19> /usr/lib/libtailspin.dylib\\n 985 |        0x1c22bf000 -        0x1c39e5e7f  com.apple.SwiftUI (6.5.4 - 6.5.4) <3F264631-4839-3187-A7F6-3F3852D57C3C> /System/Library/Frameworks/SwiftUI.framework/Versions/A/SwiftUI\\n 986 |        0x1c39e6000 -        0x1c3a28d9f  com.apple.AttributeGraph (6.5.1 - 6.5.1) <24B5A67B-8123-3340-9461-F47921F72E27> /System/Library/PrivateFrameworks/AttributeGraph.framework/Versions/A/AttributeGraph\\n 987 |        0x1c3a29000 -        0x1c3aab71f  com.apple.EmojiFoundation (1.0 - 1) <63F932D3-7274-38C3-805A-0FBBA77E94EB> /System/Library/PrivateFrameworks/EmojiFoundation.framework/Versions/A/EmojiFoundation\\n 988 |        0x1c3aac000 -        0x1c3b89a9f  com.apple.CoreCDPInternal (1.0 - 1) <1EF7708D-EA8A-3A44-A1CD-E4F945A77903> /System/Library/PrivateFrameworks/CoreCDPInternal.framework/Versions/A/CoreCDPInternal\\n 989 |        0x1c3b8a000 -        0x1c44366df  libfaceCore.dylib (8.0.78) <C3FCDF73-3C1E-3567-8BF2-C2A0C49ABD54> /System/Library/Frameworks/Vision.framework/libfaceCore.dylib\\n 990 |        0x1c4437000 -        0x1c468317f  com.apple.TextRecognition (1.0 - 157) <5F222EEC-92CB-3C3B-8465-F0A32EBD3255> /System/Library/PrivateFrameworks/TextRecognition.framework/Versions/A/TextRecognition\\n 991 |        0x1c4684000 -        0x1c469b6bf  com.apple.Futhark (1.0 - 1) <2F59761F-A234-3DEA-A743-F08F0EDB1031> /System/Library/PrivateFrameworks/Futhark.framework/Versions/A/Futhark\\n 992 |        0x1c469c000 -        0x1c472c0bf  com.apple.DifferentialPrivacy (1.0 - 1) <D1C354C8-1C7C-315A-A250-5CCF4F3B0735> /System/Library/PrivateFrameworks/DifferentialPrivacy.framework/Versions/A/DifferentialPrivacy\\n 993 |        0x1c472d000 -        0x1c4766dbf  com.apple.SafariFoundation (20621 - 20621.********) <F05A30D9-F94B-383F-8ECC-89242ABAF630> /System/Library/PrivateFrameworks/SafariFoundation.framework/Versions/A/SafariFoundation\\n 994 |        0x1c4b91000 -        0x1c4bcc52f  com.apple.MobileBluetooth (1.0 - 1.0) <C3AF2986-C9AC-3833-83C0-E0C2BF91D6CB> /System/Library/PrivateFrameworks/MobileBluetooth.framework/Versions/A/MobileBluetooth\\n 995 |        0x1c4bd3000 -        0x1c4caf01f  com.apple.AuthenticationServices (12.0 - 20621.********) <D41944D5-0EBA-3AB5-BB2E-CF184F0A07D8> /System/Library/Frameworks/AuthenticationServices.framework/Versions/A/AuthenticationServices\\n 996 |        0x1c573a000 -        0x1c5790c7f  com.apple.biome.BiomeFoundation (1.0 - 166.23.1) <792F78BD-E24B-37C5-A229-E77317F4CD66> /System/Library/PrivateFrameworks/BiomeFoundation.framework/Versions/A/BiomeFoundation\\n 997 |        0x1c5791000 -        0x1c5795c9f  com.apple.DAAPKit (1.0 - 4024.500.19) <B228E631-57EC-3407-B327-5B301B34022D> /System/Library/PrivateFrameworks/DAAPKit.framework/Versions/A/DAAPKit\\n 998 |        0x1c581a000 -        0x1c58d75ff  com.apple.icloud.SPOwner (1.0 - 396.25.2.11.23) <8CF6CC2E-FBE1-377B-A2D2-DD59BD874273> /System/Library/PrivateFrameworks/SPOwner.framework/Versions/A/SPOwner\\n 999 |        0x1c7ccb000 -        0x1c7d760df  com.apple.MMCS (1.3 - 2250.16) <1ED0EDDB-BC0F-3408-87DB-936BE9C7DD83> /System/Library/PrivateFrameworks/MMCS.framework/Versions/A/MMCS\\n1000 |        0x1c7d77000 -        0x1c7ded8df  com.apple.acg.InertiaCam (1.0 - 1) <81763B89-AFAB-3B69-AB14-643E41CA0DF9> /System/Library/PrivateFrameworks/InertiaCam.framework/Versions/A/InertiaCam\\n1001 |        0x1c7e56000 -        0x1c7f70dff  com.apple.ConfigurationEngineModel (1.0 - 221.5.1) <E1E95998-72DC-332B-9E77-A26DA435B886> /System/Library/PrivateFrameworks/ConfigurationEngineModel.framework/Versions/A/ConfigurationEngineModel\\n1002 |        0x1c7fa3000 -        0x1c7fa674b  libpmenergy.dylib (287.120.2) <D768F2D8-8D8B-34A6-A175-04A8BEF7A707> /usr/lib/libpmenergy.dylib\\n1003 |        0x1c7fa7000 -        0x1c7fae3b8  libswiftMetal.dylib (368.12) <F5EEDBC1-3000-37E2-B383-D622E1FBAB90> /usr/lib/swift/libswiftMetal.dylib\\n1004 |        0x1c7faf000 -        0x1c7fb5e5f  libswiftCompression.dylib (8) <0A456196-6E0B-3A67-8B96-7FE4B108771F> /usr/lib/swift/libswiftCompression.dylib\\n1005 |        0x1c8319000 -        0x1c832167f  com.apple.SpotlightReceiver (1.0 - 2333.50.1) <9E27A239-C171-36D0-B05F-B1E6D98DA174> /System/Library/PrivateFrameworks/SpotlightReceiver.framework/Versions/A/SpotlightReceiver\\n1006 |        0x1c856e000 -        0x1c8820e9f  com.apple.imcore (10.0 - 1000) <6F3DA6FD-62B1-3F71-BB19-94536E1A0DD3> /System/Library/PrivateFrameworks/IMCore.framework/Versions/A/IMCore\\n1007 |        0x1c8821000 -        0x1c883d8df  com.apple.ftawd (8.0 - 900) <96937E13-543B-307B-AD4E-F28B0B5379FE> /System/Library/PrivateFrameworks/FTAWD.framework/Versions/A/FTAWD\\n1008 |        0x1c8bff000 -        0x1c8c02f9f  com.apple.iChat.InstantMessage (8.0 - 5501) <7077A5F1-9513-382F-A1DD-FBB9E6504219> /System/Library/Frameworks/InstantMessage.framework/Versions/A/InstantMessage\\n1009 |        0x1c8c0f000 -        0x1c8cb77ff  libFDR.dylib (1300.120.3) <7130CAB2-AFA1-3AC6-9214-412C3E0F8EA8> /usr/lib/libFDR.dylib\\n1010 |        0x1c8cb8000 -        0x1c8d3533f  com.apple.TimeSync (1.0 - 1340.12) <172DE7B4-1452-3C75-BED5-F95151671351> /System/Library/PrivateFrameworks/TimeSync.framework/Versions/A/TimeSync\\n1011 |        0x1c8d36000 -        0x1c9344b3f  com.apple.biome.BiomeStreams (1.0 - 166.23.1) <26B89FFE-B4F2-337D-AB79-43E1F9666823> /System/Library/PrivateFrameworks/BiomeStreams.framework/Versions/A/BiomeStreams\\n1012 |        0x1c9345000 -        0x1c9356e7f  com.apple.framework.ctcategories (1.0 - 44.4.1) <8CB7D177-FA55-3E65-9EF6-68198E80F7CB> /System/Library/PrivateFrameworks/Categories.framework/Versions/A/Categories\\n1013 |        0x1c97c0000 -        0x1c97d615f  com.apple.SoftwareUpdateCoreConnect (1.0 - 1) <36B642E8-314D-3895-853D-6D8E4A67FB22> /System/Library/PrivateFrameworks/SoftwareUpdateCoreConnect.framework/Versions/A/SoftwareUpdateCoreConnect\\n1014 |        0x1c97ef000 -        0x1c9864f5f  com.apple.StorageKit (1.0 - 934.120.4) <C972B284-6771-3ADE-A596-5F3E8363DC52> /System/Library/PrivateFrameworks/StorageKit.framework/Versions/A/StorageKit\\n1015 |        0x1ca80e000 -        0x1ca8172df  com.apple.audio.IOKitten (300.1 - 300.1) <AF91915B-56FE-3EF0-BAF2-93F4D89C1875> /System/Library/PrivateFrameworks/IOKitten.framework/Versions/A/IOKitten\\n1016 |        0x1cabd5000 -        0x1cac0469f  com.apple.coreduet.KnowledgeMonitor (1.0 - 1) <9FECE5A2-5072-308C-A104-C3723C64AA2E> /System/Library/PrivateFrameworks/KnowledgeMonitor.framework/Versions/A/KnowledgeMonitor\\n1017 |        0x1cac05000 -        0x1cac48f1f  com.apple.UsageTracking (3.0 - 377.5.1) <0A243E4D-3A3C-3D6A-A7C0-739625BFA810> /System/Library/PrivateFrameworks/UsageTracking.framework/Versions/A/UsageTracking\\n1018 |        0x1cac49000 -        0x1cc00c33f  com.apple.VectorKit (1.0 - 1948.25.3.6.5) <8FEBBDC5-E3F1-3DA5-B5EF-080EDF5679F8> /System/Library/PrivateFrameworks/VectorKit.framework/Versions/A/VectorKit\\n1019 |        0x1cc00d000 -        0x1cc06ad1f  com.apple.osanalytics.OSAnalytics (1.0 - 1) <BDC9F0D5-0F10-395C-90D3-05236ABCD29B> /System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics\\n1020 |        0x1cc094000 -        0x1cc0fdedf  com.apple.NetworkServiceProxyFramework (1.0 - 1) <483A572B-99E7-35D7-A6F8-77511DE80CA9> /System/Library/PrivateFrameworks/NetworkServiceProxy.framework/Versions/A/NetworkServiceProxy\\n1021 |        0x1cc129000 -        0x1cc13bb7f  com.apple.CloudPhotoServicesConfiguration (10.0 - 760.6.150) <4402FF07-B347-3825-A500-1F57BF8CF3E8> /System/Library/PrivateFrameworks/CloudPhotoServicesConfiguration.framework/Versions/A/CloudPhotoServicesConfiguration\\n1022 |        0x1cc15b000 -        0x1cc3989df  com.apple.CloudPhotoLibrary (1.0 - 760.6.150) <6FC13940-1561-3289-A37C-4C8EA53241EA> /System/Library/PrivateFrameworks/CloudPhotoLibrary.framework/Versions/A/CloudPhotoLibrary\\n1023 |        0x1cc3c1000 -        0x1cc735667  com.apple.SDAPI (1.0 - 1) <C48CCCBD-909D-363B-A009-21CCA287EF95> /System/Library/PrivateFrameworks/SDAPI.framework/Versions/A/SDAPI\\n1024 |        0x1cd355000 -        0x1cd3b7f3f  com.apple.Transparency (1.0 - 1218.120.15) <7001DF42-75CA-3612-82EB-42B2E36D87A6> /System/Library/PrivateFrameworks/Transparency.framework/Versions/A/Transparency\\n1025 |        0x1cd3ce000 -        0x1cd3cfe25  libswiftQuartzCore.dylib (4) <EBD71EE2-8F5B-3885-BD8E-87E20E4E4EC7> /usr/lib/swift/libswiftQuartzCore.dylib\\n1026 |        0x1cd3d1000 -        0x1cd41fe7f  com.apple.network.statistics.framework (1.2 - 1) <0315D39E-3752-3F81-8A42-84E208E27ED8> /System/Library/PrivateFrameworks/NetworkStatistics.framework/Versions/A/NetworkStatistics\\n1027 |        0x1cd7c3000 -        0x1cd7c8967  com.apple.kperf (1.0 - 1) <99B8B404-82DC-3995-B9CA-8325B9742F9B> /System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf\\n1028 |        0x1cd7c9000 -        0x1cd8b83df  com.apple.libktrace (1.0 - 649.120.3) <5879F088-6763-3D17-8C7D-E5CFC25825A8> /System/Library/PrivateFrameworks/ktrace.framework/Versions/A/ktrace\\n1029 |        0x1cd8b9000 -        0x1cd8c00df  com.apple.BezelServicesFW (368 - 368) <96F0BBE2-98D3-3978-98EC-D78486B7C76F> /System/Library/PrivateFrameworks/BezelServices.framework/Versions/A/BezelServices\\n1030 |        0x1cd8c1000 -        0x1cd8cc1ff  com.apple.appstore.foundation (1.0 - 1) <161E1B43-8AE5-32A6-B0EA-3DBEF02213D6> /System/Library/PrivateFrameworks/AppStoreFoundation.framework/Versions/A/AppStoreFoundation\\n1031 |        0x1cd8cd000 -        0x1cd8d245f  com.apple.MobileSystemServices (1.0 - 1) <17002321-7933-3C20-93F1-74D35A3E001E> /System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices\\n1032 |        0x1cd936000 -        0x1cd964dbf  com.apple.frameworks.preferencepanes (16.0 - 16.0) <A78EAB07-B81A-3181-BB5A-97D51473F1F0> /System/Library/Frameworks/PreferencePanes.framework/Versions/A/PreferencePanes\\n1033 |        0x1cd965000 -        0x1cd96da1f  com.apple.InAppMessagesCore (1.0 - 1) <A867F8C7-AEDD-3DD9-979D-BD1244D2C4E5> /System/Library/PrivateFrameworks/InAppMessagesCore.framework/Versions/A/InAppMessagesCore\\n1034 |        0x1cd96e000 -        0x1cd99219f  com.apple.InAppMessages (1.0 - 1) <B6D423BB-A39A-38CE-9772-6B608168B2FE> /System/Library/PrivateFrameworks/InAppMessages.framework/Versions/A/InAppMessages\\n1035 |        0x1cd993000 -        0x1cda1a69f  com.apple.EmailCore (11.0 - 3826.600.51.1.1) <1AB313B8-768A-3F25-9C52-2EA9BB468586> /System/Library/PrivateFrameworks/EmailCore.framework/Versions/A/EmailCore\\n1036 |        0x1cda5f000 -        0x1cda64b3f  com.apple.FindMyMac (3.1 - 71.25.2.11.3) <5BCD8B54-5D86-36EC-97D8-4A8CF3799262> /System/Library/PrivateFrameworks/FindMyMac.framework/Versions/A/FindMyMac\\n1037 |        0x1cda65000 -        0x1cda737ff  com.apple.MobileActivation (1.0 - 1017.120.3) <1648F629-7D55-3AD4-BB60-A22415C0C634> /System/Library/PrivateFrameworks/MobileActivationMacOS.framework/Versions/A/MobileActivationMacOS\\n1038 |        0x1cdff8000 -        0x1ce02fd7f  com.apple.photo.MediaConversionService (10.0 - 760.6.150) <275B1027-EC87-3074-A8A2-C717F4718F5E> /System/Library/PrivateFrameworks/MediaConversionService.framework/Versions/A/MediaConversionService\\n1039 |        0x1ce065000 -        0x1ce0f287f  com.apple.signpost.SignpostSupport (1.151.8 - 151.8) <71EEE985-CBF5-33AC-A5C5-F679D359492D> /System/Library/PrivateFrameworks/SignpostSupport.framework/Versions/A/SignpostSupport\\n1040 |        0x1ce0f3000 -        0x1ce2b0e9f  com.apple.ModelIO (266.1.12 - 266.1.12) <6FC01850-9EEF-3F82-BEF9-42D56B8308C9> /System/Library/Frameworks/ModelIO.framework/Versions/A/ModelIO\\n1041 |        0x1ce2e5000 -        0x1ce43a25f  com.apple.SampleAnalysis (1.0 - 385.14) <C71F7F27-14D6-33AD-98A6-0D606C8B829F> /System/Library/PrivateFrameworks/SampleAnalysis.framework/Versions/A/SampleAnalysis\\n1042 |        0x1ce512000 -        0x1ce79ce7f  com.apple.photo.NeutrinoCore (1.0 - 760.6.150) <FEBEF55B-83E1-35C4-8F7A-4274E6DA476B> /System/Library/PrivateFrameworks/NeutrinoCore.framework/Versions/A/NeutrinoCore\\n1043 |        0x1ce955000 -        0x1ce9f71bf  com.apple.EmailFoundation (11.0 - 3826.600.51.1.1) <71292C37-856F-39D9-87A5-1EBAC53B6ABF> /System/Library/PrivateFrameworks/EmailFoundation.framework/Versions/A/EmailFoundation\\n1044 |        0x1cec6d000 -        0x1ced3fedf  libauthinstall.dylib (1049.100.23) <581AAACF-BD02-3939-A5E8-D5B968A0B8E8> /usr/lib/libauthinstall.dylib\\n1045 |        0x1ced40000 -        0x1ced5f1df  libamsupport.dylib (407.100.7) <DF2954D1-2CF1-3BD4-A556-B339B8C60A39> /usr/lib/libamsupport.dylib\\n1046 |        0x1cf0e3000 -        0x1cf0e5adf  com.apple.ScreenTimeServiceUI (3.0 - 537.5.7) <2E3BD64A-36D5-3EBB-BCD5-F56512F59F4E> /System/Library/PrivateFrameworks/ScreenTimeServiceUI.framework/Versions/A/ScreenTimeServiceUI\\n1047 |        0x1cf0ea000 -        0x1cf146f1f  com.apple.biome.BiomePubSub (1.0 - 166.23.1) <AB629060-197B-34D0-9EE7-85E167F93EE4> /System/Library/PrivateFrameworks/BiomePubSub.framework/Versions/A/BiomePubSub\\n1048 |        0x1cf147000 -        0x1cf188d5f  com.apple.biome.BiomeStorage (1.0 - 166.23.1) <E7B3CB77-46FC-3EC3-9925-E25A3AD876FF> /System/Library/PrivateFrameworks/BiomeStorage.framework/Versions/A/BiomeStorage\\n1049 |        0x1cf29f000 -        0x1cf399607  libcrypto.42.dylib (107) <881BCBEB-4CFE-3525-B1C0-54FFF8B2C424> /usr/lib/libcrypto.42.dylib\\n1050 |        0x1cf79f000 -        0x1cf90b2ff  com.apple.LinkPresentation (272 - 272.13) <EFF41843-7B24-3045-8BC7-7EF95E696F1D> /System/Library/Frameworks/LinkPresentation.framework/Versions/A/LinkPresentation\\n1051 |        0x1cfb80000 -        0x1cfca98ff  com.apple.PhotosFormats (1.0 - 760.6.150) <65E1ACED-D341-370C-A2D8-6BABBD2EF435> /System/Library/PrivateFrameworks/PhotosFormats.framework/Versions/A/PhotosFormats\\n1052 |        0x1d06b6000 -        0x1d06e7aff  com.apple.DeviceIdentity (1.0 - 1) <98F2996C-3C66-39F5-AAC7-0374742D0AF7> /System/Library/PrivateFrameworks/DeviceIdentity.framework/Versions/A/DeviceIdentity\\n1053 |        0x1d1780000 -        0x1d178baff  com.apple.EmailAddressing (11.0 - 3826.600.51.1.1) <CC545B97-368B-3E6A-B6D0-C3B00ACDCBA6> /System/Library/PrivateFrameworks/EmailAddressing.framework/Versions/A/EmailAddressing\\n1054 |        0x1d178c000 -        0x1d18f2ebf  com.apple.Email (11.0 - 3826.600.51.1.1) <678EDCB1-5CA8-3006-BF63-95A5ED62F49A> /System/Library/PrivateFrameworks/Email.framework/Versions/A/Email\\n1055 |        0x1d18f4000 -        0x1d1a1019f  com.apple.CoreMediaStream (1.0 - 760.6.150) <0BC29B03-F0D2-3569-8735-A96093376DD2> /System/Library/PrivateFrameworks/CoreMediaStream.framework/Versions/A/CoreMediaStream\\n1056 |        0x1d1a11000 -        0x1d1a1f815  libswiftUniformTypeIdentifiers.dylib (840.8) <A1EBA149-B2C0-395F-B446-C5A055D2392A> /usr/lib/swift/libswiftUniformTypeIdentifiers.dylib\\n1057 |        0x1d1a20000 -        0x1d1ad7a1f  libswiftAccelerate.dylib (62) <31820542-B1CF-3155-A449-BDF2060F0947> /usr/lib/swift/libswiftAccelerate.dylib\\n1058 |        0x1d1c18000 -        0x1d1c28a3f  libpartition2_dynamic.dylib (3196.120.16) <3B7EC512-222E-3990-93BA-BDC4AD104443> /usr/lib/libpartition2_dynamic.dylib\\n1059 |        0x1d20e3000 -        0x1d20eb89f  com.apple.AFKUser (1.0 - 1) <A63564F9-E6AD-3E7D-87C0-DBD92A11CABF> /System/Library/PrivateFrameworks/AFKUser.framework/Versions/A/AFKUser\\n1060 |        0x1d3efe000 -        0x1d413cf5f  com.apple.photo.PhotoImaging (1.0 - 760.6.150) <7D9924A6-961D-30D6-B15D-0C533AA0A4A5> /System/Library/PrivateFrameworks/PhotoImaging.framework/Versions/A/PhotoImaging\\n1061 |        0x1d510e000 -        0x1d514c61f  com.apple.PhotosImagingFoundation (10.0 - 760.6.150) <E7019D86-93F4-37C0-B16D-3473E0F9D21C> /System/Library/PrivateFrameworks/PhotosImagingFoundation.framework/Versions/A/PhotosImagingFoundation\\n1062 |        0x1d514d000 -        0x1d515a37f  com.apple.CloudPhotoServices (1.0 - 760.6.150) <8D75BBAA-7693-317E-86FF-6FD0DD18CA07> /System/Library/PrivateFrameworks/CloudPhotoServices.framework/Versions/A/CloudPhotoServices\\n1063 |        0x1d515b000 -        0x1d51b4fdf  com.apple.acg.AutoLoop (1.0 - 1) <C805A7EC-E8E1-33B2-B177-18DD710DF328> /System/Library/PrivateFrameworks/AutoLoop.framework/Versions/A/AutoLoop\\n1064 |        0x1d5263000 -        0x1d5522ebf  com.apple.Osprey (1.0 - 1) <D350F283-C322-3147-A4A3-86ED6E5EC18E> /System/Library/PrivateFrameworks/Osprey.framework/Versions/A/Osprey\\n1065 |        0x1d5765000 -        0x1d5766b66  libswiftCryptoTokenKit.dylib (738.100.34) <998A3BB0-85D8-3141-8EA3-7A87BC633B4C> /usr/lib/swift/libswiftCryptoTokenKit.dylib\\n1066 |        0x1d5767000 -        0x1d579d57a  libswiftCoreMedia.dylib (3225.7.1) <F06A0098-3770-354D-89FC-8D341D936DDE> /usr/lib/swift/libswiftCoreMedia.dylib\\n1067 |        0x1d6ad6000 -        0x1d6adf487  com.apple.kperfdata (1.0 - 1) <60E55451-3AC3-32FE-AAE3-25343A66CDFC> /System/Library/PrivateFrameworks/kperfdata.framework/Versions/A/kperfdata\\n1068 |        0x1d6aee000 -        0x1d6af9abf  com.apple.Reveal (1.0 - 52) <EB411279-0E66-3845-900A-91CB4C9304DA> /System/Library/PrivateFrameworks/Reveal.framework/Versions/A/Reveal\\n1069 |        0x1d6afa000 -        0x1d6b00e3f  com.apple.RevealCore (1.0 - 52) <34C9C49A-B47C-33CD-9B32-BCDE787AA5DC> /System/Library/PrivateFrameworks/RevealCore.framework/Versions/A/RevealCore\\n1070 |        0x1d6f6b000 -        0x1d6f7cb9f  com.apple.BulkSymbolication (1.385.14 - 385.14) <4CDEF6B9-6AEF-3B49-97E7-43DAD48C2674> /System/Library/PrivateFrameworks/BulkSymbolication.framework/Versions/A/BulkSymbolication\\n1071 |        0x1d6f7d000 -        0x1d6f7ed8b  libswiftOSLog.dylib (8) <1838592E-E12A-35DF-BC3C-F01D49D63C5B> /usr/lib/swift/libswiftOSLog.dylib\\n1072 |        0x1d720d000 -        0x1d723ee61  libswiftAVFoundation.dylib (2330.7.1) <FA1B1DA1-37F4-3905-8ED5-7088790EECAC> /usr/lib/swift/libswiftAVFoundation.dylib\\n1073 |        0x1daab3000 -        0x1dabafbdf  com.apple.Symbolication (16.0 - 64570.58.1) <1859E3F9-81F0-36DA-B8E3-DC965E12B225> /System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication\\n1074 |        0x1dabb0000 -        0x1dabe561f  com.apple.ScreenTimeUI (3.0 - 537.5.7) <70187E61-23E1-33AA-BFC9-16CFC5180B2B> /System/Library/PrivateFrameworks/ScreenTimeUI.framework/Versions/A/ScreenTimeUI\\n1075 |        0x1dac51000 -        0x1dac6955f  com.apple.performance.DiagnosticRequest (1.4 - 4) <2E8B2A6F-CFE4-371B-B48B-F34D23383763> /System/Library/PrivateFrameworks/DiagnosticRequest.framework/Versions/A/DiagnosticRequest\\n1076 |        0x1db22b000 -        0x1db2320ff  com.apple.framework.netrb (1.0 - 1) <C6E31A74-D547-310B-86B3-43C123705F2F> /System/Library/PrivateFrameworks/Netrb.framework/Versions/A/Netrb\\n1077 |        0x1db233000 -        0x1db266a9f  com.apple.frameworks.preferencepanessupport (13.0 - 13.0) <CDA2D911-B901-308D-B761-CCB360634A93> /System/Library/PrivateFrameworks/PreferencePanesSupport.framework/Versions/A/PreferencePanesSupport\\n1078 |        0x1db267000 -        0x1db28bfff  com.apple.CoreMaterial (1.0 - 1) <E785FA1F-8415-3915-A327-303D5DCF9A89> /System/Library/PrivateFrameworks/CoreMaterial.framework/Versions/A/CoreMaterial\\n1079 |        0x1db2ac000 -        0x1db2ae773  libpmsample.dylib (287.120.2) <6FCBA3E6-FF1B-3AD7-88B3-FF87E48FF171> /usr/lib/libpmsample.dylib\\n1080 |        0x1db3e3000 -        0x1db3e53df  com.apple.framework.machinesettings (11.0 - 11.0) <192207CD-A8B3-37F7-83D5-9744ECED2840> /System/Library/PrivateFrameworks/MachineSettings.framework/Versions/A/MachineSettings\\n1081 |        0x1db3e6000 -        0x1db4b1a9f  com.apple.CoreCDPUI (1.0 - 1) <7399CD02-3710-3ABE-915A-745BFA4C1770> /System/Library/PrivateFrameworks/CoreCDPUI.framework/Versions/A/CoreCDPUI\\n1082 |        0x1dceab000 -        0x1dcf4497f  com.apple.AppleCVA (1001.202.0 - 1001.202.0) <40E8CF2E-E545-32EE-88EE-82B0B9884587> /System/Library/PrivateFrameworks/AppleCVA.framework/Versions/A/AppleCVA\\n1083 |        0x1debae000 -        0x1debbe21f  com.apple.OSLog (1.0 - 1643.120.5) <C82AAFE0-0499-3B4A-AD61-771E02E8396A> /System/Library/Frameworks/OSLog.framework/Versions/A/OSLog\\n1084 |        0x1dede7000 -        0x1deef07ac  com.apple.InternalSwiftProtobuf (1.0 - 1.26.0) <2700ABEC-BDFD-335D-881E-E3414A7D04E4> /System/Library/PrivateFrameworks/InternalSwiftProtobuf.framework/Versions/A/InternalSwiftProtobuf\\n1085 |        0x1deef1000 -        0x1deef179f  com.apple.PassKit (1.0 - 1) <6DF66ABE-9E61-3998-9CF2-0B181C18DAE7> /System/Library/Frameworks/PassKit.framework/Versions/A/PassKit\\n1086 |        0x1defb5000 -        0x1defc02ff  com.apple.HIDDisplay (1.0 - 1) <F18BBD9E-1860-3524-B360-444714F65661> /System/Library/PrivateFrameworks/HIDDisplay.framework/Versions/A/HIDDisplay\\n1087 |        0x1defc1000 -        0x1df012dff  com.apple.ReplayKit (1.0 - 1) <DD02BB05-5FB0-3DBB-94DF-389C2CF86F81> /System/Library/Frameworks/ReplayKit.framework/Versions/A/ReplayKit\\n1088 |        0x1df013000 -        0x1df1a7c1f  com.apple.StoreKit (1.0 - 1) <DC4C8BAB-24E1-3722-93C3-C2101CD9A112> /System/Library/Frameworks/StoreKit.framework/Versions/A/StoreKit\\n1089 |        0x1df1a8000 -        0x1df1ad9df  com.apple.StoreKitUIMac (1.0 - 1) <74FA3206-1A97-3AE1-9EB1-D4347DB85BAD> /System/Library/PrivateFrameworks/StoreKitUIMac.framework/Versions/A/StoreKitUIMac\\n1090 |        0x1e0914000 -        0x1e12dbe5f  com.apple.BlastDoor (1.0 - 1) <4EC70D47-3C1F-3FFE-A9E5-2BED4D0ABDDF> /System/Library/PrivateFrameworks/BlastDoor.framework/Versions/A/BlastDoor\\n1091 |        0x1e13bd000 -        0x1e13bf88f  com.apple.ForceFeedback (1.0.6 - 1.0.6) <53F17F32-6831-30A8-973B-0140FE548764> /System/Library/Frameworks/ForceFeedback.framework/Versions/A/ForceFeedback\\n1092 |        0x1e14d2000 -        0x1e1546932  com.apple.security.CryptoKit (1.0 - 1) <1818CE24-26DF-350A-83C0-4A7940312368> /System/Library/Frameworks/CryptoKit.framework/Versions/A/CryptoKit\\n1093 |        0x1e16bc000 -        0x1e16d435f  com.apple.BluetoothManager (1.0 - 1) <5F6BECCD-A26B-3290-B35E-0C8EB936061D> /System/Library/PrivateFrameworks/BluetoothManager.framework/Versions/A/BluetoothManager\\n1094 |        0x1e1717000 -        0x1e174e69f  com.apple.PassKitUIFoundation (1.0 - 1591.6.5.1) <8B383782-F9A9-3308-B88F-F0307E06754B> /System/Library/PrivateFrameworks/PassKitUIFoundation.framework/Versions/A/PassKitUIFoundation\\n1095 |        0x1e24a1000 -        0x1e24eaabf  com.apple.RemoteConfiguration (3.0 - 270) <168154B3-D379-3E97-9CE3-5639CBE21E15> /System/Library/PrivateFrameworks/RemoteConfiguration.framework/Versions/A/RemoteConfiguration\\n1096 |        0x1e43f5000 -        0x1e4412dab  libedit.3.dylib (62) <1C5D4C2D-D427-336F-8C40-7A00F289B5AA> /usr/lib/libedit.3.dylib\\n1097 |        0x1e442b000 -        0x1e448643f  libswiftDemangle.dylib (*******.2) <CDEA69E8-ECE5-3C19-8DBA-79FEF3BA2196> /usr/lib/swift/libswiftDemangle.dylib\\n1098 |        0x1e452e000 -        0x1e453679f  com.apple.imtransferservices (10.0 - 1000) <77E13188-C355-36CB-8603-54484866B574> /System/Library/PrivateFrameworks/IMTransferServices.framework/Versions/A/IMTransferServices\\n1099 |        0x1e5430000 -        0x1e5746d5f  com.apple.newstransport (10.4 - 5681) <7C6F92FD-5C76-3A7E-85B0-F821535235C0> /System/Library/PrivateFrameworks/NewsTransport.framework/Versions/A/NewsTransport\\n1100 |        0x1e57e6000 -        0x1e57f565f  com.apple.newsfoundation (10.4 - 5681) <AD622E32-9D81-3964-9E97-F6903314D97F> /System/Library/PrivateFrameworks/NewsFoundation.framework/Versions/A/NewsFoundation\\n1101 |        0x1e57f6000 -        0x1e5d89d7f  com.apple.newscore (10.4 - 5681) <D1885E9E-29E0-3676-A867-92AFC356136C> /System/Library/PrivateFrameworks/NewsCore.framework/Versions/A/NewsCore\\n1102 |        0x1e610c000 -        0x1e610cc5b  com.apple.FeatureFlags (1.0 - 97) <C12B23C3-A097-3252-9D2F-555CD77CC52A> /System/Library/PrivateFrameworks/FeatureFlags.framework/Versions/A/FeatureFlags\\n1103 |        0x1e6118000 -        0x1e611feed  libswiftNaturalLanguage.dylib (4.3) <0F108661-8876-310B-8DF9-A4E99E4322F1> /usr/lib/swift/libswiftNaturalLanguage.dylib\\n1104 |        0x1e6194000 -        0x1e61c61bf  com.apple.WiFiPeerToPeer (725.39.0 - 725.39) <0111CA4B-8B67-3B17-9271-D33344FBB82C> /System/Library/PrivateFrameworks/WiFiPeerToPeer.framework/Versions/A/WiFiPeerToPeer\\n1105 |        0x1ee8f7000 -        0x1ee9ef9bf  com.apple.JetUI (1.0 - 1) <CDBB78F5-5934-3BC8-B378-1A04520370C1> /System/Library/PrivateFrameworks/JetUI.framework/Versions/A/JetUI\\n1106 |        0x1ee9f0000 -        0x1eeed2cff  com.apple.JetEngine (1.0 - 1) <D6983CDD-3F0E-3F3A-8A71-A6881732A737> /System/Library/PrivateFrameworks/JetEngine.framework/Versions/A/JetEngine\\n1107 |        0x1ef3d5000 -        0x1ef3f84e7  libswiftSwiftOnoneSupport.dylib (6.1.2 - *******.2) <78B13C24-32FD-33FF-9C01-E0C500D74B37> /usr/lib/swift/libswiftSwiftOnoneSupport.dylib\\n1108 |        0x1ef94b000 -        0x1ef94d65f  com.apple.ConfigProfileHelper (17.1 - 1722) <9FD67B5B-AB35-398F-BC64-B07AD2F2E727> /System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper\\n1109 |        0x1efba9000 -        0x1efc09cdf  com.apple.ScreenReaderCore (10 - 964.12.11) <740A579E-4944-3405-8681-A170351FD6B5> /System/Library/PrivateFrameworks/ScreenReaderCore.framework/Versions/A/ScreenReaderCore\\n1110 |        0x1efc80000 -        0x1efcec7ff  com.apple.RemoteManagement (1.0 - 2.0) <24AAF730-1EA0-3471-9440-A2275DD694B8> /System/Library/PrivateFrameworks/RemoteManagement.framework/Versions/A/RemoteManagement\\n1111 |        0x1eff65000 -        0x1f12f937f  com.apple.ANECompiler (8.5.4 - 8.5.4) <05345D34-C74D-3FCE-ACB1-856D64FBC54D> /System/Library/PrivateFrameworks/ANECompiler.framework/Versions/A/ANECompiler\\n1112 |        0x1f12fa000 -        0x1f130a11f  com.apple.DeviceCheck (1.0 - 1) <36A211CD-7709-3998-BEA8-C16170575E82> /System/Library/Frameworks/DeviceCheck.framework/Versions/A/DeviceCheck\\n1113 |        0x1f28ef000 -        0x1f28f62f7  libCoreFSCache.dylib (301) <AF6B0A73-A420-3C7C-BF99-8D5A45493AB5> /System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib\\n1114 |        0x1f28f7000 -        0x1f28fc907  libCoreVMClient.dylib (301) <D346AAC7-0673-38DF-869F-757A4A45BC34> /System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib\\n1115 |        0x1f28fd000 -        0x1f290d277  com.apple.opengl (21.1.1 - 21.1.1) <0E45FD4E-B725-397D-B3DE-0168E0C0C860> /System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL\\n1116 |        0x1f290e000 -        0x1f29107af  libCVMSPluginSupport.dylib (21.1.2) <DB2ED11C-E798-3FE9-BA0E-A03E885BAD07> /System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib\\n1117 |        0x1f2911000 -        0x1f29196bf  libGFXShared.dylib (21.1.2) <3710611A-7A2F-3B31-8D20-85736430A368> /System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib\\n1118 |        0x1f291a000 -        0x1f294cce3  libGLImage.dylib (21.1.2) <F776E723-AE47-3F02-A0B6-53150D436649> /System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib\\n1119 |        0x1f294d000 -        0x1f298732f  libGLU.dylib (21.1.2) <461DB774-4689-3F22-95A6-4979A77FBD23> /System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib\\n1120 |        0x1f2ad9000 -        0x1f2ae2bcf  libGL.dylib (21.1.2) <109F67D6-5C4C-33A3-A77B-5CB007C858C7> /System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib\\n1121 |        0x1f2c69000 -        0x1f2cd81dd  com.apple.opencl (5.5 - 5.5) <B29C0EE1-48CE-3C5D-9C46-AED7C9593921> /System/Library/Frameworks/OpenCL.framework/Versions/A/OpenCL\\n1122 |        0x1f2e75000 -        0x1f2faa13f  com.apple.audio.AVFAudio (1.0 - 684.603) <5A271739-9F10-3DE4-870D-EF288D4B4E83> /System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio\\n1123 |        0x1f2fce000 -        0x1f3488fdf  com.apple.AppIntents (1.0 - 227.19) <F64F60E4-8613-3690-B5D7-1CCC3122ED08> /System/Library/Frameworks/AppIntents.framework/Versions/A/AppIntents\\n1124 |        0x1f39a7000 -        0x1f39a9d1f  com.apple.BrowserEngineCore (1.0 - 1) <9AE82C11-99D7-3118-8268-B2854BEF8422> /System/Library/Frameworks/BrowserEngineCore.framework/Versions/A/BrowserEngineCore\\n1125 |        0x1f3e42000 -        0x1f3e877ff  com.apple.CoreTransferable (1.0.1 - 1) <AACC6FF5-640F-3062-8C2D-8558AE3DD36C> /System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable\\n1126 |        0x1f43c6000 -        0x1f43c8abf  com.apple.DataDetection (8.0 - 801.2) <C64D7DBC-C59C-3AA1-99D3-FC510EEF4840> /System/Library/Frameworks/DataDetection.framework/Versions/A/DataDetection\\n1127 |        0x1f43c9000 -        0x1f43e175c  com.apple.dt.DeveloperToolsSupport (22.10.5 - 22.10.5) <03EA2A25-9CBD-3757-A81B-6705FC0BB39F> /System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport\\n1128 |        0x1f43e2000 -        0x1f448b0b9  com.apple.DeviceActivity (3.0 - 377.5.1) <2F5F2DF2-7C88-3039-A82C-1D39F086D99D> /System/Library/Frameworks/DeviceActivity.framework/Versions/A/DeviceActivity\\n1129 |        0x1f4516000 -        0x1f45dffdf  com.apple.ExtensionFoundation (97 - 97) <CB00C550-6E25-337C-9B3F-9EE705E191C4> /System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation\\n1130 |        0x1f45e0000 -        0x1f461bcdf  com.apple.ExtensionKit (97 - 97) <C85040BC-CE0A-3D6D-92ED-04A3352A3BC4> /System/Library/Frameworks/ExtensionKit.framework/Versions/A/ExtensionKit\\n1131 |        0x1f461c000 -        0x1f468053f  com.apple.FSKit (1.0 - 1) <DB4FD9C9-617D-3231-91B2-871FF4EB18CC> /System/Library/Frameworks/FSKit.framework/Versions/A/FSKit\\n1132 |        0x1f553a000 -        0x1f5600579  com.apple.ManagedSettings (242.4.10 - 242.4.10) <20AA71DC-D452-3E03-A4B8-0CCD772FDF11> /System/Library/Frameworks/ManagedSettings.framework/Versions/A/ManagedSettings\\n1133 |        0x1f61c6000 -        0x1f61e309f  com.apple.MPSBenchmarkLoop (1.0 - 1) <FAE31459-F7C8-3601-AE48-119CA3C1910F> /System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop\\n1134 |        0x1f61e4000 -        0x1f61f81bf  com.apple.MPSFunctions (1.0 - 1) <614DDB24-D3E7-3CD2-B1D8-30659F328176> /System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions\\n1135 |        0x1f61f9000 -        0x1f7305bbf  com.apple.MetalPerformanceShadersGraph (5.4.11 - 5.4.11) <D54E0BB3-F77F-3003-BCDF-CD9057C41DCE> /System/Library/Frameworks/MetalPerformanceShadersGraph.framework/Versions/A/MetalPerformanceShadersGraph\\n1136 |        0x1f7f07000 -        0x1f803bc7f  com.apple.QuickLookUIFramework (5.0 - 1002.5.2) <67A38D1C-5290-3821-9830-3E64C6EF1894> /System/Library/Frameworks/QuickLookUI.framework/Versions/A/QuickLookUI\\n1137 |        0x1f88e5000 -        0x1f893cbbf  com.apple.ScreenCaptureKit (1) <8B649AD4-1030-3BFE-9BF4-8FFC53132077> /System/Library/Frameworks/ScreenCaptureKit.framework/Versions/A/ScreenCaptureKit\\n1138 |        0x1f8969000 -        0x1f89860ff  com.apple.SensitiveContentAnalysis (1.0 - 1) <FA41CEF7-95B8-38CE-BEE9-EF9589B657CF> /System/Library/Frameworks/SensitiveContentAnalysis.framework/Versions/A/SensitiveContentAnalysis\\n1139 |        0x1f8a29000 -        0x1f8a4669f  com.apple.SharedWithYouCore (1.0 - 1) <A9A0BBF4-E0EF-33E5-8A27-F9521502123C> /System/Library/Frameworks/SharedWithYouCore.framework/Versions/A/SharedWithYouCore\\n1140 |        0x1f8df2000 -        0x1f8f2c022  com.apple.SwiftData (1.0 - 98.1) <B704C687-DCC7-354F-8DA7-47C15CB19EB0> /System/Library/Frameworks/SwiftData.framework/Versions/A/SwiftData\\n1141 |        0x1f8f2d000 -        0x1f9aef19f  com.apple.SwiftUICore (6.5.4 - 6.5.4) <AE57E82B-4E7F-3045-B3A4-F6461560C6FD> /System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore\\n1142 |        0x1f9af0000 -        0x1f9b00edf  com.apple.Symbols (1.0 - *********) <0F92BB94-B9F4-31DC-806D-4AEF0B673DFC> /System/Library/Frameworks/Symbols.framework/Versions/A/Symbols\\n1143 |        0x1f9b07000 -        0x1f9c4924a  com.apple.DataFrame (1.0 - 50) <1F0614F9-50AC-3B69-94DE-34DDD1BF8808> /System/Library/Frameworks/TabularData.framework/Versions/A/TabularData\\n1144 |        0x2372b3000 -        0x23732940f  com.apple.TipKit (8.0 - 92.5.1) <63DD6E9B-831A-3891-A6DB-63F77F862B2B> /System/Library/Frameworks/TipKit.framework/Versions/A/TipKit\\n1145 |        0x237413000 -        0x23747f6ff  com.apple.Translation (1.0 - 300.16) <F2B54D74-A2BB-3ABD-9EBF-7414611D4A67> /System/Library/Frameworks/Translation.framework/Versions/A/Translation\\n1146 |        0x237911000 -        0x237c41eff  libANGLE-shared.dylib (621.********) <11602983-89DB-3D21-8103-A0EC74798C48> /System/Library/Frameworks/WebKit.framework/Versions/A/Frameworks/WebCore.framework/Versions/A/Frameworks/libANGLE-shared.dylib\\n1147 |        0x238752000 -        0x23876dddf  com.apple.AAAFoundation (1.0 - 1) <D0C66BDE-2156-36C1-AB38-7A9079127D03> /System/Library/PrivateFrameworks/AAAFoundation.framework/Versions/A/AAAFoundation\\n1148 |        0x23876e000 -        0x2387d4a6a  com.apple.AAAFoundationSwift (1.0 - 1) <D2BB1871-EE72-3CF5-BDBE-63D956F3AE3B> /System/Library/PrivateFrameworks/AAAFoundationSwift.framework/Versions/A/AAAFoundationSwift\\n1149 |        0x23abdc000 -        0x23ac180f1  com.apple.AppIntentSchemas (1.0 - 3404.30.1) <9B8D2C1D-C0FA-35E6-AC59-5C95AA3F2946> /System/Library/PrivateFrameworks/AppIntentSchemas.framework/Versions/A/AppIntentSchemas\\n1150 |        0x23ac19000 -        0x23ae84d4b  com.apple.AppIntentsServices (1.0 - 34.5) <B22ED4FB-A756-3BFF-BC9F-B32EA52530F3> /System/Library/PrivateFrameworks/AppIntentsServices.framework/Versions/A/AppIntentsServices\\n1151 |        0x23b8d5000 -        0x23b939c7f  com.apple.AppleAccountUI (1.0 - 1) <07C2E9D3-4DF2-36E7-B813-D1A3BD767BFF> /System/Library/PrivateFrameworks/AppleAccountUI.framework/Versions/A/AppleAccountUI\\n1152 |        0x23b97e000 -        0x23ba6b07f  com.apple.AppleDepth (137.3 - 137.3) <40550D47-AF02-3187-9A79-D6534233873A> /System/Library/PrivateFrameworks/AppleDepth.framework/Versions/A/AppleDepth\\n1153 |        0x23ba6c000 -        0x23baeb19f  com.apple.AppleDepthCore (137.0 - 137.0) <3146E516-F677-3A9B-B0C9-56649F653272> /System/Library/PrivateFrameworks/AppleDepthCore.framework/Versions/A/AppleDepthCore\\n1154 |        0x23baec000 -        0x23bb008df  com.apple.AppleDeviceQuerySupport (1.0 - 340.120.3) <016C1548-7FBE-3952-8F68-FE137F4E0E4E> /System/Library/PrivateFrameworks/AppleDeviceQuerySupport.framework/Versions/A/AppleDeviceQuerySupport\\n1155 |        0x23bb4e000 -        0x23bb6955f  com.apple.siri.flatbuffer.AppleFlatBuffers (1) <BE0E90AD-828B-33E4-99CD-FB913532AAC8> /System/Library/PrivateFrameworks/AppleFlatBuffers.framework/Versions/A/AppleFlatBuffers\\n1156 |        0x23bf2d000 -        0x23c10035f  com.apple.cmphoto.AppleJPEGXL (1.0 - 1) <4CCED872-0155-3F41-85AB-F7DD31DADA68> /System/Library/PrivateFrameworks/AppleJPEGXL.framework/Versions/A/AppleJPEGXL\\n1157 |        0x23c101000 -        0x23c1571ef  com.apple.AppleKeyStore (1.0 - 1.0) <977B2025-00E4-3C34-85FD-7C31CC64A1B9> /System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore\\n1158 |        0x23c187000 -        0x23c95985f  com.apple.AppleMediaServicesKitInternal (1.0.17.1 - 1.0.17.1) <8D4D1D23-D631-3E98-96E5-803BE2EC0201> /System/Library/PrivateFrameworks/AppleMediaServicesKitInternal.framework/Versions/A/AppleMediaServicesKitInternal\\n1159 |        0x23cace000 -        0x23cae6c5f  com.apple.private.AppleMobileFileIntegrity-fmk (1.0 - 1) <303D10D7-C0AA-3770-B7CD-FCD0FDD3C755> /System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity\\n1160 |        0x23cd04000 -        0x23cd304f2  com.apple.AskToCore (1.0 - 1) <04F28C83-5F85-3848-AD31-27474B139689> /System/Library/PrivateFrameworks/AskToCore.framework/Versions/A/AskToCore\\n1161 |        0x23ce2a000 -        0x23ce3cf9f  com.apple.AtomicsInternal (1.1.0 - 103.2) <23810191-9A6A-35F4-8C18-97ECDB700C2B> /System/Library/PrivateFrameworks/AtomicsInternal.framework/Versions/A/AtomicsInternal\\n1162 |        0x23cebe000 -        0x23ceec57f  com.apple.imgaudio.AudioAnalytics (1.0 - 1) <9283BDE9-7E2A-31BB-9240-262B437F0F8C> /System/Library/PrivateFrameworks/AudioAnalytics.framework/Versions/A/AudioAnalytics\\n1163 |        0x23d17e000 -        0x23d25f27f  com.apple.AuthenticationServicesCore (1.0 - 20621.********) <1B0C02E2-7262-3C52-AEBA-FAC2C589B8BA> /System/Library/PrivateFrameworks/AuthenticationServicesCore.framework/Versions/A/AuthenticationServicesCore\\n1164 |        0x23d2ab000 -        0x23d2adf5f  com.apple.AvailabilityKit (1.0 - 1) <2E4BA635-2372-372A-A329-7AF67F24B3CF> /System/Library/PrivateFrameworks/AvailabilityKit.framework/Versions/A/AvailabilityKit\\n1165 |        0x23d2ae000 -        0x23d3645bf  com.apple.avatarkit (1.0 - 346.201) <4C1B9772-8F01-3246-A2F9-F9360AFBF386> /System/Library/PrivateFrameworks/AvatarKit.framework/Versions/A/AvatarKit\\n1166 |        0x23d365000 -        0x23d365367  com.apple.AvatarKitContent (1.0 - 346.201) <0E875488-5AC7-3BAB-B060-FF0987C7DE28> /System/Library/PrivateFrameworks/AvatarKitContent.framework/Versions/A/AvatarKitContent\\n1167 |        0x23d366000 -        0x23d3b5b9f  com.apple.AvatarPersistence (1.0 - 363.500.41) <33639534-A506-3791-B56E-14B14AEB0AE0> /System/Library/PrivateFrameworks/AvatarPersistence.framework/Versions/A/AvatarPersistence\\n1168 |        0x23d3ed000 -        0x23d409dff  com.apple.BackgroundSystemTasks (1.0 - 1) <A31FC6CF-01D2-3C22-8D53-933A885AF2B3> /System/Library/PrivateFrameworks/BackgroundSystemTasks.framework/Versions/A/BackgroundSystemTasks\\n1169 |        0x23d476000 -        0x23d49107f  com.apple.biome.BiomeDSL (1.0 - 166.23.1) <80129DE9-60CB-32E6-B90F-452FDA7012D4> /System/Library/PrivateFrameworks/BiomeDSL.framework/Versions/A/BiomeDSL\\n1170 |        0x23d492000 -        0x23d4b0c1f  com.apple.biome.BiomeFlexibleStorage (1.0 - 166.23.1) <1754BF34-44F8-32C0-94AF-93E9EE4F3A21> /System/Library/PrivateFrameworks/BiomeFlexibleStorage.framework/Versions/A/BiomeFlexibleStorage\\n1171 |        0x23d4b1000 -        0x23db4a39f  com.apple.BiomeLibrary (100.66) <772054EA-854D-355A-BCA8-BA77E3CC3DE0> /System/Library/PrivateFrameworks/BiomeLibrary.framework/Versions/A/BiomeLibrary\\n1172 |        0x23db4b000 -        0x23db50b1f  com.apple.biome.BiomeSync (1.0 - 166.23.1) <7E797D45-5A6A-36EE-871E-B0BEBC5110F8> /System/Library/PrivateFrameworks/BiomeSync.framework/Versions/A/BiomeSync\\n1173 |        0x23e2fd000 -        0x23e3346ff  com.apple.CBORLibrary (1.0 - 1) <3B0F0E87-2B49-39C5-964E-6244F64E9982> /System/Library/PrivateFrameworks/CBORLibrary.framework/Versions/A/CBORLibrary\\n1174 |        0x23e675000 -        0x23e676eb7  com.apple.CMCaptureDevice (587.*********) <96921588-B0BA-3B30-AB70-794F2F16AF5A> /System/Library/PrivateFrameworks/CMCaptureDevice.framework/Versions/A/CMCaptureDevice\\n1175 |        0x23e73f000 -        0x23e912e3f  com.apple.CMImaging (1.0 - 587.*********) <6C843D0C-C1E8-3B32-B6CB-476B85424D4E> /System/Library/PrivateFrameworks/CMImaging.framework/Versions/A/CMImaging\\n1176 |        0x23e913000 -        0x23ea1e2ff  com.apple.CMPhoto (1.0 - 1) <B05BD715-3311-3698-9241-9A1F886A3C15> /System/Library/PrivateFrameworks/CMPhoto.framework/Versions/A/CMPhoto\\n1177 |        0x23ea23000 -        0x23ea30cbf  com.apple.spotlight.CSExattrCrypto (1.0 - 2333.50.1) <2F11FBE6-5E6E-3533-9DD9-D908AB869E27> /System/Library/PrivateFrameworks/CSExattrCrypto.framework/Versions/A/CSExattrCrypto\\n1178 |        0x23eb87000 -        0x23ec3263f  com.apple.CalendarDaemon (1.0 - 1194.6.2) <FB7A88CA-F612-3484-8455-D6BD620EE84C> /System/Library/PrivateFrameworks/CalendarDaemon.framework/Versions/A/CalendarDaemon\\n1179 |        0x23ec33000 -        0x23ed6039f  com.apple.CalendarDatabase (1.0 - 1253.6.1) <BC1EA6CE-E1D5-3828-BD16-EE1C569784B0> /System/Library/PrivateFrameworks/CalendarDatabase.framework/Versions/A/CalendarDatabase\\n1180 |        0x23f029000 -        0x23f0a185f  com.apple.biome.CascadeSets (1.0 - 166.23.1) <985CAA9C-FED1-3A80-88EE-BD6BC7C549E1> /System/Library/PrivateFrameworks/CascadeSets.framework/Versions/A/CascadeSets\\n1181 |        0x23f0a2000 -        0x23f0c0c07  com.apple.cryptokit.Chirp (1.0 - 1) <EF11A9FB-39FB-32FF-841A-8BD56FA20278> /System/Library/PrivateFrameworks/Chirp.framework/Versions/A/Chirp\\n1182 |        0x23f464000 -        0x23f49d1df  com.apple.CinematicFraming (1.0 - 587.*********) <7672F6D0-EEAB-3E27-A10D-E277D02693E6> /System/Library/PrivateFrameworks/CinematicFraming.framework/Versions/A/CinematicFraming\\n1183 |        0x23f811000 -        0x23f88db50  com.apple.cloudkit.CloudAssets (2260.10.3) <F19DF147-61E3-381A-8430-F5D7C09BD1F7> /System/Library/PrivateFrameworks/CloudAssets.framework/Versions/A/CloudAssets\\n1184 |        0x23fb48000 -        0x23fb55bdf  com.apple.CloudSettings (1.0 - 1) <35F546A6-8A98-372C-972F-3EFA6427C80B> /System/Library/PrivateFrameworks/CloudSettings.framework/Versions/A/CloudSettings\\n1185 |        0x23fbd9000 -        0x23fd12cdf  com.apple.CloudSubscriptionFeatures (1.0 - 1.9) <6FD62BC3-4E2D-3010-AF9F-90A64969497B> /System/Library/PrivateFrameworks/CloudSubscriptionFeatures.framework/Versions/A/CloudSubscriptionFeatures\\n1186 |        0x23ffd7000 -        0x24054621f  com.apple.Coherence (1.0 - 1) <24FB5D3A-5E0C-3756-B773-E7F51FFFB211> /System/Library/PrivateFrameworks/Coherence.framework/Versions/A/Coherence\\n1187 |        0x240547000 -        0x24056ad5f  com.apple.CollectionViewCore (1.0 - 1) <3B25F615-5ECF-31DC-84E5-F930AE4BD913> /System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore\\n1188 |        0x24056b000 -        0x2406ae297  com.apple.CollectionsInternal (1.1.0 - 103.2) <40F76089-2C2F-3283-93F4-E2E2AA2A1AB2> /System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal\\n1189 |        0x24082b000 -        0x240864fca  com.apple.contacts.ContactsAccounts (1.0 - 1) <A6521F5F-88FA-38B1-B360-0A574F86580A> /System/Library/PrivateFrameworks/ContactsAccounts.framework/Versions/A/ContactsAccounts\\n1190 |        0x240865000 -        0x240868d7f  com.apple.contacts.ContactsMetrics (1 - 17.600.1) <E3F0F871-D33E-388B-BF28-7602F1F0872B> /System/Library/PrivateFrameworks/ContactsMetrics.framework/Versions/A/ContactsMetrics\\n1191 |        0x240992000 -        0x240996e5f  com.apple.contextkit.ContextKitCore (1.0 - 1) <EFD80936-EABB-33C7-B425-3F9FC0246A41> /System/Library/PrivateFrameworks/ContextKitCore.framework/Versions/A/ContextKitCore\\n1192 |        0x241028000 -        0x2410a2d9f  com.apple.audio.coreaudio.Stravinsky (1.0 - 1) <E0AC42BB-1D6F-3AFF-A229-255CAB306182> /System/Library/PrivateFrameworks/CoreAudioOrchestration.framework/Versions/A/CoreAudioOrchestration\\n1193 |        0x243d34000 -        0x243e3483f  com.apple.CoreSceneUnderstanding (1.69.1 - 1.69.1) <98F47C60-ECCA-3C22-BF95-F44DFF482158> /System/Library/PrivateFrameworks/CoreSceneUnderstanding.framework/Versions/A/CoreSceneUnderstanding\\n1194 |        0x243fcc000 -        0x243ff1acf  com.apple.CoreUtilsExtras (7.8 - 780.15) <DEB57A9A-AC07-355D-B62B-7EBDFAE62F94> /System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras\\n1195 |        0x2440c8000 -        0x244177c7f  com.apple.security.CryptoKit-Private (1.0 - 1) <CA535503-103C-3ECB-9FA1-15FC927CDC08> /System/Library/PrivateFrameworks/CryptoKitPrivate.framework/Versions/A/CryptoKitPrivate\\n1196 |        0x24418a000 -        0x244193aff  com.apple.devicemanagementclient.DEPClientLibrary (1.0 - 1) <A9F7823F-7D25-304B-ACEE-81C3AE59C74F> /System/Library/PrivateFrameworks/DEPClientLibrary.framework/Versions/A/DEPClientLibrary\\n1197 |        0x2441aa000 -        0x2441db97f  com.apple.devicemanagementclient.DMCEnrollmentLibrary (1.0 - 1) <46332151-CB5E-3BEC-B496-A4C3B439BC75> /System/Library/PrivateFrameworks/DMCEnrollmentLibrary.framework/Versions/A/DMCEnrollmentLibrary\\n1198 |        0x2441dc000 -        0x244223b7f  com.apple.devicemanagementclient.DMCUtilities (1.0 - 1) <995890F6-9B13-3E9C-BC1F-F8A7EC4E00D6> /System/Library/PrivateFrameworks/DMCUtilities.framework/Versions/A/DMCUtilities\\n1199 |        0x2442df000 -        0x2442e6ebf  com.apple.darwinup-framework (1.0 - 280.120.2) <0243C348-D922-32BC-9F3D-0D2BB04A903E> /System/Library/PrivateFrameworks/Darwinup.framework/Versions/A/Darwinup\\n1200 |        0x244411000 -        0x244453fdf  com.apple.dataaccess.dataaccessexpress.framework (1.0 - 1.0) <516EECCC-BDAF-3869-AD69-C5418CB4C51B> /System/Library/PrivateFrameworks/DataAccessExpress.framework/Versions/A/DataAccessExpress\\n1201 |        0x2445d0000 -        0x24461dc54  com.apple.aiml.dendrite.Dendrite (1.0 - 1) <7098E4E6-96CD-3407-936A-126F7EF2910B> /System/Library/PrivateFrameworks/Dendrite.framework/Versions/A/Dendrite\\n1202 |        0x244979000 -        0x244b5271f  com.apple.DiskImages2 (385.120.4 - 385.120.4) <0E040347-0F81-3295-91FB-0670D646B87A> /System/Library/PrivateFrameworks/DiskImages2.framework/Versions/A/DiskImages2\\n1203 |        0x244b5e000 -        0x244b7427f  com.apple.DistributedSensing (1.0 - 1) <ECF222BC-50CD-3C2B-9DFD-B06CB8D67E2E> /System/Library/PrivateFrameworks/DistributedSensing.framework/Versions/A/DistributedSensing\\n1204 |        0x244c53000 -        0x244cc1e5f  com.apple.DoNotDisturb (1.0 - 433.6.1) <39C3CECC-2F67-3F5E-807F-0076F4367FDC> /System/Library/PrivateFrameworks/DoNotDisturb.framework/Versions/A/DoNotDisturb\\n1205 |        0x245f72000 -        0x24601901f  com.apple.FeedbackService (1.0 - 1) <DBF2A70E-97C7-3EAF-83F3-AE2F694BBA7E> /System/Library/PrivateFrameworks/FeedbackService.framework/Versions/A/FeedbackService\\n1206 |        0x246966000 -        0x2469666e3  com.apple.FontServices (1.0 - 1) <149691CA-DEA6-388A-952F-B87C92D4D7BB> /System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices\\n1207 |        0x246967000 -        0x246a2f8cf  libXTFontStaticRegistryData.dylib (309.5.0.3) <7737B747-9C4F-36AB-A524-B67BA292AB12> /System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib\\n1208 |        0x246a31000 -        0x246a3cfdf  com.apple.FramePacing (1.0 - 1) <D124208B-8F2F-3052-8F3C-A98011F9A265> /System/Library/PrivateFrameworks/FramePacing.framework/Versions/A/FramePacing\\n1209 |        0x246afb000 -        0x246b42f7f  com.apple.FusionTracker (1.0 - 1) <ABEFF0C5-B788-3D4C-ACF6-FEA60E531888> /System/Library/PrivateFrameworks/FusionTracker.framework/Versions/A/FusionTracker\\n1210 |        0x2475a6000 -        0x2475ab747  libGPUCompilerUtils.dylib (32023.621) <D8960CD8-E02A-3095-A115-E2AC1AD47F14> /System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib\\n1211 |        0x24b634000 -        0x24b67401f  libllvm-flatbuffers.dylib (32023.621) <2EB6C7E0-7587-3DFD-94DC-72044592C0E5> /System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib\\n1212 |        0x24b6d6000 -        0x24b8719d2  com.apple.GRDB.GRDBInternal (1.0 - 12) <1CD9F644-C390-34ED-83E5-95DA0029E523> /System/Library/PrivateFrameworks/GRDBInternal.framework/Versions/A/GRDBInternal\\n1213 |        0x24c1ff000 -        0x24c29eb5f  com.apple.GameControllerFoundation (12.5.3) <3E9006F3-BE19-3EF5-B335-3CCB962BF68F> /System/Library/PrivateFrameworks/GameControllerFoundation.framework/Versions/A/GameControllerFoundation\\n1214 |        0x24c29f000 -        0x24c2b119f  com.apple.GameControllerSettings (12.5.3) <9A35300C-8347-3C1A-99ED-19719310CBF4> /System/Library/PrivateFrameworks/GameControllerSettings.framework/Versions/A/GameControllerSettings\\n1215 |        0x24c949000 -        0x24c9996c7  com.apple.GenerativeFunctions.GenerativeFunctions (1.0 - 154.762) <1A20E656-9CD4-3AEF-A3EE-E7EE2B715FF7> /System/Library/PrivateFrameworks/GenerativeFunctions.framework/Versions/A/GenerativeFunctions\\n1216 |        0x24c99a000 -        0x24ca48997  com.apple.GenerativeFunctions.GenerativeFunctionsFoundation (1.0 - 154.762) <86B647C0-C8B1-3CF3-9107-CE9617481A99> /System/Library/PrivateFrameworks/GenerativeFunctionsFoundation.framework/Versions/A/GenerativeFunctionsFoundation\\n1217 |        0x24ca49000 -        0x24cab1fbf  com.apple.GenerativeFunctions.GenerativeFunctionsInstrumentation (1.0 - 154.762) <3C17389A-78C7-379D-969D-EB35893142E3> /System/Library/PrivateFrameworks/GenerativeFunctionsInstrumentation.framework/Versions/A/GenerativeFunctionsInstrumentation\\n1218 |        0x24cab2000 -        0x24cb74b21  com.apple.GenerativeFunctions.GenerativeModels (1.0 - 154.762) <C9751A96-7D07-387F-A6FC-FEDD78764397> /System/Library/PrivateFrameworks/GenerativeModels.framework/Versions/A/GenerativeModels\\n1219 |        0x24cb75000 -        0x24cbe8368  com.apple.GenerativeFunctions.GenerativeModelsFoundation (1.0 - 154.762) <52C23EC1-9F26-302C-971C-7D0F1E98F486> /System/Library/PrivateFrameworks/GenerativeModelsFoundation.framework/Versions/A/GenerativeModelsFoundation\\n1220 |        0x24cbe9000 -        0x24ccd98df  com.apple.GeoAnalytics (1.0 - 1986.25.3.6.3) <DA3F4110-C0FA-3CD9-A8B7-E37D6A961EA0> /System/Library/PrivateFrameworks/GeoAnalytics.framework/Versions/A/GeoAnalytics\\n1221 |        0x24ccda000 -        0x24ccded1f  com.apple.GeoServices (1.0 - 1986.25.3.6.3) <55583F32-37A1-3278-98F2-3EDEE03F8CC9> /System/Library/PrivateFrameworks/GeoServicesCore.framework/Versions/A/GeoServicesCore\\n1222 |        0x24f7bd000 -        0x24f80aeff  com.apple.IASUtilitiesCore (1.0 - 1) <BE6746CF-D1FE-3F7B-A309-94D04B78B903> /System/Library/PrivateFrameworks/IASUtilitiesCore.framework/Versions/A/IASUtilitiesCore\\n1223 |        0x24f88d000 -        0x24f8e6dbf  com.apple.IO80211 (1.0 - 1) <E03E169E-6AAE-3DC1-9F6D-47EFB59811A8> /System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211\\n1224 |        0x24f8ea000 -        0x24f8f3f5f  com.apple.IPConfiguration (1.21 - 1.21) <4A7A6404-5571-3B61-881C-FC60B2150E2F> /System/Library/PrivateFrameworks/IPConfiguration.framework/Versions/A/IPConfiguration\\n1225 |        0x24ffd6000 -        0x250013bdf  com.apple.InputAnalytics (1.0 - 64.504) <90578926-1013-3818-8408-ABA99E590D00> /System/Library/PrivateFrameworks/InputAnalytics.framework/Versions/A/InputAnalytics\\n1226 |        0x250068000 -        0x25007cd7f  com.apple.InputToolKit (1.0 - 1) <308C6F04-AB0C-3EBD-8EB4-E95DF75DD06B> /System/Library/PrivateFrameworks/InputToolKit.framework/Versions/A/InputToolKit\\n1227 |        0x25007d000 -        0x2500ac0bf  com.apple.InputToolKitUI.InputToolKitUI (1.0 - 1) <C755C0E3-83FC-390B-9053-BE20872E1166> /System/Library/PrivateFrameworks/InputToolKitUI.framework/Versions/A/InputToolKitUI\\n1228 |        0x25016f000 -        0x250253c3f  com.apple.InstalledContentLibrary (1.0 - 1.0) <463BCC71-B63D-3AF0-92B8-93D093886414> /System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary\\n1229 |        0x252c9e000 -        0x2530dc8ff  com.apple.IntelligencePlatformLibrary (100.66) <B5E64C9C-12AA-39B3-A14C-312D8CA0F899> /System/Library/PrivateFrameworks/IntelligencePlatformLibrary.framework/Versions/A/IntelligencePlatformLibrary\\n1230 |        0x2531bc000 -        0x2531f233f  com.apple.audio.CoreAudio.IsolatedCoreAudioClient (1.0 - 1) <DF48F729-2897-3319-B6CC-50CB71CF16D6> /System/Library/PrivateFrameworks/IsolatedCoreAudioClient.framework/Versions/A/IsolatedCoreAudioClient\\n1231 |        0x2533be000 -        0x2533da1df  com.apple.JetPack-Mac (1.0 - 1) <A9A95278-1668-34CC-B699-941A60F2B017> /System/Library/PrivateFrameworks/JetPack.framework/Versions/A/JetPack\\n1232 |        0x253662000 -        0x2537c845f  com.apple.LiftUI (1.2 - 1) <0F4DCF88-9A4E-371D-894A-B648E718764E> /System/Library/PrivateFrameworks/LiftUI.framework/Versions/A/LiftUI\\n1233 |        0x254293000 -        0x2544a349f  com.apple.LinkMetadata (1.0 - 227.19) <088BF3EA-B1E4-3EBB-8A84-BF7F49F9D049> /System/Library/PrivateFrameworks/LinkMetadata.framework/Versions/A/LinkMetadata\\n1234 |        0x2544a4000 -        0x2544d33df  com.apple.LinkPresentation.StyleSheetParsing (272 - 272.13) <DDB48295-F0C2-322C-8339-7A7926ECA60C> /System/Library/PrivateFrameworks/LinkPresentationStyleSheetParsing.framework/Versions/A/LinkPresentationStyleSheetParsing\\n1235 |        0x2544d4000 -        0x254606d3f  com.apple.LinkServices (1.0 - 227.19) <D3A28847-C784-33EF-972F-1A3E3C90163B> /System/Library/PrivateFrameworks/LinkServices.framework/Versions/A/LinkServices\\n1236 |        0x254624000 -        0x254654adf  com.apple.filesystems.LiveFS (1.0 - 1) <576B3CAE-3DF0-3253-9F4F-138FE965FCC7> /System/Library/PrivateFrameworks/LiveFS.framework/Versions/A/LiveFS\\n1237 |        0x254679000 -        0x25479207f  com.apple.LocalAuthenticationCore (1656.120.6) <CB29C36F-7AD2-3F70-9BD7-A385B89FE044> /System/Library/PrivateFrameworks/LocalAuthenticationCore.framework/Versions/A/LocalAuthenticationCore\\n1238 |        0x254793000 -        0x2547db15f  com.apple.LocalAuthentication.LocalAuthenticationCoreUI (1656.120.6) <C9C63720-042E-37E9-ACA8-82C6777D52C8> /System/Library/PrivateFrameworks/LocalAuthenticationCoreUI.framework/Versions/A/LocalAuthenticationCoreUI\\n1239 |        0x2547f9000 -        0x25481dd9b  com.apple.internal.LocalStatusKit (1.0 - 1) <7311D0BE-67BE-319C-82A7-7FAA5B910FAB> /System/Library/PrivateFrameworks/LocalStatusKit.framework/Versions/A/LocalStatusKit\\n1240 |        0x254822000 -        0x25482841f  com.apple.LockdownMode (1.0 - 1) <8952670D-0269-386D-A635-3903E5BB7063> /System/Library/PrivateFrameworks/LockdownMode.framework/Versions/A/LockdownMode\\n1241 |        0x2548aa000 -        0x2548d34df  com.apple.devicemanagementclient.MDMClientLibrary (1.0 - 1) <3C95E33F-6EA9-3D8B-9892-06F1F2DAE89A> /System/Library/PrivateFrameworks/MDMClientLibrary.framework/Versions/A/MDMClientLibrary\\n1242 |        0x25492d000 -        0x254fbe78f  com.apple.MIL (3405.2 - 3405.2.1) <5892187B-F794-3FD2-A4F7-E48419008A6B> /System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL\\n1243 |        0x254fbf000 -        0x25510f2ff  com.apple.CoreML.MLAssetIO (1.0 - 3405.2.1) <4C8AB160-03F1-3370-886C-DA7006DBB1AC> /System/Library/PrivateFrameworks/MLAssetIO.framework/Versions/A/MLAssetIO\\n1244 |        0x255110000 -        0x25516b1b3  com.apple.mlcompiler.runtime (3404.3.1 - 3404.3.1) <7FC62FD2-71C2-350E-8893-EB884B308FBA> /System/Library/PrivateFrameworks/MLCompilerRuntime.framework/Versions/A/MLCompilerRuntime\\n1245 |        0x25516c000 -        0x2551857bf  com.apple.mlcompiler.services (3404.3.1 - 3404.3.1) <F456BD1D-784E-3416-B5E2-0C205E8ED36F> /System/Library/PrivateFrameworks/MLCompilerServices.framework/Versions/A/MLCompilerServices\\n1246 |        0x256617000 -        0x25661c21f  com.apple.ManagedOrganizationContacts (1.2 - 150.4.10) <D05BEB67-40E3-3C95-9E67-4B10C06581B5> /System/Library/PrivateFrameworks/ManagedOrganizationContacts.framework/Versions/A/ManagedOrganizationContacts\\n1247 |        0x25661d000 -        0x25665529f  com.apple.ManagedSettingsObjC (242.4.10 - 242.4.10) <B703C7CD-83F6-3D2D-8530-1CF98A66658F> /System/Library/PrivateFrameworks/ManagedSettingsObjC.framework/Versions/A/ManagedSettingsObjC\\n1248 |        0x256656000 -        0x256661544  com.apple.ManagedSettingsSupport (242.4.10 - 242.4.10) <CF7401F4-4D70-319A-8C2C-8EAD92D7CAEE> /System/Library/PrivateFrameworks/ManagedSettingsSupport.framework/Versions/A/ManagedSettingsSupport\\n1249 |        0x256e80000 -        0x256ed1edf  com.apple.MediaAnalysisServices (1.0 - 1) <AD563A8F-C0EA-3FDE-BC54-3F4117B2AD77> /System/Library/PrivateFrameworks/MediaAnalysisServices.framework/Versions/A/MediaAnalysisServices\\n1250 |        0x25753d000 -        0x257592c3f  com.apple.MessageSecurity (1.0 - 115.100.9) <732DF65A-FD1E-3336-AA9D-AB94921A13C1> /System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity\\n1251 |        0x257f10000 -        0x258152bfc  com.apple.ModelCatalog.ModelCatalog (1.0 - 162.762) <05A7057F-FFFD-3D8B-9758-AF90D8F09278> /System/Library/PrivateFrameworks/ModelCatalog.framework/Versions/A/ModelCatalog\\n1252 |        0x2581ce000 -        0x25832f3d0  com.apple.ModelManagerServices (1.0 - 1) <C5571CE1-5F8F-3FE5-9F03-EF880251454B> /System/Library/PrivateFrameworks/ModelManagerServices.framework/Versions/A/ModelManagerServices\\n1253 |        0x25995f000 -        0x2599e1de7  com.apple.calls.NeighborhoodActivityConduit (1.0 - 1) <D7864015-6900-338D-9597-F42FC5281197> /System/Library/PrivateFrameworks/NeighborhoodActivityConduit.framework/Versions/A/NeighborhoodActivityConduit\\n1254 |        0x259ea5000 -        0x259eacc3f  com.apple.NewsURLBucket (1.0 - 1) <1DF98EC4-9720-3F1F-AFAD-42E22E386234> /System/Library/PrivateFrameworks/NewsURLBucket.framework/Versions/A/NewsURLBucket\\n1255 |        0x25b7c3000 -        0x25b8e27af  com.apple.ParsingInternal (0.0.1 - 103.2) <291AFD9D-90D8-311B-B73D-31E4FFF53054> /System/Library/PrivateFrameworks/ParsingInternal.framework/Versions/A/ParsingInternal\\n1256 |        0x25c046000 -        0x25c054d9f  com.apple.PassKitMacHelperTemp (1.0 - 1591.6.5.1) <32537ADA-D146-3834-99EE-2B5A23C5BC57> /System/Library/PrivateFrameworks/PassKitMacHelperTemp.framework/Versions/A/PassKitMacHelperTemp\\n1257 |        0x25c6d4000 -        0x25d3a0fbf  com.apple.PegasusAPI (1.0 - 3405.13.1) <AF687EDA-97E5-3A0C-8896-A59AF30DCE8C> /System/Library/PrivateFrameworks/PegasusAPI.framework/Versions/A/PegasusAPI\\n1258 |        0x25d3a1000 -        0x25d3ffb02  com.apple.PegasusConfiguration (1.0 - 15000) <44734D0D-EF08-3846-A159-1C7CA08A20FA> /System/Library/PrivateFrameworks/PegasusConfiguration.framework/Versions/A/PegasusConfiguration\\n1259 |        0x25d938000 -        0x25da667ff  com.apple.PhotoLibraryServicesCore (1.0 - 760.6.150) <896C735D-1058-36F1-81DA-D862C59D32D7> /System/Library/PrivateFrameworks/PhotoLibraryServicesCore.framework/Versions/A/PhotoLibraryServicesCore\\n1260 |        0x25e411000 -        0x25e43db5f  com.apple.PhotosIntelligenceCore (1.0 - 760.6.150) <DF80AD11-8AA2-3667-BE83-CB7F313E4063> /System/Library/PrivateFrameworks/PhotosIntelligenceCore.framework/Versions/A/PhotosIntelligenceCore\\n1261 |        0x25ea24000 -        0x25ea4367f  com.apple.accessibility.PhotosensitivityProcessing (1.0 - 1) <7BF3ACA3-7983-36AA-A2DE-02D7B5455FE6> /System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing\\n1262 |        0x25ea89000 -        0x25eb542bf  com.apple.PlatformSSO (1.0 - 1) <70DF13DC-316B-38AA-A43C-B29FF585F9D5> /System/Library/PrivateFrameworks/PlatformSSO.framework/Versions/A/PlatformSSO\\n1263 |        0x25eb55000 -        0x25ec4cf1f  com.apple.PlatformSSOCore (1.0 - 1) <4337CD45-0279-350F-B6F0-12420A252C0C> /System/Library/PrivateFrameworks/PlatformSSOCore.framework/Versions/A/PlatformSSOCore\\n1264 |        0x25edaf000 -        0x25eddf507  com.apple.PoirotSQLite (1.0 - 1) <84AADC12-D018-3CDE-949A-2CB0DB591BE9> /System/Library/PrivateFrameworks/PoirotSQLite.framework/Versions/A/PoirotSQLite\\n1265 |        0x25ede0000 -        0x25ee4cc64  com.apple.PoirotSchematizer (1.0 - 1) <C809B8C4-724E-335B-AC0F-25C009BEBF6A> /System/Library/PrivateFrameworks/PoirotSchematizer.framework/Versions/A/PoirotSchematizer\\n1266 |        0x25ee4d000 -        0x25ee78120  com.apple.PoirotUDFs (1.0 - 1) <EBEBC624-2D74-3D2C-9BB2-5C32A506FEAD> /System/Library/PrivateFrameworks/PoirotUDFs.framework/Versions/A/PoirotUDFs\\n1267 |        0x25ee79000 -        0x25ef7d45f  com.apple.portrait.Portrait (1.0 - 18) <A707121D-E810-3AC4-9D83-2FC9ED5EFD58> /System/Library/PrivateFrameworks/Portrait.framework/Versions/A/Portrait\\n1268 |        0x25f8b5000 -        0x25f93843f  com.apple.ProDisplayLibrary (9.4.6 - 9.4.6) <42BF1AA8-7A85-37BB-872E-0454A45BF76A> /System/Library/PrivateFrameworks/ProDisplayLibrary.framework/Versions/A/ProDisplayLibrary\\n1269 |        0x25f99a000 -        0x25f9c72b9  com.apple.intelligenceflow.ProactiveDaemonSupport (1.0 - 218.5) <AF6BB1A2-8535-38F5-BFED-80414413F939> /System/Library/PrivateFrameworks/ProactiveDaemonSupport.framework/Versions/A/ProactiveDaemonSupport\\n1270 |        0x25fe6e000 -        0x25feb6334  com.apple.GenerativeFunctions.PromptKit (1.0 - 154.762) <B6866EE6-92FD-361B-9B04-247138156F80> /System/Library/PrivateFrameworks/PromptKit.framework/Versions/A/PromptKit\\n1271 |        0x2604df000 -        0x2604fa9bf  com.apple.RecapPerformanceTesting (50 - 50.0) <06090B7F-42CD-38A6-9D26-FF63F3989361> /System/Library/PrivateFrameworks/RecapPerformanceTesting.framework/Versions/A/RecapPerformanceTesting\\n1272 |        0x260564000 -        0x26056cefb  com.apple.ReflectionInternal (1.0.0 - 103.2) <C973A24A-3946-3737-B029-2BEE3FDE770E> /System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal\\n1273 |        0x26056d000 -        0x2605867bf  com.apple.RegulatoryDomainFramework (1.0 - 1) <53E76AC0-4974-3326-B1D5-95DE021E175D> /System/Library/PrivateFrameworks/RegulatoryDomain.framework/Versions/A/RegulatoryDomain\\n1274 |        0x260832000 -        0x2608b273f  com.apple.RemoteManagementModel (1.0 - 2.0) <8157E44C-B261-30E7-B640-07FFAB8D9D46> /System/Library/PrivateFrameworks/RemoteManagementModel.framework/Versions/A/RemoteManagementModel\\n1275 |        0x2608b3000 -        0x2608be2bf  com.apple.RemoteManagementProtocol (1.0 - 2.0) <006BB058-876A-3D7D-AFCA-165A73AC1DB3> /System/Library/PrivateFrameworks/RemoteManagementProtocol.framework/Versions/A/RemoteManagementProtocol\\n1276 |        0x2608bf000 -        0x260918a7f  com.apple.RemoteManagementStore (1.0 - 2.0) <92F595D4-0D44-31C2-B154-CAF8629129AA> /System/Library/PrivateFrameworks/RemoteManagementStore.framework/Versions/A/RemoteManagementStore\\n1277 |        0x260928000 -        0x260b147ff  com.apple.RemoteUI (1.0 - 1) <D0256A6F-EC67-3D63-9CA5-A4CA16244F7E> /System/Library/PrivateFrameworks/RemoteUI.framework/Versions/A/RemoteUI\\n1278 |        0x261236000 -        0x26124af0f  com.apple.RuntimeInternal (1.0.0 - 103.2) <1EC19E0A-096D-3DD7-BCC2-FB83C8294B67> /System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal\\n1279 |        0x26126e000 -        0x26132c45f  com.apple.seservice (1.0 - 1) <86420B72-17EB-32EE-9818-65EA8C348DC3> /System/Library/PrivateFrameworks/SEService.framework/Versions/A/SEService\\n1280 |        0x26132d000 -        0x2613b215f  com.apple.SFSymbolsFramework (1 - *********) <89D8B8E9-3B75-35FF-A375-30E0200C6B45> /System/Library/PrivateFrameworks/SFSymbols.framework/Versions/A/SFSymbols\\n1281 |        0x26140f000 -        0x26142853f  com.apple.STSXPCHelperClient (1.0 - 1) <EDFAF8F4-014F-3669-920D-706E1AF43A24> /System/Library/PrivateFrameworks/STSXPCHelperClient.framework/Versions/A/STSXPCHelperClient\\n1282 |        0x261f49000 -        0x261fc509f  com.apple.SensitiveContentAnalysisML (1.0 - 1) <181929CA-6E50-3429-A17A-4422544FDAE4> /System/Library/PrivateFrameworks/SensitiveContentAnalysisML.framework/Versions/A/SensitiveContentAnalysisML\\n1283 |        0x2620d4000 -        0x262165d9f  com.apple.SentencePieceInternal (52.206) <572CA5E2-40BC-346C-AF76-734CC674981A> /System/Library/PrivateFrameworks/SentencePieceInternal.framework/Versions/A/SentencePieceInternal\\n1284 |        0x262191000 -        0x2622413ff  com.apple.Settings (207.3.6 - 207.3.6) <91EAEF9A-8D20-3160-A2BE-2BB162707DCC> /System/Library/PrivateFrameworks/Settings.framework/Versions/A/Settings\\n1285 |        0x262649000 -        0x26274ed5f  com.apple.siri.SiriAnalytics (1.0 - 1) <FFCFBB02-E52D-3CF8-90F7-D613C4D7A4CB> /System/Library/PrivateFrameworks/SiriAnalytics.framework/Versions/A/SiriAnalytics\\n1286 |        0x263001000 -        0x26305493f  com.apple.crossdevicearbitration (1.0 - 1) <0FBC1867-1E0A-3789-8C05-ED6AF12D6D47> /System/Library/PrivateFrameworks/SiriCrossDeviceArbitration.framework/Versions/A/SiriCrossDeviceArbitration\\n1287 |        0x263055000 -        0x2630c6d7f  com.apple.crossdevicearbitration.feedback (1.0 - 1) <61E433C3-24B0-34D0-92C1-FD7AEECB9B95> /System/Library/PrivateFrameworks/SiriCrossDeviceArbitrationFeedback.framework/Versions/A/SiriCrossDeviceArbitrationFeedback\\n1288 |        0x266597000 -        0x26659fd1f  com.apple.siri.SiriPowerInstrumentation (1 - 1.0) <40B838B2-0177-362C-9968-3F2AFB7D7BC4> /System/Library/PrivateFrameworks/SiriPowerInstrumentation.framework/Versions/A/SiriPowerInstrumentation\\n1289 |        0x267854000 -        0x2681d3f5f  com.apple.siri.tts.SiriTTS (1 - 1) <B54F1E66-B601-3E90-94D4-660F3F9F8F0A> /System/Library/PrivateFrameworks/SiriTTS.framework/Versions/A/SiriTTS\\n1290 |        0x2681d4000 -        0x26837553f  com.apple.siri.SiriTTSService (1.0 - 1) <3F9483D6-90ED-3634-BBD5-2DE60C0E93DE> /System/Library/PrivateFrameworks/SiriTTSService.framework/Versions/A/SiriTTSService\\n1291 |        0x269f97000 -        0x269fc009f  com.apple.StatusKit (1.0 - 1) <845251DE-636E-3B5C-9B21-E5EE32BF6C60> /System/Library/PrivateFrameworks/StatusKit.framework/Versions/A/StatusKit\\n1292 |        0x26a0d0000 -        0x26a153d3b  com.apple.stickers (1.0 - 1) <FC3CF1B5-F0D1-3CC0-8D3F-B1231E45DD6D> /System/Library/PrivateFrameworks/Stickers.framework/Versions/A/Stickers\\n1293 |        0x26a154000 -        0x26a4a62ff  com.apple.StocksCore (7.3 - 1571) <2CE21760-31E1-3903-BCD5-413BD8B76BC0> /System/Library/PrivateFrameworks/StocksCore.framework/Versions/A/StocksCore\\n1294 |        0x26a4a7000 -        0x26a526044  com.apple.StocksKit (1.0 - 1571) <384AD9E0-DD7A-3E85-B1C2-2CA7DB6649A4> /System/Library/PrivateFrameworks/StocksKit.framework/Versions/A/StocksKit\\n1295 |        0x26a774000 -        0x26a7a000f  com.apple.security.SwiftASN1Internal (1.0 - 1) <BA60AC1B-739A-3720-B2B0-825C611B23C4> /System/Library/PrivateFrameworks/SwiftASN1Internal.framework/Versions/A/SwiftASN1Internal\\n1296 |        0x26a7f1000 -        0x26a7fe43f  com.apple.SymptomShared (2022.120.2) <327060FA-5FF3-32C1-85B1-80E4B7E7AA6B> /System/Library/PrivateFrameworks/SymptomShared.framework/Versions/A/SymptomShared\\n1297 |        0x26a801000 -        0x26a8144df  com.apple.SymptomAnalytics (1.0 - 2022.120.2) <8EE2141C-D70A-3BB5-AF49-84E1CEBA78EB> /System/Library/PrivateFrameworks/Symptoms.framework/Versions/A/Frameworks/SymptomAnalytics.framework/Versions/A/SymptomAnalytics\\n1298 |        0x26aa76000 -        0x26aa9a65f  com.apple.SymptomPresentationFeed (1.0 - 2022.120.2) <5DE9D600-1456-3FB3-9A50-4F9AA5FC274A> /System/Library/PrivateFrameworks/Symptoms.framework/Versions/A/Frameworks/SymptomPresentationFeed.framework/Versions/A/SymptomPresentationFeed\\n1299 |        0x26aa9e000 -        0x26aaf98ff  com.apple.Synapse (1.0 - 1) <A4038680-F824-3ADD-BD4C-209BFC88052A> /System/Library/PrivateFrameworks/Synapse.framework/Versions/A/Synapse\\n1300 |        0x26aca3000 -        0x26acfd6df  com.apple.SystemStatus (1.0 - 1) <A7F01EFE-38AC-3033-8A54-D9E2B8D58D25> /System/Library/PrivateFrameworks/SystemStatus.framework/Versions/A/SystemStatus\\n1301 |        0x26b14c000 -        0x26b14db1f  com.apple.tailspin.TailspinSymbolication (1.0 - 218.2) <94DCE11A-742F-3EC9-B43D-BD1D83E3DD84> /System/Library/PrivateFrameworks/TailspinSymbolication.framework/Versions/A/TailspinSymbolication\\n1302 |        0x26b14e000 -        0x26b1b0960  com.apple.TeaDB (3.0 - 1220) <E3887DB6-40D8-305C-BC4B-46E51C0B8BCA> /System/Library/PrivateFrameworks/TeaDB.framework/Versions/A/TeaDB\\n1303 |        0x26b1b1000 -        0x26b39931f  com.apple.TeaFoundation (3.0 - 1220) <4F6A014A-DAB5-31BF-BBED-29A5D1C020F6> /System/Library/PrivateFrameworks/TeaFoundation.framework/Versions/A/TeaFoundation\\n1304 |        0x26b39a000 -        0x26b3ba77b  com.apple.TeaSettings (3.0 - 1220) <F2426D35-3AB1-3C2E-A343-56711D251934> /System/Library/PrivateFrameworks/TeaSettings.framework/Versions/A/TeaSettings\\n1305 |        0x26d22f000 -        0x26d26309f  com.apple.tightbeam (1.0 - 386.120.4) <F7492DE2-B12E-39A0-B824-0DF2D79CF9A1> /System/Library/PrivateFrameworks/Tightbeam.framework/Versions/A/Tightbeam\\n1306 |        0x26d3ac000 -        0x26d48a626  com.apple.TipKitCore (8.0 - 92.5.1) <B4022FDD-C91B-3A35-8BC6-D6BD4AC78046> /System/Library/PrivateFrameworks/TipKitCore.framework/Versions/A/TipKitCore\\n1307 |        0x26d6d7000 -        0x26d88defb  com.apple.TokenGeneration (1.0 - 1) <475904A3-548E-3A7D-9848-27DB594C3570> /System/Library/PrivateFrameworks/TokenGeneration.framework/Versions/A/TokenGeneration\\n1308 |        0x26d88e000 -        0x26d9a0edf  com.apple.TokenGenerationCore (1.0 - 1) <89EFD02E-9908-3419-ABD5-44AFE831020F> /System/Library/PrivateFrameworks/TokenGenerationCore.framework/Versions/A/TokenGenerationCore\\n1309 |        0x26e363000 -        0x26e36b51f  com.apple.TranslationUIServices (1.0 - 300.16) <1BE4645A-16A5-3BEE-BB80-DD68112D3017> /System/Library/PrivateFrameworks/TranslationUIServices.framework/Versions/A/TranslationUIServices\\n1310 |        0x26efb7000 -        0x26f10591f  com.apple.UIIntelligenceSupport (1.0 - 1) <7600A19A-6A27-3720-A7D7-71279170D8CC> /System/Library/PrivateFrameworks/UIIntelligenceSupport.framework/Versions/A/UIIntelligenceSupport\\n1311 |        0x26f281000 -        0x26f281539  com.apple.USDLib_FormatLoaderProxy (1.0 - 22.6.2) <D299D32C-95A5-34F7-88AD-C8DB31E3F82F> /System/Library/PrivateFrameworks/USDLib_FormatLoaderProxy.framework/Versions/A/USDLib_FormatLoaderProxy\\n1312 |        0x26f30d000 -        0x26f3b227f  com.apple.UnifiedAssetFramework (1.0 - 1) <DD671C66-62FB-3B72-8798-0E7DCD107502> /System/Library/PrivateFrameworks/UnifiedAssetFramework.framework/Versions/A/UnifiedAssetFramework\\n1313 |        0x26fa96000 -        0x26fa9d31f  com.apple.UserSafety (1.0 - 1) <88F2C38E-13C0-3AFF-B808-EFB69A2DA960> /System/Library/PrivateFrameworks/UserSafety.framework/Versions/A/UserSafety\\n1314 |        0x26faa4000 -        0x26fb6a4f7  com.apple.VDAF (1.0 - 29) <B46FBFD2-035F-3F81-B26D-0BEA13BA1146> /System/Library/PrivateFrameworks/VDAF.framework/Versions/A/VDAF\\n1315 |        0x270983000 -        0x270a0345f  com.apple.vectordb.VectorSearch (1.0 - 38.1) <1446DD66-22C8-37B7-B561-4C2BAFFB62CB> /System/Library/PrivateFrameworks/VectorSearch.framework/Versions/A/VectorSearch\\n1316 |        0x270a6b000 -        0x270a6c197  com.apple.VideoToolboxParavirtualizationSupport (40.5.10 - 40.5.10) <121BCC37-9703-3A63-96E1-2D5AB629B5F3> /System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport\\n1317 |        0x27150d000 -        0x27155703f  com.apple.VisionCore (8.0.78 - 8.0.78) <64015DA8-B4C6-300D-AF70-F69997114135> /System/Library/PrivateFrameworks/VisionCore.framework/Versions/A/VisionCore\\n1318 |        0x271558000 -        0x2716476ff  com.apple.VisionKitCore (1.0 - 3) <D71DE3AC-7E40-3FEB-8CD2-24147EEE2421> /System/Library/PrivateFrameworks/VisionKitCore.framework/Versions/A/VisionKitCore\\n1319 |        0x272c31000 -        0x272eaad9f  com.apple.WebGPU (20621 - 20621.********) <23E58918-3740-3161-8872-587E40914AEF> /System/Library/PrivateFrameworks/WebGPU.framework/Versions/A/WebGPU\\n1320 |        0x273078000 -        0x273096f1f  com.apple.WindowManagement (278.4.7) <BE8C2E6B-BD70-3ABE-A9B3-7ADB0E94EB31> /System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement\\n1321 |        0x274030000 -        0x27403457f  com.apple.WritingTools (1.0 - 1) <3E70C504-F142-30E2-B5A2-880629D5E449> /System/Library/PrivateFrameworks/WritingTools.framework/Versions/A/WritingTools\\n1322 |        0x274605000 -        0x27479c553  com.apple.-JetEngine-SwiftUI (1.0 - 1) <8595365B-64EB-3BE0-B6F6-5E127AD16A4E> /System/Library/PrivateFrameworks/_JetEngine_SwiftUI.framework/Versions/A/_JetEngine_SwiftUI\\n1323 |        0x274f0b000 -        0x274fadddf  com.apple.iCloudQuota (1.0 - 1) <BB65DCBE-B627-387D-B7EE-9A0B7F74AD70> /System/Library/PrivateFrameworks/iCloudQuota.framework/Versions/A/iCloudQuota\\n1324 |        0x274fae000 -        0x27509075f  com.apple.iCloudQuotaUI (1.0 - 1) <358EA90F-F47C-332F-91DA-494DDC0DDBE1> /System/Library/PrivateFrameworks/iCloudQuotaUI.framework/Versions/A/iCloudQuotaUI\\n1325 |        0x277375000 -        0x2773c043f  com.apple.icloudMCCKit (1.0 - 1) <6A4352D3-2E43-3E97-BCC0-315BAA18283F> /System/Library/PrivateFrameworks/icloudMCCKit.framework/Versions/A/icloudMCCKit\\n1326 |        0x279906000 -        0x279908c5f  libAXSafeCategoryBundle.dylib (3148.15.26) <CFB6F603-3334-3EBB-B6C4-D0BDF74321D8> /usr/lib/libAXSafeCategoryBundle.dylib\\n1327 |        0x279988000 -        0x279a20a27  libAppleArchive.dylib (423.100.6) <6F7177FD-79E5-3A9C-B9A5-F7E473F96599> /usr/lib/libAppleArchive.dylib\\n1328 |        0x279aa9000 -        0x279aaaa3f  libBASupport.dylib (186.100.4) <BB7D29FF-4311-398E-AAF9-648971A1778F> /usr/lib/libBASupport.dylib\\n1329 |        0x279abd000 -        0x279ac76df  libCoreEntitlements.dylib (69.100.2) <A4E09B2C-0ABE-3F42-85CB-1117EF03666F> /usr/lib/libCoreEntitlements.dylib\\n1330 |        0x279ad3000 -        0x279add23f  libEndpointSecuritySystem.dylib (511.120.2) <E02F8488-0F42-3596-8617-AF8D72725225> /usr/lib/libEndpointSecuritySystem.dylib\\n1331 |        0x279c5d000 -        0x279c647ff  libReverseProxyDevice.dylib (102) <8014F80D-8D74-37E0-8F28-A7192B9915F2> /usr/lib/libReverseProxyDevice.dylib\\n1332 |        0x279c65000 -        0x279c6c109  libRosetta.dylib (349) <EEB10206-44AA-3304-B4BA-1EE3D29854AF> /usr/lib/libRosetta.dylib\\n1333 |        0x279c81000 -        0x279c94a9f  libSESShared.dylib (55.4) <2B68864F-BC91-33DC-A289-0BC90C4F47F1> /usr/lib/libSESShared.dylib\\n1334 |        0x279cc7000 -        0x279cc735f  libSpatial.dylib (99) <434D6E72-4982-3DB3-9C41-9251AA21FE63> /usr/lib/libSpatial.dylib\\n1335 |        0x279cc8000 -        0x279cd141f  libTLE.dylib (69.100.2) <4701FF4D-63C1-3450-9410-25B0973FF049> /usr/lib/libTLE.dylib\\n1336 |        0x279efc000 -        0x27a00bf7f  libcrypto.46.dylib (107) <BE2F59C2-467C-3204-9B39-F5192E60249A> /usr/lib/libcrypto.46.dylib\\n1337 |        0x27a049000 -        0x27a063497  libhvf.dylib (4.2) <4E482655-DAA2-3539-AE4E-AF27DDF48139> /usr/lib/libhvf.dylib\\n1338 |        0x27a508000 -        0x27a50ff27  libmrc.dylib (2600.120.12) <BBE69B11-5F5F-3256-9045-14590F72ACD3> /usr/lib/libmrc.dylib\\n1339 |        0x27aa24000 -        0x27aa5c147  libssl.48.dylib (107) <117886BE-A6C3-3F1C-AF64-6671C96F996A> /usr/lib/libssl.48.dylib\\n1340 |        0x27ac21000 -        0x27ac40757  libswiftAppleArchive.dylib (423.100.6) <3977BA2F-43DC-32F2-A863-5F5C8653A9F0> /usr/lib/swift/libswiftAppleArchive.dylib\\n1341 |        0x27ac45000 -        0x27ac458d9  libswiftDataDetection.dylib (801.2) <A5B1E687-BF15-36F3-8BC0-2AA98B537C82> /usr/lib/swift/libswiftDataDetection.dylib\\n1342 |        0x27ac46000 -        0x27ac5a9af  libswiftDistributed.dylib (6.1.2 - *******.2) <B32D5BCB-FCA0-3051-B1AF-8670B7F680EE> /usr/lib/swift/libswiftDistributed.dylib\\n1343 |        0x27ac63000 -        0x27ac695e4  libswiftMLCompute.dylib (82) <BB12C8DE-F2B6-3988-B710-816CBCFE2422> /usr/lib/swift/libswiftMLCompute.dylib\\n1344 |        0x27ac72000 -        0x27ac7fe97  libswiftObservation.dylib (6.1.2 - *******.2) <862E0D06-8E33-37B1-943C-DBDE365DD864> /usr/lib/swift/libswiftObservation.dylib\\n1345 |        0x27ac81000 -        0x27ac89da2  libswiftPassKit.dylib (1591.6.5.1) <BBABC199-A08D-3CB9-ADBA-45CA22B46348> /usr/lib/swift/libswiftPassKit.dylib\\n1346 |        0x27ac8f000 -        0x27ac9b847  libswiftRegexBuilder.dylib (6.1.2 - *******.2) <43BE4FCC-68C1-3CF5-8C3C-F2113EF49E98> /usr/lib/swift/libswiftRegexBuilder.dylib\\n1347 |        0x27aca1000 -        0x27acdbc5f  libswiftSpatial.dylib (99) <3954927D-AABA-3C12-B41B-ECAB542F43DB> /usr/lib/swift/libswiftSpatial.dylib\\n1348 |        0x27acdf000 -        0x27ace897f  libswiftSynchronization.dylib (6.1.2 - *******.2) <8C045C2A-786B-39A8-998F-B349D492C439> /usr/lib/swift/libswiftSynchronization.dylib\\n1349 |        0x27ace9000 -        0x27ad036f7  libswiftSystem.dylib (64) <485ADD3A-3CDE-3A0C-945C-5A7AC5A1E9B9> /usr/lib/swift/libswiftSystem.dylib\\n1350 |        0x27ad05000 -        0x27ad1733e  libswiftVideoToolbox.dylib (3225.7.1) <EE817AC7-ED85-3884-8694-A3E438D4CC0A> /usr/lib/swift/libswiftVideoToolbox.dylib\\n1351 |        0x27ad58000 -        0x27ad5a7ef  libswift_Builtin_float.dylib (6.1.2 - *******.2) <68E4DCE3-2ECC-392B-AF98-2F43F865698B> /usr/lib/swift/libswift_Builtin_float.dylib\\n1352 |        0x27ad5b000 -        0x27addb6a3  libswift_Concurrency.dylib (6.1.2 - *******.2) <EBE15305-7568-35A3-B75E-19561C0A0E02> /usr/lib/swift/libswift_Concurrency.dylib\\n1353 |        0x27ae17000 -        0x27aeb92cf  libswift_RegexParser.dylib (6.1.2 - *******.2) <384BD28E-CCBA-3C5C-8BA4-7597A719EA84> /usr/lib/swift/libswift_RegexParser.dylib\\n1354 |        0x27aeba000 -        0x27af49e27  libswift_StringProcessing.dylib (6.1.2 - *******.2) <0F1E4C64-585D-3274-BBCF-9F44A7433A80> /usr/lib/swift/libswift_StringProcessing.dylib\\n1355 |        0x27af4d000 -        0x27af4e497  libswift_errno.dylib (310.100.8.0.1) <FCDB3DAE-ABDC-3A5B-96B6-B726D484DEB0> /usr/lib/swift/libswift_errno.dylib\\n1356 |        0x27af4f000 -        0x27af5177f  libswift_math.dylib (310.100.8.0.1) <C6AA687D-6F99-3C5E-81B2-E620C22E8E7F> /usr/lib/swift/libswift_math.dylib\\n1357 |        0x27af52000 -        0x27af52565  libswift_signal.dylib (310.100.8.0.1) <747E658F-AD64-3F51-BEAA-B905CDD8FC4D> /usr/lib/swift/libswift_signal.dylib\\n1358 |        0x27af53000 -        0x27af53dcf  libswift_stdio.dylib (310.100.8.0.1) <F9E38175-23DF-3FCA-9DAF-617E369A2E8A> /usr/lib/swift/libswift_stdio.dylib\\n1359 |        0x27af54000 -        0x27af547c7  libswift_time.dylib (310.100.8.0.1) <4FBC9E6B-76D1-3635-85EB-57265A5BF1E3> /usr/lib/swift/libswift_time.dylib\\n1360 |        0x27af55000 -        0x27af55737  libswiftsys_time.dylib (310.100.8.0.1) <1D675CAA-641A-349F-87CE-3BC22B83623D> /usr/lib/swift/libswiftsys_time.dylib\\n1361 |        0x27af56000 -        0x27af5698f  libswiftunistd.dylib (310.100.8.0.1) <F6C4DE57-0C35-3A49-AE31-76C2E5F446B1> /usr/lib/swift/libswiftunistd.dylib\\n1362 |        0x27b08d000 -        0x27b090deb  libsystem_darwindirectory.dylib (122) <135AA8DB-7980-3926-836B-62CB50A06836> /usr/lib/system/libsystem_darwindirectory.dylib\\n1363 |        0x27b091000 -        0x27b098523  libsystem_eligibility.dylib (181.120.32) <8D8AD9B1-529A-3879-AC11-B670974F0C18> /usr/lib/system/libsystem_eligibility.dylib\\n1364 |        0x27b099000 -        0x27b09e6c5  libsystem_sanitizers.dylib (19) <2C6BF94B-EB62-3C05-AC78-55FD05EEA166> /usr/lib/system/libsystem_sanitizers.dylib\\n1365 |        0x27b1da000 -        0x27cbc559f  libusd_ms.dylib (22.6.2) <E9DE4955-D4FE-3DDB-9054-9F9B58091A6B> /usr/lib/usd/libusd_ms.dylib\\n\\n</file_content>\\n\\n<environment_details>\\n# VSCode Visible Files\\ncatch\\n\\n# VSCode Open Tabs\\ncatch\\n\\n# Current Time\\n2025/7/10 上午11:43:08 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>ask</slug>\\n<name>❓ Ask</name>\\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\\n\\n\\nNOTE: You are currently in '❓ Ask' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as '🏗️ Architect' mode.\\n\\n# Current Workspace Directory (/Users/<USER>/Desktop/test) Files\\ncatch\\n</environment_details>\",\"tokensIn\":119092,\"tokensOut\":701,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1752118988533, "type": "say", "say": "checkpoint_saved", "text": "6c14cc22d78d95a7abb5f4938b9c78ccbd91e4e3", "checkpoint": {"isFirst": true, "from": "6c14cc22d78d95a7abb5f4938b9c78ccbd91e4e3", "to": "6c14cc22d78d95a7abb5f4938b9c78ccbd91e4e3"}}, {"ts": 1752119006006, "type": "say", "say": "text", "text": "我来分析一下这个进程的内存使用情况和潜在的内存泄漏问题：\n\n1. 基本信息：\n- 进程名称：Code Helper (Plugin) \n- 进程 ID：50419\n- 运行时间：从 2025-07-07 15:22:49 到 2025-07-10 11:40:52，运行了约3天\n- 物理内存占用：\n  - 当前：4.1GB\n  - 峰值：4.4GB\n\n2. 内存使用分析：\n- 内存占用较高，4.1GB 对于一个插件进程来说偏大\n- 从启动到采样时运行了3天，内存增长了4.1GB，说明可能存在内存累积问题\n\n3. 线程分析：\n- 主要活跃线程：\n  - 主线程(Thread_959804)\n  - ThreadPoolServiceThread\n  - ThreadPoolForegroundWorker\n  - ThreadPoolBackgroundWorker \n  - Chrome_ChildIOThread\n  - 多个工作线程\n\n4. 可能的内存问题：\n- V8引擎相关：\n  ```\n  v8::Object::SetNativeDataProperty\n  v8::Isolate::SetAddCrashKeyCallback\n  v8::ScriptCompiler::CompileModule\n  ```\n  看到大量V8引擎相关调用，可能存在JavaScript对象未正确释放的问题\n\n- 定时器相关：\n  ```\n  node::Environment::RunTimers\n  uv__run_timers\n  ```\n  定时器回调可能没有正确清理资源\n\n- 事件循环：\n  ```\n  node::SpinEventLoopInternal\n  uv_run\n  ```\n  事件循环中可能有未释放的闭包或引用\n\n5. 建议措施：\n- 检查插件代码中的定时器、事件监听器是否在不需要时正确移除\n- 检查是否有循环引用导致的内存泄漏\n- 考虑定期重启插件进程来释放内存\n- 使用Chrome DevTools的Memory工具进行堆内存分析\n- 检查V8相关的对象生命周期管理\n\n6. 结论：\n该进程存在内存持续增长的问题，4.1GB的内存占用对插件进程来说过高。主要怀疑是JavaScript运行时的内存管理问题，建议进行代码review和内存分析来定位具体原因。", "partial": false}, {"ts": 1752119019478, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[ERROR] You did not use a tool in your previous response! Please retry with a tool use.\\n\\n# Reminder: Instructions for Tool Use\\n\\nTool uses are formatted using XML-style tags. The tool name itself becomes the XML tag name. Each parameter is enclosed within its own set of tags. Here's the structure:\\n\\n<actual_tool_name>\\n<parameter1_name>value1</parameter1_name>\\n<parameter2_name>value2</parameter2_name>\\n...\\n</actual_tool_name>\\n\\nFor example, to use the attempt_completion tool:\\n\\n<attempt_completion>\\n<result>\\nI have completed the task...\\n</result>\\n</attempt_completion>\\n\\nAlways use the actual tool name as the XML tag name for proper parsing and execution.\\n\\n# Next Steps\\n\\nIf you have completed the user's task, use the attempt_completion tool. \\nIf you require additional information from the user, use the ask_followup_question tool. \\nOtherwise, if you have not completed the task and do not need additional information, then proceed with the next step of the task. \\n(This is an automated message, so do not respond to it conversationally.)\\n\\n<environment_details>\\n# VSCode Visible Files\\ncatch\\n\\n# VSCode Open Tabs\\ncatch\\n\\n# Current Time\\n2025/7/10 上午11:43:39 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>ask</slug>\\n<name>❓ Ask</name>\\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\\n\\n\\nNOTE: You are currently in '❓ Ask' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as '🏗️ Architect' mode.\\n</environment_details>\",\"tokensIn\":120230,\"tokensOut\":500,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1752119019909, "type": "say", "say": "condense_context_error", "text": "没有足够的对话来压缩上下文"}, {"ts": 1752119035908, "type": "say", "say": "text", "text": ""}, {"ts": 1752119035930, "type": "say", "say": "completion_result", "text": "基于日志分析，该VSCode插件进程(Code Helper)存在明显的内存问题：\n\n1. 内存使用情况：\n- 当前物理内存：4.1GB\n- 峰值物理内存：4.4GB\n- 运行时长：约3天(2025-07-07 15:22至2025-07-10 11:40)\n\n2. 内存泄漏迹象：\n- 内存持续增长到4.1GB，对插件进程来说异常高\n- 调用栈显示大量V8引擎相关操作：\n  - `v8::Object::SetNativeDataProperty`\n  - `v8::Isolate::SetAddCrashKeyCallback`\n  - `v8::ScriptCompiler::CompileModule`\n可能存在JavaScript对象未被正确释放\n\n3. 问题源头：\n- 定时器管理问题：出现`node::Environment::RunTimers`调用，可能存在未清理的定时器\n- 事件循环：`node::SpinEventLoopInternal`和`uv_run`表明可能有未释放的事件监听器\n- V8内存：大量V8引擎调用表明JavaScript运行时可能存在内存管理问题\n\n4. 建议解决方案：\n- 检查并清理未使用的事件监听器\n- 确保定时器在不需要时被清除\n- 使用Chrome DevTools的Memory工具分析堆内存\n- 定期重启插件进程以释放内存\n- 审查代码中的闭包和引用，避免循环引用\n\n结论：进程确实存在内存泄漏，建议从JavaScript运行时的资源管理入手排查。", "partial": false}, {"ts": 1752119044259, "type": "ask", "ask": "completion_result", "text": ""}]