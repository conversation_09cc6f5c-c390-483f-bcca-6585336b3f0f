[{"role": "user", "content": [{"type": "text", "text": "<task>\nvscode的this.context.globalState.update()的值存储在哪\n</task>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\npackages/agent-driven/src/core/webview/JoycoderProvider.ts\n\n# VSCode Open Tabs\npackages/agent-driven/initAgentDriven.ts,packages/agent-driven/src/core/webview/JoycoderProvider.ts,packages/agent-setting/src/components/mcp/McpMarketView.scss\n\n# Current Time\nCurrent time in ISO 8601 UTC format: 2025-07-21T08:36:29.552Z\nUser time zone: Asia/Shanghai, UTC+8:00\n\n# Current Cost\n$0.00\n\n# Current Mode\n<slug>ask</slug>\n<name>❓ Ask</name>\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\n\n\n# Current Workspace Directory (/Users/<USER>/Documents/work/JoyCoder-Plugin) Files\nassets/\nassets/agent/\nassets/agent/default-themes/\nassets/codicon/\nassets/d2c/\nassets/dark/\nassets/iconfont/\nassets/iconfont/joycode-iconfont/\nassets/light/\nassets/sec/\nassets/translation/\nassets/treeView/\nassets/wasm/\ndoc/\npackages/\npackages/agent-common/\npackages/agent-common/src/\npackages/agent-common/src/demo/\npackages/agent-common/src/tools/\npackages/agent-common/src/utils/\npackages/agent-common/src/vscode/\npackages/agent-driven/\npackages/agent-driven/src/\npackages/agent-driven/src/__mocks__/\npackages/agent-driven/src/adaptor/\npackages/agent-driven/src/adaptor/api/\npackages/agent-driven/src/adaptor/api/providers/\npackages/agent-driven/src/adaptor/api/transform/\npackages/agent-driven/src/adaptor/contextMenu/\npackages/agent-driven/src/adaptor/translate/\npackages/agent-driven/src/core/\npackages/agent-driven/src/core/assistant-message/\npackages/agent-driven/src/core/config/\npackages/agent-driven/src/core/context-management/\npackages/agent-driven/src/core/context-management/__tests__/\npackages/agent-driven/src/core/context-tracking/\npackages/agent-driven/src/core/diff/\npackages/agent-driven/src/core/diff/strategies/\npackages/agent-driven/src/core/diff/strategies/__tests__/\npackages/agent-driven/src/core/ignore/\npackages/agent-driven/src/core/JoyCoder/\npackages/agent-driven/src/core/JoyCoder/tools/\npackages/agent-driven/src/core/mentions/\npackages/agent-driven/src/core/prompts/\npackages/agent-driven/src/core/prompts/__tests__/\npackages/agent-driven/src/core/prompts/instructions/\npackages/agent-driven/src/core/prompts/sections/\npackages/agent-driven/src/core/prompts/tools/\npackages/agent-driven/src/core/slash-commands/\npackages/agent-driven/src/core/sliding-window/\npackages/agent-driven/src/core/storage/\npackages/agent-driven/src/core/user-pormpt/\npackages/agent-driven/src/core/webview/\npackages/agent-driven/src/exports/\npackages/agent-driven/src/integrations/\npackages/agent-driven/src/integrations/checkpoints/\npackages/agent-driven/src/integrations/debug/\npackages/agent-driven/src/integrations/diagnostics/\npackages/agent-driven/src/integrations/editor/\npackages/agent-driven/src/integrations/misc/\npackages/agent-driven/src/integrations/notifications/\npackages/agent-driven/src/integrations/terminal/\npackages/agent-driven/src/integrations/theme/\npackages/agent-driven/src/integrations/theme/default-themes/\npackages/agent-driven/src/integrations/workspace/\npackages/agent-driven/src/schemas/\npackages/agent-driven/src/services/\npackages/agent-driven/src/services/auth/\npackages/agent-driven/src/services/browser/\npackages/agent-driven/src/services/code-index/\npackages/agent-driven/src/services/code-index/__tests__/\npackages/agent-driven/src/services/code-index/constants/\npackages/agent-driven/src/services/code-index/embedders/\npackages/agent-driven/src/services/code-index/embedders/__tests__/\npackages/agent-driven/src/services/code-index/interfaces/\npackages/agent-driven/src/services/code-index/server/\npackages/agent-driven/src/services/code-index/server/__tests__/\npackages/agent-driven/src/services/code-index/shared/\npackages/agent-driven/src/services/code-index/vector-store/\npackages/agent-driven/src/services/code-index/vector-store/__tests__/\npackages/agent-driven/src/services/glob/\npackages/agent-driven/src/services/mcp/\npackages/agent-driven/src/services/ripgrep/\npackages/agent-driven/src/services/tree-sitter/\npackages/agent-driven/src/services/tree-sitter/queries/\npackages/agent-driven/src/shared/\npackages/agent-driven/src/test/\npackages/agent-driven/src/test/suite/\npackages/agent-driven/src/test/webview/\npackages/agent-driven/src/utils/\npackages/agent-driven/web-agent/\npackages/agent-driven/web-agent/public/\npackages/agent-driven/web-agent/scripts/\npackages/agent-driven/web-agent/src/\npackages/agent-driven/web-agent/src/adaptor/\npackages/agent-driven/web-agent/src/adaptor/components/\npackages/agent-driven/web-agent/src/adaptor/locales/\npackages/agent-driven/web-agent/src/adaptor/login/\npackages/agent-driven/web-agent/src/assets/\npackages/agent-driven/web-agent/src/assets/animations/\npackages/agent-driven/web-agent/src/assets/images/\npackages/agent-driven/web-agent/src/components/\npackages/agent-driven/web-agent/src/components/account/\npackages/agent-driven/web-agent/src/components/browser/\npackages/agent-driven/web-agent/src/components/chat/\npackages/agent-driven/web-agent/src/components/chat/__tests__/\npackages/agent-driven/web-agent/src/components/common/\npackages/agent-driven/web-agent/src/components/history/\npackages/agent-driven/web-agent/src/components/mcp/\npackages/agent-driven/web-agent/src/components/prompts/\npackages/agent-driven/web-agent/src/components/settings/\npackages/agent-driven/web-agent/src/components/settings/__tests__/\npackages/agent-driven/web-agent/src/components/welcome/\npackages/agent-driven/web-agent/src/context/\npackages/agent-driven/web-agent/src/types/\npackages/agent-driven/web-agent/src/utils/\npackages/agent-driven/web-agent/src/utils/__tests__/\npackages/agent-setting/\npackages/agent-setting/public/\npackages/agent-setting/src/\npackages/agent-setting/src/assets/\npackages/agent-setting/src/assets/animations/\npackages/agent-setting/src/assets/images/\npackages/agent-setting/src/components/\npackages/agent-setting/src/components/AgentManagement/\npackages/agent-setting/src/components/Codebase/\npackages/agent-setting/src/components/common/\npackages/agent-setting/src/components/General/\npackages/agent-setting/src/components/mcp/\npackages/agent-setting/src/components/Rules/\npackages/agent-setting/src/components/setting/\npackages/agent-setting/src/components/Special/\npackages/agent-setting/src/hooks/\npackages/agent-setting/src/messages/\npackages/agent-setting/src/typings/\npackages/agent-setting/src/utils/\npackages/lib-typescript-sdk/\npackages/plugin-base-ai/\npackages/plugin-base-ai/src/\npackages/plugin-base-ai/src/contextMenu/\npackages/plugin-base-ai/src/cursor/\npackages/plugin-base-ai/src/dialog/\npackages/plugin-base-ai/src/inlinechat/\npackages/plugin-base-ai/src/knowledge/\npackages/plugin-base-ai/src/langchain/\npackages/plugin-base-ai/src/langchain/agent/\npackages/plugin-base-ai/src/langchain/chat_models/\npackages/plugin-base-ai/src/langchain/document_loaders/\npackages/plugin-base-ai/src/langchain/embeddings/\npackages/plugin-base-ai/src/langchain/llms/\npackages/plugin-base-ai/src/langchain/shared/\npackages/plugin-base-ai/src/langchain/tools/\npackages/plugin-base-ai/src/langchain/tools/knowledge/\npackages/plugin-base-ai/src/langchain/tools/search/\npackages/plugin-base-ai/src/langchain/tools/shared/\npackages/plugin-base-ai/src/langchain/tools/webbrowser/\npackages/plugin-base-ai/src/proxy/\npackages/plugin-base-ai/src/terminal/\npackages/plugin-base-ai/src/warehouseQuestion/\npackages/plugin-base-ai-d2c/\npackages/plugin-base-ai-d2c/src/\npackages/plugin-base-ai-d2c/src/agents/\npackages/plugin-base-ai-d2c/src/bridge/\npackages/plugin-base-ai-d2c/src/functions/\npackages/plugin-base-ai-d2c/src/llm/\npackages/plugin-base-ai-d2c/src/utils/\npackages/plugin-base-app-manager/\npackages/plugin-base-app-manager/src/\npackages/plugin-base-browse-lite/\npackages/plugin-base-browse-lite/demo/\npackages/plugin-base-browser/\npackages/plugin-base-browser/src/\npackages/plugin-base-browser/src/browser/\npackages/plugin-base-browser/src/provider/\npackages/plugin-base-browser/src/shared/\npackages/plugin-base-browser/src/shared/utils/\npackages/plugin-base-browser/src/webview-panel/\npackages/plugin-base-cli-factory/\npackages/plugin-base-cli-factory/src/\npackages/plugin-base-cli-factory/src/commands/\npackages/plugin-base-code-completion/\npackages/plugin-base-code-completion/src/\npackages/plugin-base-code-completion/src/stats/\npackages/plugin-base-code-completion/src/utils/\npackages/plugin-base-code-completion/test/\npackages/plugin-base-code-completion/test/java/\npackages/plugin-base-code-completion/test/javascript/\npackages/plugin-base-code-completion/test/python/\npackages/plugin-base-code-context/\npackages/plugin-base-code-context/src/\npackages/plugin-base-code-context/src/indexing/\npackages/plugin-base-code-context/src/indexing/embeddings/\npackages/plugin-base-code-context/src/indexing/embeddings/transformers/\npackages/plugin-base-code-detection/\npackages/plugin-base-code-detection/src/\nremote_binary/\nscripts/\nsnippets/\nsrc/\ntypings/\n\n(File list truncated. Use list_files on specific subdirectories if you need to explore further.)\nYou have not created a todo list yet. Create one with `update_todo_list` if your task is complicated or involves multiple steps.\n</environment_details>"}], "ts": 1753086989678}, {"role": "assistant", "content": [{"type": "text", "text": "让我来解释 VSCode 中 `this.context.globalState.update()` 的值存储位置。\n\n<thinking>\n1. 这是一个关于 VSCode 扩展开发中状态存储的问题\n2. 需要了解 VSCode 的全局状态存储机制\n3. globalState 是 VSCode 提供的持久化存储机制\n</thinking>\n\nVSCode 的 [`this.context.globalState.update()`](vscode-api:extensionContext.globalState) 存储的值实际上保存在以下位置：\n\n1. 在 macOS 系统中（根据系统信息）：\n```\n~/Library/Application Support/Code/User/globalStorage/<extension-id>/\n```\n\n2. 在其他系统中：\n- Windows: `%APPDATA%\\Code\\User\\globalStorage\\<extension-id>\\`\n- Linux: `~/.config/Code/User/globalStorage/<extension-id>/`\n\n其中 `<extension-id>` 是你的 VSCode 扩展的唯一标识符（例如：`vscode.git`）。\n\n几个重要说明：\n\n1. 存储格式：\n- 数据以 JSON 格式存储在 `state.json` 文件中\n- 每次调用 `update()` 都会更新这个文件\n\n2. 存储特点：\n- 这是一个持久化存储\n- 在 VSCode 重启后数据仍然保留\n- 在不同的工作区（workspace）中都可以访问\n- 存储大小限制为 100MB\n\n3. 与 workspaceState 的区别：\n- globalState：在所有工作区共享\n- workspaceState：仅在当前工作区有效\n\n使用示例：\n```typescript\n// 存储数据\nawait context.globalState.update('key', value);\n\n// 读取数据\nconst value = context.globalState.get('key');\n```\n\n你可以通过访问相应的目录来查看或验证存储的数据。"}], "ts": 1753087002966}, {"role": "user", "content": [{"type": "text", "text": "[ERROR] You did not use a tool in your previous response! Please retry with a tool use.\n\n# Reminder: Instructions for Tool Use\n\nTool uses are formatted using XML-style tags. The tool name itself becomes the XML tag name. Each parameter is enclosed within its own set of tags. Here's the structure:\n\n<actual_tool_name>\n<parameter1_name>value1</parameter1_name>\n<parameter2_name>value2</parameter2_name>\n...\n</actual_tool_name>\n\nFor example, to use the attempt_completion tool:\n\n<attempt_completion>\n<result>\nI have completed the task...\n</result>\n</attempt_completion>\n\nAlways use the actual tool name as the XML tag name for proper parsing and execution.\n\n# Next Steps\n\nIf you have completed the user's task, use the attempt_completion tool. \nIf you require additional information from the user, use the ask_followup_question tool. \nOtherwise, if you have not completed the task and do not need additional information, then proceed with the next step of the task. \n(This is an automated message, so do not respond to it conversationally.)"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\npackages/agent-driven/src/core/webview/JoycoderProvider.ts\n\n# VSCode Open Tabs\npackages/agent-driven/initAgentDriven.ts,packages/agent-driven/src/core/webview/JoycoderProvider.ts,packages/agent-setting/src/components/mcp/McpMarketView.scss\n\n# Current Time\nCurrent time in ISO 8601 UTC format: 2025-07-21T08:36:42.983Z\nUser time zone: Asia/Shanghai, UTC+8:00\n\n# Current Cost\n$0.00\n\n# Current Mode\n<slug>ask</slug>\n<name>❓ Ask</name>\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\nYou have not created a todo list yet. Create one with `update_todo_list` if your task is complicated or involves multiple steps.\n</environment_details>"}], "ts": 1753087002990}, {"role": "assistant", "content": [{"type": "text", "text": "<thinking>\n我需要使用工具来完成这个解释任务。由于这是一个关于VSCode存储机制的问题，我可以使用browser_action工具来访问VSCode的官方文档，获取准确的信息。\n</thinking>\n\n<browser_action>\n<action>launch</action>\n<url>https://code.visualstudio.com/api/references/vscode-api#ExtensionContext</url>\n</browser_action>"}], "ts": 1753087009576}, {"role": "user", "content": [{"type": "text", "text": "[browser_action for 'launch'] Result:"}, {"type": "text", "text": "The browser action has been executed. The console logs and screenshot have been captured for your analysis.\n\nConsole logs:\n(No new logs)\n\n(REMEMBER: if you need to proceed to using non-`browser_action` tools or launch a new browser, you MUST first close cline browser. For example, if after analyzing the logs and screenshot you need to edit a file, you must first close the browser before you can use the write_to_file tool.)"}, {"type": "image", "source": {"type": "base64", "media_type": "image/webp", "data": "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"}}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\npackages/agent-driven/src/core/webview/JoycoderProvider.ts\n\n# VSCode Open Tabs\npackages/agent-driven/initAgentDriven.ts,packages/agent-driven/src/core/webview/JoycoderProvider.ts,packages/agent-setting/src/components/mcp/McpMarketView.scss\n\n# Current Time\nCurrent time in ISO 8601 UTC format: 2025-07-21T08:36:57.391Z\nUser time zone: Asia/Shanghai, UTC+8:00\n\n# Current Cost\n$0.00\n\n# Current Mode\n<slug>ask</slug>\n<name>❓ Ask</name>\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\nYou have not created a todo list yet. Create one with `update_todo_list` if your task is complicated or involves multiple steps.\n</environment_details>"}], "ts": 1753087017394}, {"role": "assistant", "content": [{"type": "text", "text": "<thinking>\n已经获取了VSCode API文档，现在需要关闭浏览器并使用attempt_completion来提供完整的答案。\n</thinking>\n\n<browser_action>\n<action>close</action>\n</browser_action>\n\n<attempt_completion>\n<result\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]"}], "ts": 1753087023406}, {"role": "user", "content": [{"type": "text", "text": "[browser_action for 'close'] Result:"}, {"type": "text", "text": "The browser has been closed. You may now proceed to using other tools."}, {"type": "text", "text": "Tool [attempt_completion] was not executed because a tool has already been used in this message. Only one tool may be used per message. You must assess the first tool's result before proceeding to use the next tool."}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\npackages/agent-driven/src/core/webview/JoycoderProvider.ts\n\n# VSCode Open Tabs\npackages/agent-driven/initAgentDriven.ts,packages/agent-driven/src/core/webview/JoycoderProvider.ts,packages/agent-setting/src/components/mcp/McpMarketView.scss\n\n# Current Time\nCurrent time in ISO 8601 UTC format: 2025-07-21T08:37:03.415Z\nUser time zone: Asia/Shanghai, UTC+8:00\n\n# Current Cost\n$0.00\n\n# Current Mode\n<slug>ask</slug>\n<name>❓ Ask</name>\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\nYou have not created a todo list yet. Create one with `update_todo_list` if your task is complicated or involves multiple steps.\n</environment_details>"}], "ts": 1753087023416}, {"role": "assistant", "content": [{"type": "text", "text": "<attempt_completion>\n<result>\nVSCode 的 [`this.context.globalState.update()`](vscode-api:extensionContext.globalState) 存储的值位置如下：\n\n1. macOS 系统（根据您的系统信息）：\n```\n~/Library/Application Support/Code/User/globalStorage/<extension-id>/state.json\n```\n\n2. 其他系统参考：\n- Windows: `%APPDATA%\\Code\\User\\globalStorage\\<extension-id>\\state.json`\n- Linux: `~/.config/Code/User/globalStorage\\<extension-id>\\state.json`\n\n其中：\n- `<extension-id>` 是扩展的唯一标识符（如：`vscode.git`）\n- 数据以 JSON 格式存储在 `state.json` 文件中\n- 这是一个持久化存储，在 VSCode 重启后数据仍然保留\n- 在所有工作区中都可以访问这些数据\n- 存储大小上限为 100MB\n\n这与 `workspaceState` 的主要区别是：`globalState` 在所有工作区共享数据，而 `workspaceState` 的数据仅在当前工作区有效。\n</result>\n</attempt_completion>"}], "ts": 1753087039901}]