[{"ts": 1750859641887, "type": "say", "say": "text", "text": "根据当前git历史，帮我把git历史中的几个commit去掉，也就是剔除掉到其中的几个commit，id分别为：90cc2b6d7815951141a861f666246a787227b730、82245bb84e51667c551f67f55b953236c62c92a6、e1da20c837c4b1f21f05ef910a018531f8f16458，如果遇到冲突问题，可以停止等待，交给我来解决冲突，解决完之后，我会提示继续执行，", "images": []}, {"ts": 1750859641896, "type": "say", "say": "api_req_started", "text": "{\"request\":\"<task>\\n根据当前git历史，帮我把git历史中的几个commit去掉，也就是剔除掉到其中的几个commit，id分别为：90cc2b6d7815951141a861f666246a787227b730、82245bb84e51667c551f67f55b953236c62c92a6、e1da20c837c4b1f21f05ef910a018531f8f16458，如果遇到冲突问题，可以停止等待，交给我来解决冲突，解决完之后，我会提示继续执行，\\n</task>\\n\\n<environment_details>\\n# VSCode Visible Files\\npackages/agent-driven/web-agent/src/components/chat/ChatRow.tsx\\n\\n# VSCode Open Tabs\\npackages/agent-setting/src/components/setting/index.tsx,packages/agent-driven/web-agent/src/components/chat/ContextMenu.tsx,package.json,packages/agent-driven/initAgentDriven.ts,packages/agent-driven/src/core/JoyCoder/presentAssistantMessage.ts,packages/agent-driven/web-agent/src/components/chat/ChatRow.tsx,packages/agent-setting/src/components/AgentManagement/index.tsx,packages/plugin-base-ai/src/dialog/index.ts\\n\\n# Current Time\\n2025/6/25 下午9:54:01 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n(Not available)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>code</slug>\\n<name>💻 Code</name>\\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\\n\\n\\n# Current Workspace Directory (/Users/<USER>/Documents/work/JoyCoder-Plugin) Files\\nassets/\\nassets/agent/\\nassets/agent/default-themes/\\nassets/codicon/\\nassets/d2c/\\nassets/dark/\\nassets/iconfont/\\nassets/iconfont/joycode-iconfont/\\nassets/light/\\nassets/sec/\\nassets/translation/\\nassets/treeView/\\nassets/wasm/\\ndoc/\\npackages/\\npackages/agent-common/\\npackages/agent-common/src/\\npackages/agent-common/src/demo/\\npackages/agent-common/src/tools/\\npackages/agent-common/src/utils/\\npackages/agent-common/src/vscode/\\npackages/agent-driven/\\npackages/agent-driven/src/\\npackages/agent-driven/src/__mocks__/\\npackages/agent-driven/src/adaptor/\\npackages/agent-driven/src/adaptor/api/\\npackages/agent-driven/src/adaptor/api/providers/\\npackages/agent-driven/src/adaptor/api/transform/\\npackages/agent-driven/src/adaptor/contextMenu/\\npackages/agent-driven/src/adaptor/translate/\\npackages/agent-driven/src/core/\\npackages/agent-driven/src/core/assistant-message/\\npackages/agent-driven/src/core/config/\\npackages/agent-driven/src/core/context-management/\\npackages/agent-driven/src/core/context-management/__tests__/\\npackages/agent-driven/src/core/context-tracking/\\npackages/agent-driven/src/core/diff/\\npackages/agent-driven/src/core/diff/strategies/\\npackages/agent-driven/src/core/diff/strategies/__tests__/\\npackages/agent-driven/src/core/ignore/\\npackages/agent-driven/src/core/JoyCoder/\\npackages/agent-driven/src/core/JoyCoder/tools/\\npackages/agent-driven/src/core/mentions/\\npackages/agent-driven/src/core/prompts/\\npackages/agent-driven/src/core/prompts/__tests__/\\npackages/agent-driven/src/core/prompts/instructions/\\npackages/agent-driven/src/core/prompts/sections/\\npackages/agent-driven/src/core/prompts/tools/\\npackages/agent-driven/src/core/slash-commands/\\npackages/agent-driven/src/core/sliding-window/\\npackages/agent-driven/src/core/storage/\\npackages/agent-driven/src/core/user-pormpt/\\npackages/agent-driven/src/core/webview/\\npackages/agent-driven/src/exports/\\npackages/agent-driven/src/integrations/\\npackages/agent-driven/src/integrations/checkpoints/\\npackages/agent-driven/src/integrations/debug/\\npackages/agent-driven/src/integrations/diagnostics/\\npackages/agent-driven/src/integrations/editor/\\npackages/agent-driven/src/integrations/misc/\\npackages/agent-driven/src/integrations/notifications/\\npackages/agent-driven/src/integrations/terminal/\\npackages/agent-driven/src/integrations/theme/\\npackages/agent-driven/src/integrations/theme/default-themes/\\npackages/agent-driven/src/integrations/workspace/\\npackages/agent-driven/src/schemas/\\npackages/agent-driven/src/services/\\npackages/agent-driven/src/services/auth/\\npackages/agent-driven/src/services/browser/\\npackages/agent-driven/src/services/code-index/\\npackages/agent-driven/src/services/code-index/__tests__/\\npackages/agent-driven/src/services/code-index/constants/\\npackages/agent-driven/src/services/code-index/embedders/\\npackages/agent-driven/src/services/code-index/embedders/__tests__/\\npackages/agent-driven/src/services/code-index/interfaces/\\npackages/agent-driven/src/services/code-index/server/\\npackages/agent-driven/src/services/code-index/server/__tests__/\\npackages/agent-driven/src/services/code-index/shared/\\npackages/agent-driven/src/services/code-index/vector-store/\\npackages/agent-driven/src/services/code-index/vector-store/__tests__/\\npackages/agent-driven/src/services/glob/\\npackages/agent-driven/src/services/mcp/\\npackages/agent-driven/src/services/ripgrep/\\npackages/agent-driven/src/services/tree-sitter/\\npackages/agent-driven/src/services/tree-sitter/queries/\\npackages/agent-driven/src/shared/\\npackages/agent-driven/src/test/\\npackages/agent-driven/src/test/suite/\\npackages/agent-driven/src/test/webview/\\npackages/agent-driven/src/utils/\\npackages/agent-driven/web-agent/\\npackages/agent-driven/web-agent/public/\\npackages/agent-driven/web-agent/scripts/\\npackages/agent-driven/web-agent/src/\\npackages/agent-driven/web-agent/src/adaptor/\\npackages/agent-driven/web-agent/src/adaptor/components/\\npackages/agent-driven/web-agent/src/adaptor/locales/\\npackages/agent-driven/web-agent/src/adaptor/login/\\npackages/agent-driven/web-agent/src/assets/\\npackages/agent-driven/web-agent/src/assets/animations/\\npackages/agent-driven/web-agent/src/components/\\npackages/agent-driven/web-agent/src/components/account/\\npackages/agent-driven/web-agent/src/components/browser/\\npackages/agent-driven/web-agent/src/components/chat/\\npackages/agent-driven/web-agent/src/components/chat/__tests__/\\npackages/agent-driven/web-agent/src/components/common/\\npackages/agent-driven/web-agent/src/components/history/\\npackages/agent-driven/web-agent/src/components/mcp/\\npackages/agent-driven/web-agent/src/components/prompts/\\npackages/agent-driven/web-agent/src/components/settings/\\npackages/agent-driven/web-agent/src/components/settings/__tests__/\\npackages/agent-driven/web-agent/src/components/welcome/\\npackages/agent-driven/web-agent/src/context/\\npackages/agent-driven/web-agent/src/types/\\npackages/agent-driven/web-agent/src/utils/\\npackages/agent-driven/web-agent/src/utils/__tests__/\\npackages/agent-runtime/\\npackages/agent-runtime/agent/\\npackages/agent-runtime/agent/bindings/\\npackages/agent-runtime/agent/bindings/kotlin/\\npackages/agent-runtime/agent/bindings/kotlin/gradle/\\npackages/agent-runtime/agent/bindings/kotlin/gradle/wrapper/\\npackages/agent-runtime/agent/bindings/kotlin/lib/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/com/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/com/sourcegraph/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/com/sourcegraph/cody/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/com/sourcegraph/cody/agent/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/com/sourcegraph/cody/agent/protocol_generated/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/test/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/test/kotlin/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/test/kotlin/org/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/test/kotlin/org/example/\\npackages/agent-runtime/agent/bindings/kotlin/src/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/kotlin/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/kotlin/com/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/kotlin/com/sourcegraph/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/kotlin/com/sourcegraph/cody/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/kotlin/com/sourcegraph/cody/protocol_generated/\\npackages/agent-runtime/agent/demo/\\npackages/agent-runtime/agent/recordings/\\npackages/agent-runtime/agent/recordings/auth_2503977039/\\npackages/agent-runtime/agent/recordings/autocomplete_2136217965/\\npackages/agent-runtime/agent/recordings/cody-chat_103640681/\\npackages/agent-runtime/agent/recordings/customCommandsClient_509552979/\\npackages/agent-runtime/agent/recordings/defaultClient_631904893/\\npackages/agent-runtime/agent/recordings/document-code_965949506/\\npackages/agent-runtime/agent/recordings/edit_1541920145/\\npackages/agent-runtime/agent/recordings/enterpriseClient_3965582033/\\npackages/agent-runtime/agent/recordings/enterpriseMainBranchClient_759014996/\\npackages/agent-runtime/agent/recordings/fix_3001320056/\\npackages/agent-runtime/agent/recordings/generate-unit-test_1969275822/\\npackages/agent-runtime/agent/recordings/rateLimitedClient_2043004676/\\npackages/agent-runtime/agent/recordings/unauthed_2245427793/\\npackages/agent-runtime/agent/recordings/vscode_525627603/\\npackages/agent-runtime/agent/scripts/\\npackages/agent-runtime/agent/src/\\npackages/agent-runtime/agent/src/__snapshots__/\\npackages/agent-runtime/agent/src/__tests__/\\npackages/agent-runtime/agent/src/__tests__/auth/\\npackages/agent-runtime/agent/src/__tests__/autocomplete/\\npackages/agent-runtime/agent/src/__tests__/autocomplete/src/\\npackages/agent-runtime/agent/src/__tests__/chat-response-quality/\\npackages/agent-runtime/agent/src/__tests__/chat-response-quality/grpc/\\npackages/agent-runtime/agent/src/__tests__/chat-response-quality/grpc/chunk/\\npackages/agent-runtime/agent/src/__tests__/chat-response-quality/shards/\\npackages/agent-runtime/agent/src/__tests__/cody-cli-chat/\\npackages/agent-runtime/agent/src/__tests__/custom-commands/\\npackages/agent-runtime/agent/src/__tests__/custom-commands/src/\\npackages/agent-runtime/agent/src/__tests__/document-code/\\npackages/agent-runtime/agent/src/__tests__/document-code/src/\\npackages/agent-runtime/agent/src/__tests__/edit-code/\\npackages/agent-runtime/agent/src/__tests__/edit-code/src/\\npackages/agent-runtime/agent/src/__tests__/example-ts/\\npackages/agent-runtime/agent/src/__tests__/example-ts/src/\\npackages/agent-runtime/agent/src/__tests__/fix/\\npackages/agent-runtime/agent/src/__tests__/fix/src/\\npackages/agent-runtime/agent/src/__tests__/generate-unit-test/\\npackages/agent-runtime/agent/src/__tests__/generate-unit-test/src/\\npackages/agent-runtime/agent/src/__tests__/graph-test/\\npackages/agent-runtime/agent/src/__tests__/graph-test/src/\\npackages/agent-runtime/agent/src/cli/\\npackages/agent-runtime/agent/src/cli/__snapshots__/\\npackages/agent-runtime/agent/src/cli/command-auth/\\npackages/agent-runtime/agent/src/cli/command-bench/\\npackages/agent-runtime/agent/src/cli/command-bench/__snapshots__/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/cpp/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/csharp/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/dart/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/go/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/java/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/javascript/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/php/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/python/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/tsx/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/typescript/\\n\\n(File list truncated. Use list_files on specific subdirectories if you need to explore further.)\\n</environment_details>\",\"tokensIn\":16216,\"tokensOut\":115,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1750859646617, "type": "say", "say": "text", "text": "根据用户的", "partial": true}, {"ts": 1750859646679, "type": "say", "say": "checkpoint_saved", "text": "28ee26971995a902f1dde8b455d66e87d94e4a81", "checkpoint": {"isFirst": true, "from": "28ee26971995a902f1dde8b455d66e87d94e4a81", "to": "28ee26971995a902f1dde8b455d66e87d94e4a81"}}, {"ts": 1750859646705, "type": "say", "say": "text", "text": "根据用户的要求，我需要帮助删除指定的几个git commit。这个任务需要使用git rebase命令来完成。我需要使用execute_command工具来执行git命令。\n\n首先需要执行git log来确认这些commit的位置和关系。", "partial": false}, {"ts": 1750859648570, "type": "ask", "ask": "command", "text": "git log --oneline", "partial": false, "isProtected": false}, {"ts": 1750859649044, "type": "ask", "ask": "command_output", "text": ""}, {"ts": 1750859649047, "type": "ask", "ask": "command_output", "text": ""}, {"ts": 1750859649051, "type": "ask", "ask": "command_output", "text": ""}, {"ts": 1750859649055, "type": "say", "say": "command_output", "text": "81345362 (HEAD -> release/release-vs-3.0.0-0620, origin/release/release-vs-3.0.0-0620) feat(agent): 恢复ProgressIndicator组件的使用\n829cce59 Merge branch 'feature/multi-agent-0430' into release/release-vs-3.0.0-0620\n90a1dd40 (origin/feature/multi-agent-0430) Merge branch 'feature/multi-agent-0430' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into feature/multi-agent-0430\ne47f7484 feat(代码索引): 添加基于Qdrant的语义代码搜索功能\n90cc2b6d Merge branch 'release/ide-agent-0.7.1-bak' into release/release-vs-3.0.0-0620\n82245bb8 Merge branch 'release/release-vs-3.0.0-0620-bak' into release/release-vs-3.0.0-0620\ne1da20c8 Merge branch 'release/ide-agent-0.7.1-bak' into release/release-vs-3.0.0-0620\nd394e094 feat(dialog): 添加自定义模式切换功能\n0a0cd08b refactor(智能体管理): 重构组件结构并拆分子组件\nb42ef5aa (origin/release/ide-agent-0.7.1-bak, release/ide-agent-0.7.1-bak) style(BrowserSessionRow): 优化浏览器思考中状态的图片展示样式\n04c06969 fix(api): 修复对网关返回数据处理缺陷\na6e1b846 refactor(settings): mcp、智能体设置页代码优化\n7f76a8d1 Merge branch 'release/ide-agent-0.7.1-bak' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1-bak\nfed9f329 (origin/feature/ide-agent-0.7.1-joycode) refactor(coder): 品牌名更改，将命令配置项前缀从JoyCoder更改为JoyCode\n6595b2ea Merge branch 'release/release-vs-3.0.0-0620' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/release-vs-3.0.0-0620\nda657c8f (origin/release/ide-agent-0.7.1-bak-joycoder, release/ide-agent-0.7.1-bak-joycoder) feat(ChatRow): 优化子任务显示，添加思考中动画\n47aba378 Merge branch 'release/ide-agent-0.7.1' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1\n503e2adb refactor(coder): 将命令行配置项前缀从JoyCode更改为JoyCoder\n4e667409 refactor(ContextMenu): 简化菜单项文本内容\n2fc58ca2 Merge branch 'release/ide-agent-0.7.1' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1\n57121f8b refactor(coder): 将命令行配置项前缀从JoyCode更改为JoyCoder\n5dd4220d refactor(ContextMenu): 简化菜单项文本内容\nee10edf2 Merge branch 'release/release-vs-3.0.0-0620' into release/ide-agent-0.7.1\n17ee14c1 feat(agent-setting): 添加自动批准功能并优化代码结构\n11973445 feat(动画): 引入Lottie动画组件引入json资源替换GIF加载动画\n8484ef56 fix(agent): 优化模式切换展示，优先展示智能体名称\n0360d42d feat(McpInstalledView): 在mcp设置中使用Lottie动画替换GIF等待图标\n87bf5abf fix(agent): 修复codebase和use-definition-names工具问题\n2bc123a9 feat(代码索引): 添加基于Qdrant的语义代码搜索功能\nd3385ae3 refactor(coder): 移除多余的日志输出\n08e65eab refactor(coder): 将命令行JoyCoder.config.searchApiKey从JoyCoder改为JoyCode\ndcf447b8 refactor(coder): 将命令行JoyCode.config.duplicateCodeDetection从JoyCoder改为JoyCode\n4df18a64 refactor(coder): 将命令行JoyCoder.config.customHelper从JoyCoder改为JoyCode\n846252c7 refactor(coder): 将命令行JoyCoder.config.cssModuleTransformIgnorePrefix从JoyCoder改为JoyCode\nf77387c4 refactor(coder): 将命令行JoyCode.config.chatKnowledge.options从JoyCoder改为JoyCode\n894a3a6c refactor(coder): 将命令行JoyCode.switch.AgentView从JoyCoder改为JoyCode\n23732fc4 refactor(codebase): 注释掉CodebaseManager中的日志输出代码\nd078cd6e refactor(coder): 将命令行JoyCode.enableCodebase从JoyCoder改为JoyCode\nf62a907f refactor(coder): 将命令行JoyCode.enableCheckpoints从JoyCoder改为JoyCode\n605909a6 refactor(coder): 将命令行JoyCode.enableBrowserTool从JoyCoder改为JoyCode\n522fc002 refactor(coder): 将命令行JoyCode.config.workSpaceId从JoyCoder改为JoyCode\n485b67f7 refactor(coder): 将命令行JoyCode.config.translationTokens从JoyCoder改为JoyCode\n003564b3 refactor(coder): 将命令行JoyCode.config.translationRows从JoyCoder改为JoyCode\n43a5d6a5 refactor(coder): 将命令行JoyCode.config.test-llm-list从JoyCoder改为JoyCode\n1652c496 refactor(coder): 将命令行JoyCode.config.personalSnippets从JoyCoder改为JoyCode\n39a0b277 refactor(coder): 将命令行JoyCode.config.onlyTranslationFlutter3从JoyCoder改为JoyCode\n50119a03 Merge branch 'release/ide-agent-0.7.1' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1\n80a91b4f refactor(coder): 将命令行JoyCode.config.completionsRejectTimes从JoyCoder改为JoyCode\nf26937dc Merge branch 'release/ide-agent-0.7.1' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1\n14dcc47f refactor(mcp): adjust gap in property header and remove unused manualConfigJson state\n07012dee refactor(coder): 将命令行JoyCode.config.codeTranslatePrompt从JoyCoder改为JoyCode\nf3c6736b (feature/multi-agent-0430) fix(行间菜单): 优化行间菜单未登录时处理方式\n8d4ed3ca refactor(coder): 将命令行JoyCode.config.codeReview.otherRules从JoyCoder改为JoyCode\nee1a6b1f refactor(coder): 将命令行JoyCode.config.snippets.switch从JoyCoder改为JoyCode\n4a032b6d refactor(coder): 将命令行JoyCode.config.shenYi.commit从JoyCoder改为JoyCode\n962d0975 refactor(coder): 将命令行JoyCode.config.codeReview.immediate从JoyCoder改为JoyCode\n372801de refactor(coder): 将命令行JoyCode.config.codeReview.fileNumber从JoyCoder改为JoyCode\n2f45f5a2 refactor(coder): 将命令行JoyCode.config.codeReview.errorLine从JoyCoder改为JoyCode\n7cc1694c refactor(coder): 将命令行JoyCode.config.codeReview.commitMessageStyle从JoyCoder改为JoyCode\n328b54f9 refactor(coder): 将命令行JoyCode.config.codeReview.commit从JoyCoder改为JoyCode\n467bf823 refactor(coder): 将命令行JoyCode.config.codeLens-row-menus从JoyCoder改为JoyCode\n7dcda73a refactor(coder): 将命令行JoyCode.config.codeCompletionsMoreContext从JoyCoder改为JoyCode\n94c2486c refactor(coder): 将命令行JoyCode.config.codeCompletionsMaxTimes从JoyCoder改为JoyCode\n3fb8733e refactor(coder): 将命令行JoyCode.config.codeCompletionsMaxLines从JoyCoder改为JoyCode\nb88ed06c refactor(coder): 将命令行JoyCode.config.codeCompletionsGenTask从JoyCoder改为JoyCode\n6d7f8871 refactor(coder): 将命令行JoyCode.config.codeCompletionsFormat从JoyCoder改为JoyCode\n14a9df95 Merge branch 'release/ide-agent-0.7.1' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1\na182645e refactor(coder): 将命令行JoyCode.config.chatModel从JoyCoder改为JoyCode\n8bfe602c Merge branch 'release/ide-agent-0.7.1' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1\n4bcbfdc5 Merge branch 'release/ide-agent-0.7.1' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1\ne7ef074a refactor(coder): 将命令行JoyCode.config.autoActivateOnNewWindow从JoyCoder改为JoyCode\n498954f0 feat(mcp): add tips for JSON configuration in McpModal and clear services on quick config toggle\n4cd64842 refactor(coder): 将命令行JoyCode.coder.selectFileNubmer从JoyCoder改为JoyCode\n10dd0869 refactor(coder): 将命令行扩展唯一标识符及JoyCode.coder.maxReadFileLine命令JoyCoder品牌名称更改为JoyCode\n9a019108 Merge branch 'release/ide-agent-0.7.0' into release/ide-agent-0.7.1\na3852486 Merge branch 'release/ide-agent-0.7.1' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1\nd988e9a5 feat(ui): 将产品名称从JoyCoder统一变更为JoyCode\n496a6c86 (origin/release/ide-agent-0.7.0, release/ide-agent-0.7.0) feat(agent-management): 添加重置Modal参数功能并优化智能体创建流程\n6c539182 fix(McpMarketView): fix MCP list fetching and reset services on quick config toggle\n7081d98a fix(mcp): update error message for missing mcp.json and modify instruction text for adding services\n08746a64 fix(web-agent): 输入框上传图片悬停样式hover样式优化\n8a814705 fix(chat/ContextMenu): 修改聊天输入框rules类型图标\n7372f76c fix(webview): 优化消息处理逻辑避免新任务时重复创建任务\n005f7f04 fix(agent): 优化模式切换展示，优先展示智能体名称\na75b9365 feat(utils): 添加安全解码URI功能并统一处理中文路径\n00a2882f Merge branch 'ide-mcp-market20250618' into release/ide-agent-0.7.1\n1547681c Merge branch release/ide-agent-0.7.0 into release/release-vs-3.0.0-0620-bak\n0622cda6 chore(vscode-IDE): 更新版本号至2.9.14\n2ed6cc83 fix(样式调整): 修复设置页面在小分辨率下无法滚动问题和滚动条样式\n6363e53c Merge branch 'feature/multi-agent-0430' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into feature/multi-agent-0430\n638e9746 feat(agent): 添加Excel文件内容提取功能并引入exceljs依赖\ncdd2c1df fix(McpModal): 移除调试用的console.log语句\nf2faf30e fix(McpHub): 修复远程环境下MCP服务器配置读取逻辑\n7bfa0694 fix(agent-driven): 修复远程环境下路径处理问题并优化代码格式\n3bc3532f Merge branch 'release/ide-agent-0.7.0' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.0\n2496fd49 fix(MessageToolbar): 远程环境下隐藏回滚按钮\n818f6c35 fix(agent-driven): 优化agentId排序逻辑\n0acdb244 feat(AgentManagement): 根据source字段动态显示MD编辑链接并调整作用范围默认值\n469fb56c fix(AgentManagement): 修复表单字段空值校验问题\n42ecc2c8 feat(agent-management): 为智能体模式添加描述字段并更新展示逻辑\n\n[...1285 lines omitted...]\n\n9093cc82 fix(codeshow): 1024节引导语，补充监控，必须输入10行代码参加\n3dc10092 (tag: v1.0.2) chore(release): 1.0.2\n935bf7b3 fix(codeshow): 1024节引导语，必须输入10行代码参加\n2c4f74b1 (tag: v1.0.1) chore(release): 1.0.1\n6c8e8527 fix(codeshow): 补充1024节打卡描述哦\n5b714ee4 Merge request #2 from JoyCoder:feature_joycoder_v1 on 2023-10-13 17:35:01\n0d9d851c fix(codeshow): 替换为线上的打卡接口\n82ade60f fix(codeshow): 补充开关，修复添加打卡内容时，主动关掉弹窗\n2cf67861 (tag: v1.0.0) chore(release): 1.0.0\na3d8a899 Merge branch 'feature_joycoder_v1'\n6423b760 docs(docs): 更新changelog\nd15e0213 chore(npm script): 发布命令优化\n517ddb1d feat(codeshow): 提示词模板支持可配置\nb50ec932 feat(品牌融合): 优化新老插件切换体验\n919f4ec7 fix(codeshow): 成功登录后继续打卡流程，优化黑色系\ned109b9b feat(codeshow): 替换为京东红色系\nc1d448ac feat(品牌融合): 首次安装时自动同步HiBox配置并卸载老插件\n7ccc5c08 fix(codeshow): 打卡接口支持远程下发\n062e6635 feat(icon): 侧边图标替换为outline形式\n10b193bb feat(codeshow): 语言读取优化、可配置化活动\n84808647 feat(web): webview面板中类名替换为joycoderfe\n48adef2f feat(shendeng): 支持1024神灯姐打卡活动\ncee2bafc feat(品牌融合): 插件更名为JoyCoder-前端专业版 & 无用功能删减 & 结构及文档优化\nf09bae88 feat(H5调试): 优化视图样式布局\n44284303 feat(H5调试): 增加视图缩放最大尺寸限制逻辑\n4c58ae77 feat(H5调试): 增加视图缩放逻辑\n6f0092b7 fix(H5调试): 修复模拟设备尺寸异常问题\nfd907145 Merge branch 'master' of coding.jd.com:HiBox/HiBox into feature_browse_lite\nd0c9cff6 feat(H5调试): 完善调试映射页面\nee888633 (tag: v2.12.0) chore(release): 2.12.0\ncae36ef0 fix(auto import): 修复输入if+空格后误触发import语句提示问题\na866bdaf fix(auto import): 优化import语句补全体验\n22fcd4da fix(快速开始): 优化快速创建体验:避免重复操作\nfde62a07 feat(taro): 增加Taro组件,API,Hooks,条件编译相关snippets\n642daf69 feat(H5调试): 删除冗余代码\ncf4246f8 feat(H5调试): 增加调试映射页面\n6b9eab53 feat(H5调试): 增加无头浏览器逻辑\nb3218359 feat(H5调试): 初始化内置H5开发调试模块入口\n56400365 fix(login): 解决登录页面偶现503问题\n8c90d6e0 (tag: v2.11.5) chore(release): 2.11.5\nf075fa5e fix(doc): 补充开发贡献文档\n1e14d761 fix(doc): 补充开发贡献文档\n612d185e fix(chatgpt): 聊天窗中直接调用handleAskChatGPT，无需进行code拼接\ndec77101 fix(chatgpt): 自定义key的场景，走默认通道去除频限\nac98fe3c (tag: v2.11.4) chore(release): 2.11.4\n1e5af1e6 docs(readme): 增加changelog入口\n5998dc9f chore(install): 去除npm包安装方式\n2b042c93 fix(quickjump): 修复umpkey跳kibana问题\n46dbcf48 (tag: v2.11.3) chore(release): 2.11.3\n166b6b82 fix(cursor): 修复修改代码时缩进不一致问题\n29f6a91d fix(cursor): 修复移除结尾换行时产生的bug\n217cd40b fix(cursor): 将用户选择的代码片段约束为整行\n41e54f81 fix(cursor): 修改代码完成后自动打开diff\n14306613 (tag: v2.11.2) chore(release): 2.11.2\n81cb3d29 chore(打包): bundle大小限制调整\n9b5017a0 fix(chatgpt): 登录态有效期延长至30天\n061a321e fix(chatgpt): cursor编码体验优化&小灯泡增加注释功能\nb77b1619 fix(chatgpt): cursor编码支持从小灯泡唤起\n2e8a5222 fix(chatgpt): 优化悬浮按钮样式\na4b58eb0 fix(chatgpt): 新增工具栏\n31a03a44 (tag: v2.11.1) chore(release): 2.11.1\n405765d9 fix(chatgpt): 修复你好时，重试的列表判断错误的问题\n8559de2a (tag: v2.11.0) chore(release): 2.11.0\n0357d4c6 feat(chatgpt): 优化你好的登录体验\need7f588 fix(chatgpt): 优化GPT4用尽时的交互体验\n7be03bee feat(chatgpt): 频限支持GPT4模型\n26e515db fix(login): 优化登录处理，初始化注册router\n65410092 (tag: v2.10.1) chore(release): 2.10.1\n89168417 fix(chatgpt): 主题css文件cdn加载\n81f7716b fix(chatgpt): 支持主题色切换\n63725964 (tag: v2.10.0) chore(release): 2.10.0\n10e98a90 fix(chatgpt): 收藏问题修复及样式优化\n03da801e fix(chatgpt): 优化提示词收藏状态的调用，减少依赖\nd3e03c8c feat(chatgpt): 支持复制和重试功能\n7aaee308 fix(chatgpt-web): 代码类型完善\nc0981cdf (tag: v2.9.2) chore(release): 2.9.2\n725f81af fix: 物料导入修复白屏问题 (#69)\n4aaafde0 (tag: v2.9.1) chore(release): 2.9.1\n1468d1b1 fix(chatgpt): 修复不存在profile报错的问题\n7ee71402 (tag: v2.9.0) chore(release): 2.9.0\na412f8b6 fix(chatgpt): 补充自定义指令新增跳转\nb4f9811a fix(chatgpt): 避免主按钮被覆盖\nedac6047 feat(chatgpt): 新增个性化指令特性，配置项UI交互优化\n456a8508 Merge remote-tracking branch 'origin/master' into feature_chatgpt_custom_instructions\n9a054e78 feat(chatgpt): 支持提示词收藏&快速检索历史提示词\nf6c06d66 fix(物料导入): 首次加载时展示bug\n97590084 refactor(物料导入): 代码优化\n8d21838e feat(物料导入): 支持配置二级Tab及版本选择\na18626de (tag: v2.8.1) chore(release): 2.8.1\nf5680865 fix(chatgpt): 替换知识库链接，默认万能模板更新\naa0d917f (tag: v2.8.0) chore(release): 2.8.0\n857156a3 feat(chatgpt): 支持insiders版本vscode登录\n86dabf78 fix(chatgpt): 登录欢迎弹窗展示1.5秒后自动关闭\nb6f4ed31 feat(chatgpt): 支持调用集团ChatGPT网关\nadce42df fix: 修复snippets合并时同名key覆盖问题\n51cf0bb1 fix: 修复ChatGPT输出换行异常问题\n9d914d6e (tag: v2.7.0) chore(release): 2.7.0\nad256dc9 chore(publish): 发布hibox时同步发布hibox-installer\n13d75b5f Merge request #54 from HiBox:feature_chatgpt_knowledge on 2023-07-07 17:19:24\ndd49ec2d fix(chatgpt): 修复已存在webview时重新打开的体验问题\nc37a49bb feat(chatgpt): 通过你好判断登录态和GPT服务可用性\nacdbe7e6 feat(chatgpt): 支持私域向量知识库\n06518602 feat(快速开始): 支持Taro项目/页面/页面组件\ne92573ca (tag: v2.6.0) chore(release): 2.6.0\nff9e5052 fix(chatgpt): 优化respMaxTokens,缓解上文丢失情况\nca766408 fix(chatgpt): 图标更换，quickpick优化\nc1084172 feat(chatgpt): 支持用户选择不同模型\n2a780bff (tag: v2.5.3) chore(release): 2.5.3\n76989817 fix(快速开始): 修复未配置formItemConf，不触发兜底bug；兜底逻辑剔除leo类型\ne2900617 (tag: v2.5.2) chore(release): 2.5.2\n0bb0ee54 fix(chatgpt): 修复highlight识别```k出现异常报错白屏的问题\naf6b834c (tag: v2.5.1) chore(release): 2.5.1\ne6bcff25 fix(chatgpt): 优化异常情况提示\n86dac5c3 fix(快速开始): 如果远程没有配置name，默认给一个\n348df732 fix(快速开始): 修复不配置名称报错，兼容templateDirName以/开头的写法\n949b792a (tag: v2.5.0) chore(release): 2.5.0\n23a50787 feat(cli&quickstart): 适配Leo：支持Leo快捷命令行和常用Leo模板快速创建 (#34)\nc8412dfc fix(chatgpt): 调整respMaxTokens入参,尽可能确保完整的输出\n35aed94f (tag: v2.4.3) chore(release): 2.4.3\nfb42eec5 fix(chatgpt): 增加chatgpt使用要求\n06921043 fix(update): 解决windows下自动更新问题,优化自动更新体验\nff8ca140 fix(chatgpt): 初始化对话框时自动回复,节省token\n0bd26678 (tag: v2.4.2) chore(release): 2.4.2\n7bc4f69b fix(cursor): 暂时去掉cursor快捷入口功能\nbc49b0c8 (tag: v2.4.1) chore(release): 2.4.1\nb6fafcae fix(cursor): 修复编码失焦问题\n78b9bef8 (tag: v2.4.0) chore(release): 2.4.0\nc5bcf0cb chore(release): 优化自动更新体验\n41193a53 chore(release): 拆分发布命令,以便分步验证\n24e64cc3 fix(materialimport): 物料导入体验优化（支持NutUI）\n88dfc12b chore(report): 完善业务&错误监控\nc7057244 chore(commit): 强制要求commit scope\n54abcc2c fix(chatgpt): 优化交互体验及相关代码\n502b4535 chore: 增加调试时忽略其他插件参数\n9fd57f28 fix(chatgpt): 修复windows下输入换行问题\n02be51d3 fix(chatgpt): 切换接口逻辑优化\n10f541f1 feat(chatgpt): chatgpt服务切换为java版本\nca6335c9 feat: 支持cursor类似的交互能力\n6c3eea31 (tag: v2.3.0) chore(release): 2.3.0\n5ffd3074 Merge request #28 from HiBox:feature_chatgpt_opti on 2023-04-18 20:53:07\nebf454b0 fix(chatgpt): 优化chatgpt交互区域布局\n65cba1bb fix(chatgpt): 对话框样式优化,用更少区域承载更多内容\n3599ddd9 feat: chatgpt代码块增加插入和复制操作\n5268036c fix(chatgpt): 优化输入框回车逻辑\na8e6b29a fix(chatgpt): 优化请求超时、频限等容灾体验\na7aed03f feat: 增加chatgpt快捷入口\n906a9132 fix(chatgpt): 优化代码高亮及输出内容自动滚动体验\n01868a0a feat: 支持ERP登录态打通\n2fa68b45 (tag: v2.2.0) chore(release): 2.2.0\n0081eabd fix: chatgpt文案优化\ne5d769f8 feat: chatgpt增加提前停止功能，支持配置私有key\na7a6bf22 feat: 新增HTML转CSS/SCSS/LESS功能\n4beae970 feat: 新增无用export代码扫描和无用文件扫描功能\na6912630 refactor: 重命名代码检测模块\n67ba9880 feat: 支持插件市场一键安装\nfb896463 (tag: v2.1.2) chore(release): 2.1.2\nb18e2459 Merge branch 'feature_2.0_opti'\n489ec7ae fix: 优化自动更新逻辑\nc347e50f fix: 优化chatgpt体验:支持操作可配置,优化入口\nb24e6f04 fix: 修复物料导入时只能插入第一行代码的问题\neba97b3f Merge branch 'master' into feature_2.0_opti\n36b9d010 docs: 增加接口说明\n8dd6aa73 docs: 修改联系我们地址\n0e547eba Merge request #21 into master on 2023-02-10 09:54:20\nbe28674d docs: 贡献文档优化\n4e85e8e6 (tag: v2.1.1) chore(release): 2.1.1\n202163a3 fix: 修复chatgpt无法生成测试用例问题\n9b5a46dc fix: 修复自动更新后需手动重载问题\n669fc07e (tag: v2.1.0) chore(release): 2.1.0\ndeb792cd chore: 发布问题处理\nb39aec60 feat(chatgpt): 切换到远程代理服务\na5b07614 (tag: v2.0.0) chore(release): 2.0.0\n6f438c52 Merge branch 'feature_open_use'\n763f71e0 fix: 增加chatgpt监控&上报\n00dbb42c fix: 补充chatgpt问答模式\n8d863fa8 Merge branch 'feature_open_use' of coding.jd.com:HiBox/HiBox into feature_open_use\n93fd3d1b fix: chatgpt支持command+回车换行\n1ecc9baa fix: chatgpt样式优化\nc815a0c3 feat:  chatgpt异常交互提醒，去除日志提升性能\n88e503cd fix: 增加私有chatGPTToken配置\n615a4e5b Merge branch 'feature_open_use' of coding.jd.com:HiBox/HiBox into feature_open_use\n1539f744 fix: 更新首次安装上报\ne9dfaa2c feat:  增加chagpt\na27de21f feat:  优化远程命令行解析方式，调整顺序\n93ad82c8 fix: 升级axios,解决header content报错\n56954442 fix: 更新成功时增加公告提示&更新失败引导重试\n91b76aa5 fix: 修复快捷导航刷新功能\ndcd3d80f fix: 修复无数据是展示异常问题\n11ab12ed feat: 搜索增加快捷键\n6c8dbbd1 fix: windows下自测及问题修复\n35579a40 fix: 上报信息完善\n57f661e2 chore: 发版优化\nceaff45e fix: 自测体验问题修复\nfac16fc2 feat: 命令行执行拆分本地&远程，修复beta发布问题\n129f922e fix: 更换配置端文档链接\n8a0754ec fix: 工作区切换改为远程下发\n33498798 fix: 完善定制功能开关逻辑\n2e74ff44 Merge branch 'feature_open_use' of coding.jd.com:HiBox/HiBox into feature_open_use\n2182cb37 chore: 更新发布脚本\n8cbe2ed4 feat: 插件配置数据切换至水滴数据源\na58247a5 fix: 更换模版仓库为ssh地址\nd4fced01 fix: 编辑区隐藏快捷命令和快速创建\na69b56fe chore: 编译速度&WEB页首屏速度优化\n84563504 chore: 编译速度和体积优化\ne812c372 fix: 类型问题修复\n5c97b38d feat: 恢复插件包功能,只附带安装轻量且较通用的插件\n1e9b1f08 fix: 修复悬浮npm包功能包名识别异常问题\nf15e0d09 feat: 快速开始支持第三方仓库及部分代码重构\ncb4b3359 fix: 提前插件激活时机\n7a283bf2 fix: quick-console体验优化&图片上传接口监控&快速开始样式优化\n0232d41e feat: 新增快速切换工作区&主接口可用率上报等\nb090feb2 feat: 新增快速添加console代码功能\n6bc0af6c refactor: 划分悬浮提示功能\n82bb90d7 refactor: 重构快速跳转模块\n75ddeef0 chore: 修正package.json\n833249a2 feat: 支持远程配置来开关非通用性的功能\nca18ae03 feat: 命令行工厂重构，支持远程配置化\nc7da5434 chore: 调整项目结构为基于pnpm workspace的monorepo\n2543a52a chore: 使用pnpm作为包管理器\ned813ebf chore: 构建优化\n9c798ea8 Merge branch 'master' into feature_open_use\n0cd69c16 chore: 更改安装包位置\n2abbc8af chore: 更改换行符避免出现git差异\n60b9c893 chore: 提交插件安装包以供用户首次下载\n0981f4c5 chore(release): 1.4.0\nc1339897 fix: 解决download库导致的打包失败问题\n9d7243fe Merge branch 'feature_open_use'\nf176bf50 feat: 支持插件自动安装更新\n01396e3c fix: 代码结构优化\n2d5073a8 fix: 本地配置优化\n3117066a docs: 更新贡献文档\n59bcc7fc (tag: v1.3.1) chore(release): 1.3.1\n6c97e448 fix: 修复自定义编译失败问题\n2174e486 chore(release): 1.3.0\n4139cc18 feat: 增加git发布管理后台功能\n25358af5 refactor: 重写获取私有配置方法\n0c9bf946 fix: 修复遍历git根目录问题\nacda4cf4 feat: css中插入图片时增加宽高信息\n2cb5ad9b chore(release): 1.2.0\n3b3fb695 feat(cli): 代码优化，命令长度为0 的异常流判断统一处理\n133012e2 feat(cli): 增加不限制目录的legos命令\nad3b6d38 fix(cli): 调试代码误提交删除\n736919a2 (tag: v1.1.1) chore(release): 1.1.1\n447cb234 fix: 修复umpkey查询bizid失败问题(更换接口)\na5e5e566 chore(release): 1.1.0\n736aa515 Merge request #10 from HiBox:feature_cli_factory\nb2a927eb refactor(util): cli中的utils函数提升\nfc36aa17 feat(cli): 提示文案调整\n5e21ac9c feat(cli): 编译taro京喜小程序，判断wxapp是否有编译产物来调整timeout\n52e6c365 feat(cli): 根目录提示文案优化\nc1b8923c fix(cli): 调整getTargetPath中isTarget优先级提前\n9025443c fix(cli): 版本号增加字符串问题修复\n13ce2903 feat(cli): 更新beta的基线改为npm verisons中最后1个\nc225ddfb fix(cli): 发布失败提示优化\n05b7b074 feat(cli): 支持任意仓库发布beta版npm包\nacaa5873 feat(插件包): 下线插件包特性,只在使用文档推荐,不做强制安装\n87633bd9 Merge branch 'master' of https://coding.jd.com/HiBox/HiBox\n78c3ad97 feat(cli): 发布beta包时远程版本选择beta和latest中的较大值\n5645e9b9 fix(cli): 快捷命令行taro编译京喜小程序\nc649a2e6 fix(cli): 拼写错误&支持配置timeout等待时间\na1c4ed26 Merge remote-tracking branch 'origin/master' into feature_cli_factory\n2cf2bc89 fix(quick_jump): 修复文档快速跳转链接被覆盖问题\n494993f6 chore: 删除无用文件\nbb40a55b chore(release): 1.0.0\n59dda0fd fix: 修复hover重复上报问题\nf4425ce3 chore: 补充依赖包\n2b36bd60 fix: 修复数据上报无效问题\nf69197c2 fix: 重复检测支持配置minTokens\nf7faa233 feat: treeview展示快速创建\n6ef950e0 chore: 更换logo\ne47f2a4d chore: 忽略模板目录\ncc527966 fix: 调整插件包\n5b5c8634 Merge branch 'master' into feature_quick_create\n4f54e3fb feat(快速创建): 模板列表样式调整。\nd66e6210 Merge branch 'master' into feature_quick_create\n620b1f8c feat(快速创建): 处理写入路径时减少变动。\n4b2e8595 feat(cli): 关闭命令行后commandBar隐藏\n32155dd9 Merge branch 'master' into feature_command_custom_fix\nb7f9214f chore: 忽略所有的声明文件（ *.d.ts）的类型检查\n5e4d40d3 fix(代码重复检测): 修复提交ts类型报错\n4e193189 feat(cli): 增加关闭所有命令行\n9e04caaa fix(style): 统一空格缩进\nf3450ebb feat(cli): 自定义配置命令行统一执行器\nfeba6f6e Merge branch 'feature_cli_factory' into feature_command_custom_fix\na73432b7 refactor(cli): 按照统一规范实现快捷命令行\n761a9421 feat: 工作目录支持相对、绝对、项目根目录\n5f4ee9f3 feat:自定义编译代码优化\ne872fa2a fix(cli): 修复pages前面多/导致部分cli工具不识别问题\nf119cc85 feat: 统一message、清理多余console。\n005c6a1b fix: 修改路径问题。\n50afeabf fix: 处理本地运行时，报id错误问题。\na2c45657 chore: 删除并忽略没必要的文件。\nf72b70d9 Merge branch 'master' into feature_quick_create\n3fa36f15 feat(快速创建): 新增页面增加路径写入。\n41856eea Merge branch 'master' into feat_duplicateCode_detectiona\n7842c25b feat(代码重复检查): 提示文案修改为中文\n9b178245 fix(代码重复检查): 修复代码诊断展示数量不准确问题\n5b98ada0 feat(代码重复检查): 查重参数调整\n66cc5b2b feat(代码重复检查): 代码路径调整\n512f0109 feat(cli): 递归往父级查找目标文件的逻辑收敛\n10d880d0 feat(cli): 文件窗口内右键也展示快捷命令行\ne7b20e51 feat: 代码重复检查支持选中多文件\n5d12b653 feat(cli): 使用shared/logger标准化展示消息\ndefa7696 fix(cli): 冲突处理\n2f393cc5 chore(style): package.json改tab为空格\n8b4e14a9 fix(cli): 冲突处理\n65d827e4 fix(cli): 冲突处理\n36d53785 fix(cli): 冲突处理\n0ab2875b Merge remote-tracking branch 'origin/master' into feature_cli_factory\n6ef89767 feat(快速创建): 埋点。\ndd158f78 feat(cli): 增加使用次数上报\n3f4039fd feat(cli): 支持jxapp单页发布&基础库发beta版本\nd9844e80 feat: 自定义命令编译支持多选\n1291515d fix(快速创建): 读取模板json配置解析问题处理。\n29ff8284 feat(快速创建): 创建时替换文本。\ncf783a13 chore: 移动目录。\ne7c4a021 feat: 新增代码重复检查能力\n8f364125 feat: 更新自定义编译\ncf7b5949 Merge branch 'master' into feature_quick_create\nfbb88656 feat: 新增快速创建插件。\nb2ce21a1 feat: 调整打开ViewLoader里的viewColumn配置判断逻辑。\nfdaf654d feat: 新增自定义配置命令\n1d8b3b46 fix: 修复自动补全标签新增的类名没有定义和悬浮提示问题\nd54be56a fix: 补充上报\na58309db refactor: 部分菜单项整合\ncbae5653 feat: 新增悬浮ip时直达webadmin能力\n7572b11c feat: 新增推荐插件\n74b9c8d9 feat: 新增git辅助功能\n1c1511a4 fix: 获取项目根路径方法问题修复\n6494b815 feat(cli): jxapp项目H5单页开发能力\nc15d67fd Merge branch 'master' into feature_quick_create\n7c0ed789 feat: 新增右击页面目录查看该页面badjs功能\nb8b4d6ac Merge branch 'master' into feature_quick_create\n89c2d0d0 (tag: v0.0.3) chore(release): 0.0.3\nc9344564 feat: 增加插件包能力,安装hibox时自动安装其他推荐插件\n2345c141 Merge branch 'master' into feature_JHooks_create\n7eb33eac chore: 更换activitybar图标\n7619e052 fix: 补充数据上报\n95e5cbc9 fix: 物料导入模块跳转链接体验优化\na6743dfb Merge branch 'master' of https://coding.jd.com/HiBox/HiBox\na2aaa85d feat: 新增快速搜索(组件文档、接口查询、kibana 查询) (#1)\n30e84ab3 fix: 快捷导航改为craft配置\n7ce1ba9b fix: 修复css中有注释时css definition失效问题\n0232885c fix: 优化物料导入编辑框失焦后无法插入代码体验\nc1c26c0f feat: 优化标签自动补全,并在补全时自动添加css代码\n24e430b3 feat: 增加css 定义功能(css预览及跳转定位)\n6372c11f feat: 增加数据上报\n20412813 feat: 增加快捷操作树视图\n68e23cc2 feat: 增加快捷导航树视图\n8904efca feat: 树视图增加问题反馈等入口\n32f07aee feat: snippet支持远程及本地配置\naee1405b chore: snippets.json改为动态生成\n7558b2ca fix: 物料导入时重复安装npm问题修复\n4c798c49 refactor: 重构获取配置模块\nb9e37715 docs: 例会文档迁移至joyspace\ne6821cb3 docs: 例会文档整理\n907b842e chore: 更换包版本\nccd1efcc Merge branch 'master' of https://coding.jd.com/HiBox/HiBox\n06281f25 docs: 更新例会文档\nd8bcf440 feat: 增加css转为cssmodule能力\ne7cdcb33 feat: 增加标签补全为cssmodule能力\nb7f7315d feat: 增加import代码补全\n3c225822 feat: 增加snippet代码片段能力\n3a8d8e42 feat: 物料导入体验优化\n6a0537fd docs: 例会文档\n97ba0f49 docs: 例会文档\n0fee2a57 docs: 例会文档\nce114038 feat: 新增物料导入能力\n30cc0d08 feat: 增加公共跳转方法\n7c3284df fix: web demo改名\n1298e041 fix: 公共webview新增通过web页面在chrome或vscodez中打开网页能力\n839deb2e fix: 去掉log以便web页面使用\nca571990 fix: 公共容器增加scss配置\nfb5e725e fix: 公共容器增加全局reset.css\n9a2975ed feat: 增加远程配置获取及版本更新提示能力\nf6ebab90 feat: 增加公共webview\n2ba07bb2 chore: 更名为HiBox\ne605a3bb fix: 更换logo\nf2157b8e fix: 修复填充代码问题\n7c888020 docs: 例会总结\ndd6fb3fd docs: 补充文档\n8b15083e (tag: v0.0.2) chore(release): 0.0.2\n3e757968 fix: 修复打包后引用os路径打包问题\n28e89f9d feat: 增加悬浮提示能力\n0a979264 feat: 条件编译自动编码初步\nfa3dd8e1 feat: 新增上传剪切板中截图特性\n846a5d7e feat: 新增xbox日志输出渠道\n3abc376f chore: 增加单独打包script\n2fcabab0 (tag: v0.0.1) chore(release): 0.0.1\ncd2b2937 chore: 发布完善\n419d5a89 docs: 文档完善\n7d10d4d9 chore: add license\n4aeed8d9 chore: 插件打包相关改动\n7702deae feat: 增加图片上传功能\n0c655e0a chore: prettier约束代码风格\nc14e95c9 feat: 增加侧边栏入口\na70704ce chore: 项目结构初步\nf3a992d5 chore: 约束commitmsg及代码规范\na970c68f feat: 使用vscode插件脚手架生成插件代码\n                                                                                                                                                                                                                                                        "}, {"ts": 1750859649315, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[execute_command for 'git log --oneline'] Result:\\n\\nCommand executed in terminal  within working directory '/Users/<USER>/Documents/work/JoyCoder-Plugin'. Exit code: 0\\nOutput:\\n81345362 (HEAD -> release/release-vs-3.0.0-0620, origin/release/release-vs-3.0.0-0620) feat(agent): 恢复ProgressIndicator组件的使用\\n829cce59 Merge branch 'feature/multi-agent-0430' into release/release-vs-3.0.0-0620\\n90a1dd40 (origin/feature/multi-agent-0430) Merge branch 'feature/multi-agent-0430' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into feature/multi-agent-0430\\ne47f7484 feat(代码索引): 添加基于Qdrant的语义代码搜索功能\\n90cc2b6d Merge branch 'release/ide-agent-0.7.1-bak' into release/release-vs-3.0.0-0620\\n82245bb8 Merge branch 'release/release-vs-3.0.0-0620-bak' into release/release-vs-3.0.0-0620\\ne1da20c8 Merge branch 'release/ide-agent-0.7.1-bak' into release/release-vs-3.0.0-0620\\nd394e094 feat(dialog): 添加自定义模式切换功能\\n0a0cd08b refactor(智能体管理): 重构组件结构并拆分子组件\\nb42ef5aa (origin/release/ide-agent-0.7.1-bak, release/ide-agent-0.7.1-bak) style(BrowserSessionRow): 优化浏览器思考中状态的图片展示样式\\n04c06969 fix(api): 修复对网关返回数据处理缺陷\\na6e1b846 refactor(settings): mcp、智能体设置页代码优化\\n7f76a8d1 Merge branch 'release/ide-agent-0.7.1-bak' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1-bak\\nfed9f329 (origin/feature/ide-agent-0.7.1-joycode) refactor(coder): 品牌名更改，将命令配置项前缀从JoyCoder更改为JoyCode\\n6595b2ea Merge branch 'release/release-vs-3.0.0-0620' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/release-vs-3.0.0-0620\\nda657c8f (origin/release/ide-agent-0.7.1-bak-joycoder, release/ide-agent-0.7.1-bak-joycoder) feat(ChatRow): 优化子任务显示，添加思考中动画\\n47aba378 Merge branch 'release/ide-agent-0.7.1' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1\\n503e2adb refactor(coder): 将命令行配置项前缀从JoyCode更改为JoyCoder\\n4e667409 refactor(ContextMenu): 简化菜单项文本内容\\n2fc58ca2 Merge branch 'release/ide-agent-0.7.1' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1\\n57121f8b refactor(coder): 将命令行配置项前缀从JoyCode更改为JoyCoder\\n5dd4220d refactor(ContextMenu): 简化菜单项文本内容\\nee10edf2 Merge branch 'release/release-vs-3.0.0-0620' into release/ide-agent-0.7.1\\n17ee14c1 feat(agent-setting): 添加自动批准功能并优化代码结构\\n11973445 feat(动画): 引入Lottie动画组件引入json资源替换GIF加载动画\\n8484ef56 fix(agent): 优化模式切换展示，优先展示智能体名称\\n0360d42d feat(McpInstalledView): 在mcp设置中使用Lottie动画替换GIF等待图标\\n87bf5abf fix(agent): 修复codebase和use-definition-names工具问题\\n2bc123a9 feat(代码索引): 添加基于Qdrant的语义代码搜索功能\\nd3385ae3 refactor(coder): 移除多余的日志输出\\n08e65eab refactor(coder): 将命令行JoyCoder.config.searchApiKey从JoyCoder改为JoyCode\\ndcf447b8 refactor(coder): 将命令行JoyCode.config.duplicateCodeDetection从JoyCoder改为JoyCode\\n4df18a64 refactor(coder): 将命令行JoyCoder.config.customHelper从JoyCoder改为JoyCode\\n846252c7 refactor(coder): 将命令行JoyCoder.config.cssModuleTransformIgnorePrefix从JoyCoder改为JoyCode\\nf77387c4 refactor(coder): 将命令行JoyCode.config.chatKnowledge.options从JoyCoder改为JoyCode\\n894a3a6c refactor(coder): 将命令行JoyCode.switch.AgentView从JoyCoder改为JoyCode\\n23732fc4 refactor(codebase): 注释掉CodebaseManager中的日志输出代码\\nd078cd6e refactor(coder): 将命令行JoyCode.enableCodebase从JoyCoder改为JoyCode\\nf62a907f refactor(coder): 将命令行JoyCode.enableCheckpoints从JoyCoder改为JoyCode\\n605909a6 refactor(coder): 将命令行JoyCode.enableBrowserTool从JoyCoder改为JoyCode\\n522fc002 refactor(coder): 将命令行JoyCode.config.workSpaceId从JoyCoder改为JoyCode\\n485b67f7 refactor(coder): 将命令行JoyCode.config.translationTokens从JoyCoder改为JoyCode\\n003564b3 refactor(coder): 将命令行JoyCode.config.translationRows从JoyCoder改为JoyCode\\n43a5d6a5 refactor(coder): 将命令行JoyCode.config.test-llm-list从JoyCoder改为JoyCode\\n1652c496 refactor(coder): 将命令行JoyCode.config.personalSnippets从JoyCoder改为JoyCode\\n39a0b277 refactor(coder): 将命令行JoyCode.config.onlyTranslationFlutter3从JoyCoder改为JoyCode\\n50119a03 Merge branch 'release/ide-agent-0.7.1' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1\\n80a91b4f refactor(coder): 将命令行JoyCode.config.completionsRejectTimes从JoyCoder改为JoyCode\\nf26937dc Merge branch 'release/ide-agent-0.7.1' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1\\n14dcc47f refactor(mcp): adjust gap in property header and remove unused manualConfigJson state\\n07012dee refactor(coder): 将命令行JoyCode.config.codeTranslatePrompt从JoyCoder改为JoyCode\\nf3c6736b (feature/multi-agent-0430) fix(行间菜单): 优化行间菜单未登录时处理方式\\n8d4ed3ca refactor(coder): 将命令行JoyCode.config.codeReview.otherRules从JoyCoder改为JoyCode\\nee1a6b1f refactor(coder): 将命令行JoyCode.config.snippets.switch从JoyCoder改为JoyCode\\n4a032b6d refactor(coder): 将命令行JoyCode.config.shenYi.commit从JoyCoder改为JoyCode\\n962d0975 refactor(coder): 将命令行JoyCode.config.codeReview.immediate从JoyCoder改为JoyCode\\n372801de refactor(coder): 将命令行JoyCode.config.codeReview.fileNumber从JoyCoder改为JoyCode\\n2f45f5a2 refactor(coder): 将命令行JoyCode.config.codeReview.errorLine从JoyCoder改为JoyCode\\n7cc1694c refactor(coder): 将命令行JoyCode.config.codeReview.commitMessageStyle从JoyCoder改为JoyCode\\n328b54f9 refactor(coder): 将命令行JoyCode.config.codeReview.commit从JoyCoder改为JoyCode\\n467bf823 refactor(coder): 将命令行JoyCode.config.codeLens-row-menus从JoyCoder改为JoyCode\\n7dcda73a refactor(coder): 将命令行JoyCode.config.codeCompletionsMoreContext从JoyCoder改为JoyCode\\n94c2486c refactor(coder): 将命令行JoyCode.config.codeCompletionsMaxTimes从JoyCoder改为JoyCode\\n3fb8733e refactor(coder): 将命令行JoyCode.config.codeCompletionsMaxLines从JoyCoder改为JoyCode\\nb88ed06c refactor(coder): 将命令行JoyCode.config.codeCompletionsGenTask从JoyCoder改为JoyCode\\n6d7f8871 refactor(coder): 将命令行JoyCode.config.codeCompletionsFormat从JoyCoder改为JoyCode\\n14a9df95 Merge branch 'release/ide-agent-0.7.1' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1\\na182645e refactor(coder): 将命令行JoyCode.config.chatModel从JoyCoder改为JoyCode\\n8bfe602c Merge branch 'release/ide-agent-0.7.1' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1\\n4bcbfdc5 Merge branch 'release/ide-agent-0.7.1' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1\\ne7ef074a refactor(coder): 将命令行JoyCode.config.autoActivateOnNewWindow从JoyCoder改为JoyCode\\n498954f0 feat(mcp): add tips for JSON configuration in McpModal and clear services on quick config toggle\\n4cd64842 refactor(coder): 将命令行JoyCode.coder.selectFileNubmer从JoyCoder改为JoyCode\\n10dd0869 refactor(coder): 将命令行扩展唯一标识符及JoyCode.coder.maxReadFileLine命令JoyCoder品牌名称更改为JoyCode\\n9a019108 Merge branch 'release/ide-agent-0.7.0' into release/ide-agent-0.7.1\\na3852486 Merge branch 'release/ide-agent-0.7.1' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.1\\nd988e9a5 feat(ui): 将产品名称从JoyCoder统一变更为JoyCode\\n496a6c86 (origin/release/ide-agent-0.7.0, release/ide-agent-0.7.0) feat(agent-management): 添加重置Modal参数功能并优化智能体创建流程\\n6c539182 fix(McpMarketView): fix MCP list fetching and reset services on quick config toggle\\n7081d98a fix(mcp): update error message for missing mcp.json and modify instruction text for adding services\\n08746a64 fix(web-agent): 输入框上传图片悬停样式hover样式优化\\n8a814705 fix(chat/ContextMenu): 修改聊天输入框rules类型图标\\n7372f76c fix(webview): 优化消息处理逻辑避免新任务时重复创建任务\\n005f7f04 fix(agent): 优化模式切换展示，优先展示智能体名称\\na75b9365 feat(utils): 添加安全解码URI功能并统一处理中文路径\\n00a2882f Merge branch 'ide-mcp-market20250618' into release/ide-agent-0.7.1\\n1547681c Merge branch release/ide-agent-0.7.0 into release/release-vs-3.0.0-0620-bak\\n0622cda6 chore(vscode-IDE): 更新版本号至2.9.14\\n2ed6cc83 fix(样式调整): 修复设置页面在小分辨率下无法滚动问题和滚动条样式\\n6363e53c Merge branch 'feature/multi-agent-0430' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into feature/multi-agent-0430\\n638e9746 feat(agent): 添加Excel文件内容提取功能并引入exceljs依赖\\ncdd2c1df fix(McpModal): 移除调试用的console.log语句\\nf2faf30e fix(McpHub): 修复远程环境下MCP服务器配置读取逻辑\\n7bfa0694 fix(agent-driven): 修复远程环境下路径处理问题并优化代码格式\\n3bc3532f Merge branch 'release/ide-agent-0.7.0' of coding.jd.com:JoyCoder-IDE/JoyCoder-Plugin into release/ide-agent-0.7.0\\n2496fd49 fix(MessageToolbar): 远程环境下隐藏回滚按钮\\n818f6c35 fix(agent-driven): 优化agentId排序逻辑\\n0acdb244 feat(AgentManagement): 根据source字段动态显示MD编辑链接并调整作用范围默认值\\n469fb56c fix(AgentManagement): 修复表单字段空值校验问题\\n42ecc2c8 feat(agent-management): 为智能体模式添加描述字段并更新展示逻辑\\n\\n[...1285 lines omitted...]\\n\\n9093cc82 fix(codeshow): 1024节引导语，补充监控，必须输入10行代码参加\\n3dc10092 (tag: v1.0.2) chore(release): 1.0.2\\n935bf7b3 fix(codeshow): 1024节引导语，必须输入10行代码参加\\n2c4f74b1 (tag: v1.0.1) chore(release): 1.0.1\\n6c8e8527 fix(codeshow): 补充1024节打卡描述哦\\n5b714ee4 Merge request #2 from JoyCoder:feature_joycoder_v1 on 2023-10-13 17:35:01\\n0d9d851c fix(codeshow): 替换为线上的打卡接口\\n82ade60f fix(codeshow): 补充开关，修复添加打卡内容时，主动关掉弹窗\\n2cf67861 (tag: v1.0.0) chore(release): 1.0.0\\na3d8a899 Merge branch 'feature_joycoder_v1'\\n6423b760 docs(docs): 更新changelog\\nd15e0213 chore(npm script): 发布命令优化\\n517ddb1d feat(codeshow): 提示词模板支持可配置\\nb50ec932 feat(品牌融合): 优化新老插件切换体验\\n919f4ec7 fix(codeshow): 成功登录后继续打卡流程，优化黑色系\\ned109b9b feat(codeshow): 替换为京东红色系\\nc1d448ac feat(品牌融合): 首次安装时自动同步HiBox配置并卸载老插件\\n7ccc5c08 fix(codeshow): 打卡接口支持远程下发\\n062e6635 feat(icon): 侧边图标替换为outline形式\\n10b193bb feat(codeshow): 语言读取优化、可配置化活动\\n84808647 feat(web): webview面板中类名替换为joycoderfe\\n48adef2f feat(shendeng): 支持1024神灯姐打卡活动\\ncee2bafc feat(品牌融合): 插件更名为JoyCoder-前端专业版 & 无用功能删减 & 结构及文档优化\\nf09bae88 feat(H5调试): 优化视图样式布局\\n44284303 feat(H5调试): 增加视图缩放最大尺寸限制逻辑\\n4c58ae77 feat(H5调试): 增加视图缩放逻辑\\n6f0092b7 fix(H5调试): 修复模拟设备尺寸异常问题\\nfd907145 Merge branch 'master' of coding.jd.com:HiBox/HiBox into feature_browse_lite\\nd0c9cff6 feat(H5调试): 完善调试映射页面\\nee888633 (tag: v2.12.0) chore(release): 2.12.0\\ncae36ef0 fix(auto import): 修复输入if+空格后误触发import语句提示问题\\na866bdaf fix(auto import): 优化import语句补全体验\\n22fcd4da fix(快速开始): 优化快速创建体验:避免重复操作\\nfde62a07 feat(taro): 增加Taro组件,API,Hooks,条件编译相关snippets\\n642daf69 feat(H5调试): 删除冗余代码\\ncf4246f8 feat(H5调试): 增加调试映射页面\\n6b9eab53 feat(H5调试): 增加无头浏览器逻辑\\nb3218359 feat(H5调试): 初始化内置H5开发调试模块入口\\n56400365 fix(login): 解决登录页面偶现503问题\\n8c90d6e0 (tag: v2.11.5) chore(release): 2.11.5\\nf075fa5e fix(doc): 补充开发贡献文档\\n1e14d761 fix(doc): 补充开发贡献文档\\n612d185e fix(chatgpt): 聊天窗中直接调用handleAskChatGPT，无需进行code拼接\\ndec77101 fix(chatgpt): 自定义key的场景，走默认通道去除频限\\nac98fe3c (tag: v2.11.4) chore(release): 2.11.4\\n1e5af1e6 docs(readme): 增加changelog入口\\n5998dc9f chore(install): 去除npm包安装方式\\n2b042c93 fix(quickjump): 修复umpkey跳kibana问题\\n46dbcf48 (tag: v2.11.3) chore(release): 2.11.3\\n166b6b82 fix(cursor): 修复修改代码时缩进不一致问题\\n29f6a91d fix(cursor): 修复移除结尾换行时产生的bug\\n217cd40b fix(cursor): 将用户选择的代码片段约束为整行\\n41e54f81 fix(cursor): 修改代码完成后自动打开diff\\n14306613 (tag: v2.11.2) chore(release): 2.11.2\\n81cb3d29 chore(打包): bundle大小限制调整\\n9b5017a0 fix(chatgpt): 登录态有效期延长至30天\\n061a321e fix(chatgpt): cursor编码体验优化&小灯泡增加注释功能\\nb77b1619 fix(chatgpt): cursor编码支持从小灯泡唤起\\n2e8a5222 fix(chatgpt): 优化悬浮按钮样式\\na4b58eb0 fix(chatgpt): 新增工具栏\\n31a03a44 (tag: v2.11.1) chore(release): 2.11.1\\n405765d9 fix(chatgpt): 修复你好时，重试的列表判断错误的问题\\n8559de2a (tag: v2.11.0) chore(release): 2.11.0\\n0357d4c6 feat(chatgpt): 优化你好的登录体验\\need7f588 fix(chatgpt): 优化GPT4用尽时的交互体验\\n7be03bee feat(chatgpt): 频限支持GPT4模型\\n26e515db fix(login): 优化登录处理，初始化注册router\\n65410092 (tag: v2.10.1) chore(release): 2.10.1\\n89168417 fix(chatgpt): 主题css文件cdn加载\\n81f7716b fix(chatgpt): 支持主题色切换\\n63725964 (tag: v2.10.0) chore(release): 2.10.0\\n10e98a90 fix(chatgpt): 收藏问题修复及样式优化\\n03da801e fix(chatgpt): 优化提示词收藏状态的调用，减少依赖\\nd3e03c8c feat(chatgpt): 支持复制和重试功能\\n7aaee308 fix(chatgpt-web): 代码类型完善\\nc0981cdf (tag: v2.9.2) chore(release): 2.9.2\\n725f81af fix: 物料导入修复白屏问题 (#69)\\n4aaafde0 (tag: v2.9.1) chore(release): 2.9.1\\n1468d1b1 fix(chatgpt): 修复不存在profile报错的问题\\n7ee71402 (tag: v2.9.0) chore(release): 2.9.0\\na412f8b6 fix(chatgpt): 补充自定义指令新增跳转\\nb4f9811a fix(chatgpt): 避免主按钮被覆盖\\nedac6047 feat(chatgpt): 新增个性化指令特性，配置项UI交互优化\\n456a8508 Merge remote-tracking branch 'origin/master' into feature_chatgpt_custom_instructions\\n9a054e78 feat(chatgpt): 支持提示词收藏&快速检索历史提示词\\nf6c06d66 fix(物料导入): 首次加载时展示bug\\n97590084 refactor(物料导入): 代码优化\\n8d21838e feat(物料导入): 支持配置二级Tab及版本选择\\na18626de (tag: v2.8.1) chore(release): 2.8.1\\nf5680865 fix(chatgpt): 替换知识库链接，默认万能模板更新\\naa0d917f (tag: v2.8.0) chore(release): 2.8.0\\n857156a3 feat(chatgpt): 支持insiders版本vscode登录\\n86dabf78 fix(chatgpt): 登录欢迎弹窗展示1.5秒后自动关闭\\nb6f4ed31 feat(chatgpt): 支持调用集团ChatGPT网关\\nadce42df fix: 修复snippets合并时同名key覆盖问题\\n51cf0bb1 fix: 修复ChatGPT输出换行异常问题\\n9d914d6e (tag: v2.7.0) chore(release): 2.7.0\\nad256dc9 chore(publish): 发布hibox时同步发布hibox-installer\\n13d75b5f Merge request #54 from HiBox:feature_chatgpt_knowledge on 2023-07-07 17:19:24\\ndd49ec2d fix(chatgpt): 修复已存在webview时重新打开的体验问题\\nc37a49bb feat(chatgpt): 通过你好判断登录态和GPT服务可用性\\nacdbe7e6 feat(chatgpt): 支持私域向量知识库\\n06518602 feat(快速开始): 支持Taro项目/页面/页面组件\\ne92573ca (tag: v2.6.0) chore(release): 2.6.0\\nff9e5052 fix(chatgpt): 优化respMaxTokens,缓解上文丢失情况\\nca766408 fix(chatgpt): 图标更换，quickpick优化\\nc1084172 feat(chatgpt): 支持用户选择不同模型\\n2a780bff (tag: v2.5.3) chore(release): 2.5.3\\n76989817 fix(快速开始): 修复未配置formItemConf，不触发兜底bug；兜底逻辑剔除leo类型\\ne2900617 (tag: v2.5.2) chore(release): 2.5.2\\n0bb0ee54 fix(chatgpt): 修复highlight识别```k出现异常报错白屏的问题\\naf6b834c (tag: v2.5.1) chore(release): 2.5.1\\ne6bcff25 fix(chatgpt): 优化异常情况提示\\n86dac5c3 fix(快速开始): 如果远程没有配置name，默认给一个\\n348df732 fix(快速开始): 修复不配置名称报错，兼容templateDirName以/开头的写法\\n949b792a (tag: v2.5.0) chore(release): 2.5.0\\n23a50787 feat(cli&quickstart): 适配Leo：支持Leo快捷命令行和常用Leo模板快速创建 (#34)\\nc8412dfc fix(chatgpt): 调整respMaxTokens入参,尽可能确保完整的输出\\n35aed94f (tag: v2.4.3) chore(release): 2.4.3\\nfb42eec5 fix(chatgpt): 增加chatgpt使用要求\\n06921043 fix(update): 解决windows下自动更新问题,优化自动更新体验\\nff8ca140 fix(chatgpt): 初始化对话框时自动回复,节省token\\n0bd26678 (tag: v2.4.2) chore(release): 2.4.2\\n7bc4f69b fix(cursor): 暂时去掉cursor快捷入口功能\\nbc49b0c8 (tag: v2.4.1) chore(release): 2.4.1\\nb6fafcae fix(cursor): 修复编码失焦问题\\n78b9bef8 (tag: v2.4.0) chore(release): 2.4.0\\nc5bcf0cb chore(release): 优化自动更新体验\\n41193a53 chore(release): 拆分发布命令,以便分步验证\\n24e64cc3 fix(materialimport): 物料导入体验优化（支持NutUI）\\n88dfc12b chore(report): 完善业务&错误监控\\nc7057244 chore(commit): 强制要求commit scope\\n54abcc2c fix(chatgpt): 优化交互体验及相关代码\\n502b4535 chore: 增加调试时忽略其他插件参数\\n9fd57f28 fix(chatgpt): 修复windows下输入换行问题\\n02be51d3 fix(chatgpt): 切换接口逻辑优化\\n10f541f1 feat(chatgpt): chatgpt服务切换为java版本\\nca6335c9 feat: 支持cursor类似的交互能力\\n6c3eea31 (tag: v2.3.0) chore(release): 2.3.0\\n5ffd3074 Merge request #28 from HiBox:feature_chatgpt_opti on 2023-04-18 20:53:07\\nebf454b0 fix(chatgpt): 优化chatgpt交互区域布局\\n65cba1bb fix(chatgpt): 对话框样式优化,用更少区域承载更多内容\\n3599ddd9 feat: chatgpt代码块增加插入和复制操作\\n5268036c fix(chatgpt): 优化输入框回车逻辑\\na8e6b29a fix(chatgpt): 优化请求超时、频限等容灾体验\\na7aed03f feat: 增加chatgpt快捷入口\\n906a9132 fix(chatgpt): 优化代码高亮及输出内容自动滚动体验\\n01868a0a feat: 支持ERP登录态打通\\n2fa68b45 (tag: v2.2.0) chore(release): 2.2.0\\n0081eabd fix: chatgpt文案优化\\ne5d769f8 feat: chatgpt增加提前停止功能，支持配置私有key\\na7a6bf22 feat: 新增HTML转CSS/SCSS/LESS功能\\n4beae970 feat: 新增无用export代码扫描和无用文件扫描功能\\na6912630 refactor: 重命名代码检测模块\\n67ba9880 feat: 支持插件市场一键安装\\nfb896463 (tag: v2.1.2) chore(release): 2.1.2\\nb18e2459 Merge branch 'feature_2.0_opti'\\n489ec7ae fix: 优化自动更新逻辑\\nc347e50f fix: 优化chatgpt体验:支持操作可配置,优化入口\\nb24e6f04 fix: 修复物料导入时只能插入第一行代码的问题\\neba97b3f Merge branch 'master' into feature_2.0_opti\\n36b9d010 docs: 增加接口说明\\n8dd6aa73 docs: 修改联系我们地址\\n0e547eba Merge request #21 into master on 2023-02-10 09:54:20\\nbe28674d docs: 贡献文档优化\\n4e85e8e6 (tag: v2.1.1) chore(release): 2.1.1\\n202163a3 fix: 修复chatgpt无法生成测试用例问题\\n9b5a46dc fix: 修复自动更新后需手动重载问题\\n669fc07e (tag: v2.1.0) chore(release): 2.1.0\\ndeb792cd chore: 发布问题处理\\nb39aec60 feat(chatgpt): 切换到远程代理服务\\na5b07614 (tag: v2.0.0) chore(release): 2.0.0\\n6f438c52 Merge branch 'feature_open_use'\\n763f71e0 fix: 增加chatgpt监控&上报\\n00dbb42c fix: 补充chatgpt问答模式\\n8d863fa8 Merge branch 'feature_open_use' of coding.jd.com:HiBox/HiBox into feature_open_use\\n93fd3d1b fix: chatgpt支持command+回车换行\\n1ecc9baa fix: chatgpt样式优化\\nc815a0c3 feat:  chatgpt异常交互提醒，去除日志提升性能\\n88e503cd fix: 增加私有chatGPTToken配置\\n615a4e5b Merge branch 'feature_open_use' of coding.jd.com:HiBox/HiBox into feature_open_use\\n1539f744 fix: 更新首次安装上报\\ne9dfaa2c feat:  增加chagpt\\na27de21f feat:  优化远程命令行解析方式，调整顺序\\n93ad82c8 fix: 升级axios,解决header content报错\\n56954442 fix: 更新成功时增加公告提示&更新失败引导重试\\n91b76aa5 fix: 修复快捷导航刷新功能\\ndcd3d80f fix: 修复无数据是展示异常问题\\n11ab12ed feat: 搜索增加快捷键\\n6c8dbbd1 fix: windows下自测及问题修复\\n35579a40 fix: 上报信息完善\\n57f661e2 chore: 发版优化\\nceaff45e fix: 自测体验问题修复\\nfac16fc2 feat: 命令行执行拆分本地&远程，修复beta发布问题\\n129f922e fix: 更换配置端文档链接\\n8a0754ec fix: 工作区切换改为远程下发\\n33498798 fix: 完善定制功能开关逻辑\\n2e74ff44 Merge branch 'feature_open_use' of coding.jd.com:HiBox/HiBox into feature_open_use\\n2182cb37 chore: 更新发布脚本\\n8cbe2ed4 feat: 插件配置数据切换至水滴数据源\\na58247a5 fix: 更换模版仓库为ssh地址\\nd4fced01 fix: 编辑区隐藏快捷命令和快速创建\\na69b56fe chore: 编译速度&WEB页首屏速度优化\\n84563504 chore: 编译速度和体积优化\\ne812c372 fix: 类型问题修复\\n5c97b38d feat: 恢复插件包功能,只附带安装轻量且较通用的插件\\n1e9b1f08 fix: 修复悬浮npm包功能包名识别异常问题\\nf15e0d09 feat: 快速开始支持第三方仓库及部分代码重构\\ncb4b3359 fix: 提前插件激活时机\\n7a283bf2 fix: quick-console体验优化&图片上传接口监控&快速开始样式优化\\n0232d41e feat: 新增快速切换工作区&主接口可用率上报等\\nb090feb2 feat: 新增快速添加console代码功能\\n6bc0af6c refactor: 划分悬浮提示功能\\n82bb90d7 refactor: 重构快速跳转模块\\n75ddeef0 chore: 修正package.json\\n833249a2 feat: 支持远程配置来开关非通用性的功能\\nca18ae03 feat: 命令行工厂重构，支持远程配置化\\nc7da5434 chore: 调整项目结构为基于pnpm workspace的monorepo\\n2543a52a chore: 使用pnpm作为包管理器\\ned813ebf chore: 构建优化\\n9c798ea8 Merge branch 'master' into feature_open_use\\n0cd69c16 chore: 更改安装包位置\\n2abbc8af chore: 更改换行符避免出现git差异\\n60b9c893 chore: 提交插件安装包以供用户首次下载\\n0981f4c5 chore(release): 1.4.0\\nc1339897 fix: 解决download库导致的打包失败问题\\n9d7243fe Merge branch 'feature_open_use'\\nf176bf50 feat: 支持插件自动安装更新\\n01396e3c fix: 代码结构优化\\n2d5073a8 fix: 本地配置优化\\n3117066a docs: 更新贡献文档\\n59bcc7fc (tag: v1.3.1) chore(release): 1.3.1\\n6c97e448 fix: 修复自定义编译失败问题\\n2174e486 chore(release): 1.3.0\\n4139cc18 feat: 增加git发布管理后台功能\\n25358af5 refactor: 重写获取私有配置方法\\n0c9bf946 fix: 修复遍历git根目录问题\\nacda4cf4 feat: css中插入图片时增加宽高信息\\n2cb5ad9b chore(release): 1.2.0\\n3b3fb695 feat(cli): 代码优化，命令长度为0 的异常流判断统一处理\\n133012e2 feat(cli): 增加不限制目录的legos命令\\nad3b6d38 fix(cli): 调试代码误提交删除\\n736919a2 (tag: v1.1.1) chore(release): 1.1.1\\n447cb234 fix: 修复umpkey查询bizid失败问题(更换接口)\\na5e5e566 chore(release): 1.1.0\\n736aa515 Merge request #10 from HiBox:feature_cli_factory\\nb2a927eb refactor(util): cli中的utils函数提升\\nfc36aa17 feat(cli): 提示文案调整\\n5e21ac9c feat(cli): 编译taro京喜小程序，判断wxapp是否有编译产物来调整timeout\\n52e6c365 feat(cli): 根目录提示文案优化\\nc1b8923c fix(cli): 调整getTargetPath中isTarget优先级提前\\n9025443c fix(cli): 版本号增加字符串问题修复\\n13ce2903 feat(cli): 更新beta的基线改为npm verisons中最后1个\\nc225ddfb fix(cli): 发布失败提示优化\\n05b7b074 feat(cli): 支持任意仓库发布beta版npm包\\nacaa5873 feat(插件包): 下线插件包特性,只在使用文档推荐,不做强制安装\\n87633bd9 Merge branch 'master' of https://coding.jd.com/HiBox/HiBox\\n78c3ad97 feat(cli): 发布beta包时远程版本选择beta和latest中的较大值\\n5645e9b9 fix(cli): 快捷命令行taro编译京喜小程序\\nc649a2e6 fix(cli): 拼写错误&支持配置timeout等待时间\\na1c4ed26 Merge remote-tracking branch 'origin/master' into feature_cli_factory\\n2cf2bc89 fix(quick_jump): 修复文档快速跳转链接被覆盖问题\\n494993f6 chore: 删除无用文件\\nbb40a55b chore(release): 1.0.0\\n59dda0fd fix: 修复hover重复上报问题\\nf4425ce3 chore: 补充依赖包\\n2b36bd60 fix: 修复数据上报无效问题\\nf69197c2 fix: 重复检测支持配置minTokens\\nf7faa233 feat: treeview展示快速创建\\n6ef950e0 chore: 更换logo\\ne47f2a4d chore: 忽略模板目录\\ncc527966 fix: 调整插件包\\n5b5c8634 Merge branch 'master' into feature_quick_create\\n4f54e3fb feat(快速创建): 模板列表样式调整。\\nd66e6210 Merge branch 'master' into feature_quick_create\\n620b1f8c feat(快速创建): 处理写入路径时减少变动。\\n4b2e8595 feat(cli): 关闭命令行后commandBar隐藏\\n32155dd9 Merge branch 'master' into feature_command_custom_fix\\nb7f9214f chore: 忽略所有的声明文件（ *.d.ts）的类型检查\\n5e4d40d3 fix(代码重复检测): 修复提交ts类型报错\\n4e193189 feat(cli): 增加关闭所有命令行\\n9e04caaa fix(style): 统一空格缩进\\nf3450ebb feat(cli): 自定义配置命令行统一执行器\\nfeba6f6e Merge branch 'feature_cli_factory' into feature_command_custom_fix\\na73432b7 refactor(cli): 按照统一规范实现快捷命令行\\n761a9421 feat: 工作目录支持相对、绝对、项目根目录\\n5f4ee9f3 feat:自定义编译代码优化\\ne872fa2a fix(cli): 修复pages前面多/导致部分cli工具不识别问题\\nf119cc85 feat: 统一message、清理多余console。\\n005c6a1b fix: 修改路径问题。\\n50afeabf fix: 处理本地运行时，报id错误问题。\\na2c45657 chore: 删除并忽略没必要的文件。\\nf72b70d9 Merge branch 'master' into feature_quick_create\\n3fa36f15 feat(快速创建): 新增页面增加路径写入。\\n41856eea Merge branch 'master' into feat_duplicateCode_detectiona\\n7842c25b feat(代码重复检查): 提示文案修改为中文\\n9b178245 fix(代码重复检查): 修复代码诊断展示数量不准确问题\\n5b98ada0 feat(代码重复检查): 查重参数调整\\n66cc5b2b feat(代码重复检查): 代码路径调整\\n512f0109 feat(cli): 递归往父级查找目标文件的逻辑收敛\\n10d880d0 feat(cli): 文件窗口内右键也展示快捷命令行\\ne7b20e51 feat: 代码重复检查支持选中多文件\\n5d12b653 feat(cli): 使用shared/logger标准化展示消息\\ndefa7696 fix(cli): 冲突处理\\n2f393cc5 chore(style): package.json改tab为空格\\n8b4e14a9 fix(cli): 冲突处理\\n65d827e4 fix(cli): 冲突处理\\n36d53785 fix(cli): 冲突处理\\n0ab2875b Merge remote-tracking branch 'origin/master' into feature_cli_factory\\n6ef89767 feat(快速创建): 埋点。\\ndd158f78 feat(cli): 增加使用次数上报\\n3f4039fd feat(cli): 支持jxapp单页发布&基础库发beta版本\\nd9844e80 feat: 自定义命令编译支持多选\\n1291515d fix(快速创建): 读取模板json配置解析问题处理。\\n29ff8284 feat(快速创建): 创建时替换文本。\\ncf783a13 chore: 移动目录。\\ne7c4a021 feat: 新增代码重复检查能力\\n8f364125 feat: 更新自定义编译\\ncf7b5949 Merge branch 'master' into feature_quick_create\\nfbb88656 feat: 新增快速创建插件。\\nb2ce21a1 feat: 调整打开ViewLoader里的viewColumn配置判断逻辑。\\nfdaf654d feat: 新增自定义配置命令\\n1d8b3b46 fix: 修复自动补全标签新增的类名没有定义和悬浮提示问题\\nd54be56a fix: 补充上报\\na58309db refactor: 部分菜单项整合\\ncbae5653 feat: 新增悬浮ip时直达webadmin能力\\n7572b11c feat: 新增推荐插件\\n74b9c8d9 feat: 新增git辅助功能\\n1c1511a4 fix: 获取项目根路径方法问题修复\\n6494b815 feat(cli): jxapp项目H5单页开发能力\\nc15d67fd Merge branch 'master' into feature_quick_create\\n7c0ed789 feat: 新增右击页面目录查看该页面badjs功能\\nb8b4d6ac Merge branch 'master' into feature_quick_create\\n89c2d0d0 (tag: v0.0.3) chore(release): 0.0.3\\nc9344564 feat: 增加插件包能力,安装hibox时自动安装其他推荐插件\\n2345c141 Merge branch 'master' into feature_JHooks_create\\n7eb33eac chore: 更换activitybar图标\\n7619e052 fix: 补充数据上报\\n95e5cbc9 fix: 物料导入模块跳转链接体验优化\\na6743dfb Merge branch 'master' of https://coding.jd.com/HiBox/HiBox\\na2aaa85d feat: 新增快速搜索(组件文档、接口查询、kibana 查询) (#1)\\n30e84ab3 fix: 快捷导航改为craft配置\\n7ce1ba9b fix: 修复css中有注释时css definition失效问题\\n0232885c fix: 优化物料导入编辑框失焦后无法插入代码体验\\nc1c26c0f feat: 优化标签自动补全,并在补全时自动添加css代码\\n24e430b3 feat: 增加css 定义功能(css预览及跳转定位)\\n6372c11f feat: 增加数据上报\\n20412813 feat: 增加快捷操作树视图\\n68e23cc2 feat: 增加快捷导航树视图\\n8904efca feat: 树视图增加问题反馈等入口\\n32f07aee feat: snippet支持远程及本地配置\\naee1405b chore: snippets.json改为动态生成\\n7558b2ca fix: 物料导入时重复安装npm问题修复\\n4c798c49 refactor: 重构获取配置模块\\nb9e37715 docs: 例会文档迁移至joyspace\\ne6821cb3 docs: 例会文档整理\\n907b842e chore: 更换包版本\\nccd1efcc Merge branch 'master' of https://coding.jd.com/HiBox/HiBox\\n06281f25 docs: 更新例会文档\\nd8bcf440 feat: 增加css转为cssmodule能力\\ne7cdcb33 feat: 增加标签补全为cssmodule能力\\nb7f7315d feat: 增加import代码补全\\n3c225822 feat: 增加snippet代码片段能力\\n3a8d8e42 feat: 物料导入体验优化\\n6a0537fd docs: 例会文档\\n97ba0f49 docs: 例会文档\\n0fee2a57 docs: 例会文档\\nce114038 feat: 新增物料导入能力\\n30cc0d08 feat: 增加公共跳转方法\\n7c3284df fix: web demo改名\\n1298e041 fix: 公共webview新增通过web页面在chrome或vscodez中打开网页能力\\n839deb2e fix: 去掉log以便web页面使用\\nca571990 fix: 公共容器增加scss配置\\nfb5e725e fix: 公共容器增加全局reset.css\\n9a2975ed feat: 增加远程配置获取及版本更新提示能力\\nf6ebab90 feat: 增加公共webview\\n2ba07bb2 chore: 更名为HiBox\\ne605a3bb fix: 更换logo\\nf2157b8e fix: 修复填充代码问题\\n7c888020 docs: 例会总结\\ndd6fb3fd docs: 补充文档\\n8b15083e (tag: v0.0.2) chore(release): 0.0.2\\n3e757968 fix: 修复打包后引用os路径打包问题\\n28e89f9d feat: 增加悬浮提示能力\\n0a979264 feat: 条件编译自动编码初步\\nfa3dd8e1 feat: 新增上传剪切板中截图特性\\n846a5d7e feat: 新增xbox日志输出渠道\\n3abc376f chore: 增加单独打包script\\n2fcabab0 (tag: v0.0.1) chore(release): 0.0.1\\ncd2b2937 chore: 发布完善\\n419d5a89 docs: 文档完善\\n7d10d4d9 chore: add license\\n4aeed8d9 chore: 插件打包相关改动\\n7702deae feat: 增加图片上传功能\\n0c655e0a chore: prettier约束代码风格\\nc14e95c9 feat: 增加侧边栏入口\\na70704ce chore: 项目结构初步\\nf3a992d5 chore: 约束commitmsg及代码规范\\na970c68f feat: 使用vscode插件脚手架生成插件代码\\n                                                                                                                                                                                                                                                        \\n\\n<environment_details>\\n# VSCode Visible Files\\npackages/agent-driven/web-agent/src/components/chat/ChatRow.tsx\\n\\n# VSCode Open Tabs\\npackages/agent-setting/src/components/setting/index.tsx,packages/agent-driven/web-agent/src/components/chat/ContextMenu.tsx,package.json,packages/agent-driven/initAgentDriven.ts,packages/agent-driven/src/core/JoyCoder/presentAssistantMessage.ts,packages/agent-driven/web-agent/src/components/chat/ChatRow.tsx,packages/agent-setting/src/components/AgentManagement/index.tsx,packages/plugin-base-ai/src/dialog/index.ts\\n\\n# Current Time\\n2025/6/25 下午9:54:09 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n16,331 (13%)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>code</slug>\\n<name>💻 Code</name>\\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\\n</environment_details>\",\"tokensIn\":30141,\"tokensOut\":150,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1750859654975, "type": "say", "say": "text", "text": "我需要删除指定的三个commit。根据git log的输出，这些commit都在历史记录中。我可以使用git rebase -i命令来交互式地删除这些commit。\n\n我需要找到一个合适的起点来开始rebase。从日志中可以看到，最早的目标commit是e1da20c8，我应该从它之前的一个commit开始rebase。", "partial": false}, {"ts": 1750859657652, "type": "ask", "ask": "command", "text": "git rebase -i d394e094", "partial": false, "isProtected": false}, {"ts": 1750859664402, "type": "ask", "ask": "command_output", "text": ""}, {"ts": 1750859664402, "type": "ask", "ask": "command_output", "text": ""}, {"ts": 1750859702283, "type": "ask", "ask": "command_output", "text": ""}, {"ts": 1750859724734, "type": "ask", "ask": "command_output", "text": ""}, {"ts": 1750859726967, "type": "ask", "ask": "command_output", "text": ""}]