[{"ts": 1748156157418, "type": "say", "say": "text", "text": "// SPDX-License-Identifier: GPL-2.0\n/*\n * Pressure stall information for CPU, memory and IO\n *\n * Copyright (c) 2018 Facebook, Inc.\n * Author: <PERSON> <<EMAIL>>\n *\n * Polling support by <PERSON><PERSON> <<EMAIL>>\n * Copyright (c) 2018 Google, Inc.\n *\n * When CPU, memory and IO are contended, tasks experience delays that\n * reduce throughput and introduce latencies into the workload. Memory\n * and IO contention, in addition, can cause a full loss of forward\n * progress in which the CPU goes idle.\n *\n * This code aggregates individual task delays into resource pressure\n * metrics that indicate problems with both workload health and\n * resource utilization.\n *\n *\t\t\tModel\n *\n * The time in which a task can execute on a CPU is our baseline for\n * productivity. Pressure expresses the amount of time in which this\n * potential cannot be realized due to resource contention.\n *\n * This concept of productivity has two components: the workload and\n * the CPU. To measure the impact of pressure on both, we define two\n * contention states for a resource: SOME and FULL.\n *\n * In the SOME state of a given resource, one or more tasks are\n * delayed on that resource. This affects the workload's ability to\n * perform work, but the CPU may still be executing other tasks.\n *\n * In the FULL state of a given resource, all non-idle tasks are\n * delayed on that resource such that nobody is advancing and the CPU\n * goes idle. This leaves both workload and CPU unproductive.\n *\n *\tSOME = nr_delayed_tasks != 0\n *\tFULL = nr_delayed_tasks != 0 && nr_productive_tasks == 0\n *\n * What it means for a task to be productive is defined differently\n * for each resource. For IO, productive means a running task. For\n * memory, productive means a running task that isn't a reclaimer. For\n * CPU, productive means an on-CPU task.\n *\n * Naturally, the FULL state doesn't exist for the CPU resource at the\n * system level, but exist at the cgroup level. At the cgroup level,\n * FULL means all non-idle tasks in the cgroup are delayed on the CPU\n * resource which is being used by others outside of the cgroup or\n * throttled by the cgroup cpu.max configuration.\n *\n * The percentage of wall clock time spent in those compound stall\n * states gives pressure numbers between 0 and 100 for each resource,\n * where the SOME percentage indicates workload slowdowns and the FULL\n * percentage indicates reduced CPU utilization:\n *\n *\t%SOME = time(SOME) / period\n *\t%FULL = time(FULL) / period\n *\n *\t\t\tMultiple CPUs\n *\n * The more tasks and available CPUs there are, the more work can be\n * performed concurrently. This means that the potential that can go\n * unrealized due to resource contention *also* scales with non-idle\n * tasks and CPUs.\n *\n * Consider a scenario where 257 number crunching tasks are trying to\n * run concurrently on 256 CPUs. If we simply aggregated the task\n * states, we would have to conclude a CPU SOME pressure number of\n * 100%, since *somebody* is waiting on a runqueue at all\n * times. However, that is clearly not the amount of contention the\n * workload is experiencing: only one out of 256 possible execution\n * threads will be contended at any given time, or about 0.4%.\n *\n * Conversely, consider a scenario of 4 tasks and 4 CPUs where at any\n * given time *one* of the tasks is delayed due to a lack of memory.\n * Again, looking purely at the task state would yield a memory FULL\n * pressure number of 0%, since *somebody* is always making forward\n * progress. But again this wouldn't capture the amount of execution\n * potential lost, which is 1 out of 4 CPUs, or 25%.\n *\n * To calculate wasted potential (pressure) with multiple processors,\n * we have to base our calculation on the number of non-idle tasks in\n * conjunction with the number of available CPUs, which is the number\n * of potential execution threads. SOME becomes then the proportion of\n * delayed tasks to possible threads, and FULL is the share of possible\n * threads that are unproductive due to delays:\n *\n *\tthreads = min(nr_nonidle_tasks, nr_cpus)\n *\t   SOME = min(nr_delayed_tasks / threads, 1)\n *\t   FULL = (threads - min(nr_productive_tasks, threads)) / threads\n *\n * For the 257 number crunchers on 256 CPUs, this yields:\n *\n *\tthreads = min(257, 256)\n *\t   SOME = min(1 / 256, 1)             = 0.4%\n *\t   FULL = (256 - min(256, 256)) / 256 = 0%\n *\n * For the 1 out of 4 memory-delayed tasks, this yields:\n *\n *\tthreads = min(4, 4)\n *\t   SOME = min(1 / 4, 1)               = 25%\n *\t   FULL = (4 - min(3, 4)) / 4         = 25%\n *\n * [ Substitute nr_cpus with 1, and you can see that it's a natural\n *   extension of the single-CPU model. ]\n *\n *\t\t\tImplementation\n *\n * To assess the precise time spent in each such state, we would have\n * to freeze the system on task changes and start/stop the state\n * clocks accordingly. Obviously that doesn't scale in practice.\n *\n * Because the scheduler aims to distribute the compute load evenly\n * among the available CPUs, we can track task state locally to each\n * CPU and, at much lower frequency, extrapolate the global state for\n * the cumulative stall times and the running averages.\n *\n * For each runqueue, we track:\n *\n *\t   tSOME[cpu] = time(nr_delayed_tasks[cpu] != 0)\n *\t   tFULL[cpu] = time(nr_delayed_tasks[cpu] && !nr_productive_tasks[cpu])\n *\ttNONIDLE[cpu] = time(nr_nonidle_tasks[cpu] != 0)\n *\n * and then periodically aggregate:\n *\n *\ttNONIDLE = sum(tNONIDLE[i])\n *\n *\t   tSOME = sum(tSOME[i] * tNONIDLE[i]) / tNONIDLE\n *\t   tFULL = sum(tFULL[i] * tNONIDLE[i]) / tNONIDLE\n *\n *\t   %SOME = tSOME / period\n *\t   %FULL = tFULL / period\n *\n * This gives us an approximation of pressure that is practical\n * cost-wise, yet way more sensitive and accurate than periodic\n * sampling of the aggregate task states would be.\n */\n\nstatic int psi_bug __read_mostly;\n\nDEFINE_STATIC_KEY_FALSE(psi_disabled);\nstatic DEFINE_STATIC_KEY_TRUE(psi_cgroups_enabled);\n\n#ifdef CONFIG_PSI_DEFAULT_DISABLED\nstatic bool psi_enable;\n#else\nstatic bool psi_enable = true;\n#endif\nstatic int __init setup_psi(char *str)\n{\n\treturn kstrtobool(str, &psi_enable) == 0;\n}\n__setup(\"psi=\", setup_psi);\n\n/* Running averages - we need to be higher-res than loadavg */\n#define PSI_FREQ\t(2*HZ+1)\t/* 2 sec intervals */\n#define EXP_10s\t\t1677\t\t/* 1/exp(2s/10s) as fixed-point */\n#define EXP_60s\t\t1981\t\t/* 1/exp(2s/60s) */\n#define EXP_300s\t2034\t\t/* 1/exp(2s/300s) */\n\n/* PSI trigger definitions */\n#define WINDOW_MAX_US 10000000\t/* Max window size is 10s */\n#define UPDATES_PER_WINDOW 10\t/* 10 updates per window */\n\n/* Sampling frequency in nanoseconds */\nstatic u64 psi_period __read_mostly;\n\n/* System-level pressure and stall tracking */\nstatic DEFINE_PER_CPU(struct psi_group_cpu, system_group_pcpu);\nstruct psi_group psi_system = {\n\t.pcpu = &system_group_pcpu,\n};\n\nstatic void psi_avgs_work(struct work_struct *work);\n\nstatic void poll_timer_fn(struct timer_list *t);\n\nstatic void group_init(struct psi_group *group)\n{\n\tint cpu;\n\n\tgroup->enabled = true;\n\tfor_each_possible_cpu(cpu)\n\t\tseqcount_init(&per_cpu_ptr(group->pcpu, cpu)->seq);\n\tgroup->avg_last_update = sched_clock();\n\tgroup->avg_next_update = group->avg_last_update + psi_period;\n\tmutex_init(&group->avgs_lock);\n\n\t/* Init avg trigger-related members */\n\tINIT_LIST_HEAD(&group->avg_triggers);\n\tmemset(group->avg_nr_triggers, 0, sizeof(group->avg_nr_triggers));\n\tINIT_DELAYED_WORK(&group->avgs_work, psi_avgs_work);\n\n\t/* Init rtpoll trigger-related members */\n\tatomic_set(&group->rtpoll_scheduled, 0);\n\tmutex_init(&group->rtpoll_trigger_lock);\n\tINIT_LIST_HEAD(&group->rtpoll_triggers);\n\tgroup->rtpoll_min_period = U32_MAX;\n\tgroup->rtpoll_next_update = ULLONG_MAX;\n\tinit_waitqueue_head(&group->rtpoll_wait);\n\ttimer_setup(&group->rtpoll_timer, poll_timer_fn, 0);\n\trcu_assign_pointer(group->rtpoll_task, NULL);\n}\n\nvoid __init psi_init(void)\n{\n\tif (!psi_enable) {\n\t\tstatic_branch_enable(&psi_disabled);\n\t\tstatic_branch_disable(&psi_cgroups_enabled);\n\t\treturn;\n\t}\n\n\tif (!cgroup_psi_enabled())\n\t\tstatic_branch_disable(&psi_cgroups_enabled);\n\n\tpsi_period = jiffies_to_nsecs(PSI_FREQ);\n\tgroup_init(&psi_system);\n}\n\nstatic u32 test_states(unsigned int *tasks, u32 state_mask)\n{\n\tconst bool oncpu = state_mask & PSI_ONCPU;\n\n\tif (tasks[NR_IOWAIT]) {\n\t\tstate_mask |= BIT(PSI_IO_SOME);\n\t\tif (!tasks[NR_RUNNING])\n\t\t\tstate_mask |= BIT(PSI_IO_FULL);\n\t}\n\n\tif (tasks[NR_MEMSTALL]) {\n\t\tstate_mask |= BIT(PSI_MEM_SOME);\n\t\tif (tasks[NR_RUNNING] == tasks[NR_MEMSTALL_RUNNING])\n\t\t\tstate_mask |= BIT(PSI_MEM_FULL);\n\t}\n\n\tif (tasks[NR_RUNNING] > oncpu)\n\t\tstate_mask |= BIT(PSI_CPU_SOME);\n\n\tif (tasks[NR_RUNNING] && !oncpu)\n\t\tstate_mask |= BIT(PSI_CPU_FULL);\n\n\tif (tasks[NR_IOWAIT] || tasks[NR_MEMSTALL] || tasks[NR_RUNNING])\n\t\tstate_mask |= BIT(PSI_NONIDLE);\n\n\treturn state_mask;\n}\n\nstatic void get_recent_times(struct psi_group *group, int cpu,\n\t\t\t     enum psi_aggregators aggregator, u32 *times,\n\t\t\t     u32 *pchanged_states)\n{\n\tstruct psi_group_cpu *groupc = per_cpu_ptr(group->pcpu, cpu);\n\tint current_cpu = raw_smp_processor_id();\n\tunsigned int tasks[NR_PSI_TASK_COUNTS];\n\tu64 now, state_start;\n\tenum psi_states s;\n\tunsigned int seq;\n\tu32 state_mask;\n\n\t*pchanged_states = 0;\n\n\t/* Snapshot a coherent view of the CPU state */\n\tdo {\n\t\tseq = read_seqcount_begin(&groupc->seq);\n\t\tnow = cpu_clock(cpu);\n\t\tmemcpy(times, groupc->times, sizeof(groupc->times));\n\t\tstate_mask = groupc->state_mask;\n\t\tstate_start = groupc->state_start;\n\t\tif (cpu == current_cpu)\n\t\t\tmemcpy(tasks, groupc->tasks, sizeof(groupc->tasks));\n\t} while (read_seqcount_retry(&groupc->seq, seq));\n\n\t/* Calculate state time deltas against the previous snapshot */\n\tfor (s = 0; s < NR_PSI_STATES; s++) {\n\t\tu32 delta;\n\t\t/*\n\t\t * In addition to already concluded states, we also\n\t\t * incorporate currently active states on the CPU,\n\t\t * since states may last for many sampling periods.\n\t\t *\n\t\t * This way we keep our delta sampling buckets small\n\t\t * (u32) and our reported pressure close to what's\n\t\t * actually happening.\n\t\t */\n\t\tif (state_mask & (1 << s))\n\t\t\ttimes[s] += now - state_start;\n\n\t\tdelta = times[s] - groupc->times_prev[aggregator][s];\n\t\tgroupc->times_prev[aggregator][s] = times[s];\n\n\t\ttimes[s] = delta;\n\t\tif (delta)\n\t\t\t*pchanged_states |= (1 << s);\n\t}\n\n\t/*\n\t * When collect_percpu_times() from the avgs_work, we don't want to\n\t * re-arm avgs_work when all CPUs are IDLE. But the current CPU running\n\t * this avgs_work is never IDLE, cause avgs_work can't be shut off.\n\t * So for the current CPU, we need to re-arm avgs_work only when\n\t * (NR_RUNNING > 1 || NR_IOWAIT > 0 || NR_MEMSTALL > 0), for other CPUs\n\t * we can just check PSI_NONIDLE delta.\n\t */\n\tif (current_work() == &group->avgs_work.work) {\n\t\tbool reschedule;\n\n\t\tif (cpu == current_cpu)\n\t\t\treschedule = tasks[NR_RUNNING] +\n\t\t\t\t     tasks[NR_IOWAIT] +\n\t\t\t\t     tasks[NR_MEMSTALL] > 1;\n\t\telse\n\t\t\treschedule = *pchanged_states & (1 << PSI_NONIDLE);\n\n\t\tif (reschedule)\n\t\t\t*pchanged_states |= PSI_STATE_RESCHEDULE;\n\t}\n}\n\nstatic void calc_avgs(unsigned long avg[3], int missed_periods,\n\t\t      u64 time, u64 period)\n{\n\tunsigned long pct;\n\n\t/* Fill in zeroes for periods of no activity */\n\tif (missed_periods) {\n\t\tavg[0] = calc_load_n(avg[0], EXP_10s, 0, missed_periods);\n\t\tavg[1] = calc_load_n(avg[1], EXP_60s, 0, missed_periods);\n\t\tavg[2] = calc_load_n(avg[2], EXP_300s, 0, missed_periods);\n\t}\n\n\t/* Sample the most recent active period */\n\tpct = div_u64(time * 100, period);\n\tpct *= FIXED_1;\n\tavg[0] = calc_load(avg[0], EXP_10s, pct);\n\tavg[1] = calc_load(avg[1], EXP_60s, pct);\n\tavg[2] = calc_load(avg[2], EXP_300s, pct);\n}\n\nstatic void collect_percpu_times(struct psi_group *group,\n\t\t\t\t enum psi_aggregators aggregator,\n\t\t\t\t u32 *pchanged_states)\n{\n\tu64 deltas[NR_PSI_STATES - 1] = { 0, };\n\tunsigned long nonidle_total = 0;\n\tu32 changed_states = 0;\n\tint cpu;\n\tint s;\n\n\t/*\n\t * Collect the per-cpu time buckets and average them into a\n\t * single time sample that is normalized to wall clock time.\n\t *\n\t * For averaging, each CPU is weighted by its non-idle time in\n\t * the sampling period. This eliminates artifacts from uneven\n\t * loading, or even entirely idle CPUs.\n\t */\n\tfor_each_possible_cpu(cpu) {\n\t\tu32 times[NR_PSI_STATES];\n\t\tu32 nonidle;\n\t\tu32 cpu_changed_states;\n\n\t\tget_recent_times(group, cpu, aggregator, times,\n\t\t\t\t&cpu_changed_states);\n\t\tchanged_states |= cpu_changed_states;\n\n\t\tnonidle = nsecs_to_jiffies(times[PSI_NONIDLE]);\n\t\tnonidle_total += nonidle;\n\n\t\tfor (s = 0; s < PSI_NONIDLE; s++)\n\t\t\tdeltas[s] += (u64)times[s] * nonidle;\n\t}\n\n\t/*\n\t * Integrate the sample into the running statistics that are\n\t * reported to userspace: the cumulative stall times and the\n\t * decaying averages.\n\t *\n\t * Pressure percentages are sampled at PSI_FREQ. We might be\n\t * called more often when the user polls more frequently than\n\t * that; we might be called less often when there is no task\n\t * activity, thus no data, and clock ticks are sporadic. The\n\t * below handles both.\n\t */\n\n\t/* total= */\n\tfor (s = 0; s < NR_PSI_STATES - 1; s++)\n\t\tgroup->total[aggregator][s] +=\n\t\t\t\tdiv_u64(deltas[s], max(nonidle_total, 1UL));\n\n\tif (pchanged_states)\n\t\t*pchanged_states = changed_states;\n}\n\n/* Trigger tracking window manipulations */\nstatic void window_reset(struct psi_window *win, u64 now, u64 value,\n\t\t\t u64 prev_growth)\n{\n\twin->start_time = now;\n\twin->start_value = value;\n\twin->prev_growth = prev_growth;\n}\n\n/*\n * PSI growth tracking window update and growth calculation routine.\n *\n * This approximates a sliding tracking window by interpolating\n * partially elapsed windows using historical growth data from the\n * previous intervals. This minimizes memory requirements (by not storing\n * all the intermediate values in the previous window) and simplifies\n * the calculations. It works well because PSI signal changes only in\n * positive direction and over relatively small window sizes the growth\n * is close to linear.\n */\nstatic u64 window_update(struct psi_window *win, u64 now, u64 value)\n{\n\tu64 elapsed;\n\tu64 growth;\n\n\telapsed = now - win->start_time;\n\tgrowth = value - win->start_value;\n\t/*\n\t * After each tracking window passes win->start_value and\n\t * win->start_time get reset and win->prev_growth stores\n\t * the average per-window growth of the previous window.\n\t * win->prev_growth is then used to interpolate additional\n\t * growth from the previous window assuming it was linear.\n\t */\n\tif (elapsed > win->size)\n\t\twindow_reset(win, now, value, growth);\n\telse {\n\t\tu32 remaining;\n\n\t\tremaining = win->size - elapsed;\n\t\tgrowth += div64_u64(win->prev_growth * remaining, win->size);\n\t}\n\n\treturn growth;\n}\n\nstatic void update_triggers(struct psi_group *group, u64 now,\n\t\t\t\t\t\t   enum psi_aggregators aggregator)\n{\n\tstruct psi_trigger *t;\n\tu64 *total = group->total[aggregator];\n\tstruct list_head *triggers;\n\tu64 *aggregator_total;\n\n\tif (aggregator == PSI_AVGS) {\n\t\ttriggers = &group->avg_triggers;\n\t\taggregator_total = group->avg_total;\n\t} else {\n\t\ttriggers = &group->rtpoll_triggers;\n\t\taggregator_total = group->rtpoll_total;\n\t}\n\n\t/*\n\t * On subsequent updates, calculate growth deltas and let\n\t * watchers know when their specified thresholds are exceeded.\n\t */\n\tlist_for_each_entry(t, triggers, node) {\n\t\tu64 growth;\n\t\tbool new_stall;\n\n\t\tnew_stall = aggregator_total[t->state] != total[t->state];\n\n\t\t/* Check for stall activity or a previous threshold breach */\n\t\tif (!new_stall && !t->pending_event)\n\t\t\tcontinue;\n\t\t/*\n\t\t * Check for new stall activity, as well as deferred\n\t\t * events that occurred in the last window after the\n\t\t * trigger had already fired (we want to ratelimit\n\t\t * events without dropping any).\n\t\t */\n\t\tif (new_stall) {\n\t\t\t/* Calculate growth since last update */\n\t\t\tgrowth = window_update(&t->win, now, total[t->state]);\n\t\t\tif (!t->pending_event) {\n\t\t\t\tif (growth < t->threshold)\n\t\t\t\t\tcontinue;\n\n\t\t\t\tt->pending_event = true;\n\t\t\t}\n\t\t}\n\t\t/* Limit event signaling to once per window */\n\t\tif (now < t->last_event_time + t->win.size)\n\t\t\tcontinue;\n\n\t\t/* Generate an event */\n\t\tif (cmpxchg(&t->event, 0, 1) == 0) {\n\t\t\tif (t->of)\n\t\t\t\tkernfs_notify(t->of->kn);\n\t\t\telse\n\t\t\t\twake_up_interruptible(&t->event_wait);\n\t\t}\n\t\tt->last_event_time = now;\n\t\t/* Reset threshold breach flag once event got generated */\n\t\tt->pending_event = false;\n\t}\n}\n\nstatic u64 update_averages(struct psi_group *group, u64 now)\n{\n\tunsigned long missed_periods = 0;\n\tu64 expires, period;\n\tu64 avg_next_update;\n\tint s;\n\n\t/* avgX= */\n\texpires = group->avg_next_update;\n\tif (now - expires >= psi_period)\n\t\tmissed_periods = div_u64(now - expires, psi_period);\n\n\t/*\n\t * The periodic clock tick can get delayed for various\n\t * reasons, especially on loaded systems. To avoid clock\n\t * drift, we schedule the clock in fixed psi_period intervals.\n\t * But the deltas we sample out of the per-cpu buckets above\n\t * are based on the actual time elapsing between clock ticks.\n\t */\n\tavg_next_update = expires + ((1 + missed_periods) * psi_period);\n\tperiod = now - (group->avg_last_update + (missed_periods * psi_period));\n\tgroup->avg_last_update = now;\n\n\tfor (s = 0; s < NR_PSI_STATES - 1; s++) {\n\t\tu32 sample;\n\n\t\tsample = group->total[PSI_AVGS][s] - group->avg_total[s];\n\t\t/*\n\t\t * Due to the lockless sampling of the time buckets,\n\t\t * recorded time deltas can slip into the next period,\n\t\t * which under full pressure can result in samples in\n\t\t * excess of the period length.\n\t\t *\n\t\t * We don't want to report non-sensical pressures in\n\t\t * excess of 100%, nor do we want to drop such events\n\t\t * on the floor. Instead we punt any overage into the\n\t\t * future until pressure subsides. By doing this we\n\t\t * don't underreport the occurring pressure curve, we\n\t\t * just report it delayed by one period length.\n\t\t *\n\t\t * The error isn't cumulative. As soon as another\n\t\t * delta slips from a period P to P+1, by definition\n\t\t * it frees up its time T in P.\n\t\t */\n\t\tif (sample > period)\n\t\t\tsample = period;\n\t\tgroup->avg_total[s] += sample;\n\t\tcalc_avgs(group->avg[s], missed_periods, sample, period);\n\t}\n\n\treturn avg_next_update;\n}\n\nstatic void psi_avgs_work(struct work_struct *work)\n{\n\tstruct delayed_work *dwork;\n\tstruct psi_group *group;\n\tu32 changed_states;\n\tu64 now;\n\n\tdwork = to_delayed_work(work);\n\tgroup = container_of(dwork, struct psi_group, avgs_work);\n\n\tmutex_lock(&group->avgs_lock);\n\n\tnow = sched_clock();\n\n\tcollect_percpu_times(group, PSI_AVGS, &changed_states);\n\t/*\n\t * If there is task activity, periodically fold the per-cpu\n\t * times and feed samples into the running averages. If things\n\t * are idle and there is no data to process, stop the clock.\n\t * Once restarted, we'll catch up the running averages in one\n\t * go - see calc_avgs() and missed_periods.\n\t */\n\tif (now >= group->avg_next_update) {\n\t\tupdate_triggers(group, now, PSI_AVGS);\n\t\tgroup->avg_next_update = update_averages(group, now);\n\t}\n\n\tif (changed_states & PSI_STATE_RESCHEDULE) {\n\t\tschedule_delayed_work(dwork, nsecs_to_jiffies(\n\t\t\t\tgroup->avg_next_update - now) + 1);\n\t}\n\n\tmutex_unlock(&group->avgs_lock);\n}\n\nstatic void init_rtpoll_triggers(struct psi_group *group, u64 now)\n{\n\tstruct psi_trigger *t;\n\n\tlist_for_each_entry(t, &group->rtpoll_triggers, node)\n\t\twindow_reset(&t->win, now,\n\t\t\t\tgroup->total[PSI_POLL][t->state], 0);\n\tmemcpy(group->rtpoll_total, group->total[PSI_POLL],\n\t\t   sizeof(group->rtpoll_total));\n\tgroup->rtpoll_next_update = now + group->rtpoll_min_period;\n}\n\n/* Schedule rtpolling if it's not already scheduled or forced. */\nstatic void psi_schedule_rtpoll_work(struct psi_group *group, unsigned long delay,\n\t\t\t\t   bool force)\n{\n\tstruct task_struct *task;\n\n\t/*\n\t * atomic_xchg should be called even when !force to provide a\n\t * full memory barrier (see the comment inside psi_rtpoll_work).\n\t */\n\tif (atomic_xchg(&group->rtpoll_scheduled, 1) && !force)\n\t\treturn;\n\n\trcu_read_lock();\n\n\ttask = rcu_dereference(group->rtpoll_task);\n\t/*\n\t * kworker might be NULL in case psi_trigger_destroy races with\n\t * psi_task_change (hotpath) which can't use locks\n\t */\n\tif (likely(task))\n\t\tmod_timer(&group->rtpoll_timer, jiffies + delay);\n\telse\n\t\tatomic_set(&group->rtpoll_scheduled, 0);\n\n\trcu_read_unlock();\n}\n\nstatic void psi_rtpoll_work(struct psi_group *group)\n{\n\tbool force_reschedule = false;\n\tu32 changed_states;\n\tu64 now;\n\n\tmutex_lock(&group->rtpoll_trigger_lock);\n\n\tnow = sched_clock();\n\n\tif (now > group->rtpoll_until) {\n\t\t/*\n\t\t * We are either about to start or might stop rtpolling if no\n\t\t * state change was recorded. Resetting rtpoll_scheduled leaves\n\t\t * a small window for psi_group_change to sneak in and schedule\n\t\t * an immediate rtpoll_work before we get to rescheduling. One\n\t\t * potential extra wakeup at the end of the rtpolling window\n\t\t * should be negligible and rtpoll_next_update still keeps\n\t\t * updates correctly on schedule.\n\t\t */\n\t\tatomic_set(&group->rtpoll_scheduled, 0);\n\t\t/*\n\t\t * A task change can race with the rtpoll worker that is supposed to\n\t\t * report on it. To avoid missing events, ensure ordering between\n\t\t * rtpoll_scheduled and the task state accesses, such that if the\n\t\t * rtpoll worker misses the state update, the task change is\n\t\t * guaranteed to reschedule the rtpoll worker:\n\t\t *\n\t\t * rtpoll worker:\n\t\t *   atomic_set(rtpoll_scheduled, 0)\n\t\t *   smp_mb()\n\t\t *   LOAD states\n\t\t *\n\t\t * task change:\n\t\t *   STORE states\n\t\t *   if atomic_xchg(rtpoll_scheduled, 1) == 0:\n\t\t *     schedule rtpoll worker\n\t\t *\n\t\t * The atomic_xchg() implies a full barrier.\n\t\t */\n\t\tsmp_mb();\n\t} else {\n\t\t/* The rtpolling window is not over, keep rescheduling */\n\t\tforce_reschedule = true;\n\t}\n\n\n\tcollect_percpu_times(group, PSI_POLL, &changed_states);\n\n\tif (changed_states & group->rtpoll_states) {\n\t\t/* Initialize trigger windows when entering rtpolling mode */\n\t\tif (now > group->rtpoll_until)\n\t\t\tinit_rtpoll_triggers(group, now);\n\n\t\t/*\n\t\t * Keep the monitor active for at least the duration of the\n\t\t * minimum tracking window as long as monitor states are\n\t\t * changing.\n\t\t */\n\t\tgroup->rtpoll_until = now +\n\t\t\tgroup->rtpoll_min_period * UPDATES_PER_WINDOW;\n\t}\n\n\tif (now > group->rtpoll_until) {\n\t\tgroup->rtpoll_next_update = ULLONG_MAX;\n\t\tgoto out;\n\t}\n\n\tif (now >= group->rtpoll_next_update) {\n\t\tif (changed_states & group->rtpoll_states) {\n\t\t\tupdate_triggers(group, now, PSI_POLL);\n\t\t\tmemcpy(group->rtpoll_total, group->total[PSI_POLL],\n\t\t\t\t   sizeof(group->rtpoll_total));\n\t\t}\n\t\tgroup->rtpoll_next_update = now + group->rtpoll_min_period;\n\t}\n\n\tpsi_schedule_rtpoll_work(group,\n\t\tnsecs_to_jiffies(group->rtpoll_next_update - now) + 1,\n\t\tforce_reschedule);\n\nout:\n\tmutex_unlock(&group->rtpoll_trigger_lock);\n}\n\nstatic int psi_rtpoll_worker(void *data)\n{\n\tstruct psi_group *group = (struct psi_group *)data;\n\n\tsched_set_fifo_low(current);\n\n\twhile (true) {\n\t\twait_event_interruptible(group->rtpoll_wait,\n\t\t\t\tatomic_cmpxchg(&group->rtpoll_wakeup, 1, 0) ||\n\t\t\t\tkthread_should_stop());\n\t\tif (kthread_should_stop())\n\t\t\tbreak;\n\n\t\tpsi_rtpoll_work(group);\n\t}\n\treturn 0;\n}\n\nstatic void poll_timer_fn(struct timer_list *t)\n{\n\tstruct psi_group *group = from_timer(group, t, rtpoll_timer);\n\n\tatomic_set(&group->rtpoll_wakeup, 1);\n\twake_up_interruptible(&group->rtpoll_wait);\n}\n\nstatic void record_times(struct psi_group_cpu *groupc, u64 now)\n{\n\tu32 delta;\n\n\tdelta = now - groupc->state_start;\n\tgroupc->state_start = now;\n\n\tif (groupc->state_mask & (1 << PSI_IO_SOME)) {\n\t\tgroupc->times[PSI_IO_SOME] += delta;\n\t\tif (groupc->state_mask & (1 << PSI_IO_FULL))\n\t\t\tgroupc->times[PSI_IO_FULL] += delta;\n\t}\n\n\tif (groupc->state_mask & (1 << PSI_MEM_SOME)) {\n\t\tgroupc->times[PSI_MEM_SOME] += delta;\n\t\tif (groupc->state_mask & (1 << PSI_MEM_FULL))\n\t\t\tgroupc->times[PSI_MEM_FULL] += delta;\n\t}\n\n\tif (groupc->state_mask & (1 << PSI_CPU_SOME)) {\n\t\tgroupc->times[PSI_CPU_SOME] += delta;\n\t\tif (groupc->state_mask & (1 << PSI_CPU_FULL))\n\t\t\tgroupc->times[PSI_CPU_FULL] += delta;\n\t}\n\n\tif (groupc->state_mask & (1 << PSI_NONIDLE))\n\t\tgroupc->times[PSI_NONIDLE] += delta;\n}\n\nstatic void psi_group_change(struct psi_group *group, int cpu,\n\t\t\t     unsigned int clear, unsigned int set,\n\t\t\t     bool wake_clock)\n{\n\tstruct psi_group_cpu *groupc;\n\tunsigned int t, m;\n\tu32 state_mask;\n\tu64 now;\n\n\tlockdep_assert_rq_held(cpu_rq(cpu));\n\tgroupc = per_cpu_ptr(group->pcpu, cpu);\n\n\t/*\n\t * First we update the task counts according to the state\n\t * change requested through the @clear and @set bits.\n\t *\n\t * Then if the cgroup PSI stats accounting enabled, we\n\t * assess the aggregate resource states this CPU's tasks\n\t * have been in since the last change, and account any\n\t * SOME and FULL time these may have resulted in.\n\t */\n\twrite_seqcount_begin(&groupc->seq);\n\tnow = cpu_clock(cpu);\n\n\t/*\n\t * Start with TSK_ONCPU, which doesn't have a corresponding\n\t * task count - it's just a boolean flag directly encoded in\n\t * the state mask. Clear, set, or carry the current state if\n\t * no changes are requested.\n\t */\n\tif (unlikely(clear & TSK_ONCPU)) {\n\t\tstate_mask = 0;\n\t\tclear &= ~TSK_ONCPU;\n\t} else if (unlikely(set & TSK_ONCPU)) {\n\t\tstate_mask = PSI_ONCPU;\n\t\tset &= ~TSK_ONCPU;\n\t} else {\n\t\tstate_mask = groupc->state_mask & PSI_ONCPU;\n\t}\n\n\t/*\n\t * The rest of the state mask is calculated based on the task\n\t * counts. Update those first, then construct the mask.\n\t */\n\tfor (t = 0, m = clear; m; m &= ~(1 << t), t++) {\n\t\tif (!(m & (1 << t)))\n\t\t\tcontinue;\n\t\tif (groupc->tasks[t]) {\n\t\t\tgroupc->tasks[t]--;\n\t\t} else if (!psi_bug) {\n\t\t\tprintk_deferred(KERN_ERR \"psi: task underflow! cpu=%d t=%d tasks=[%u %u %u %u] clear=%x set=%x\\n\",\n\t\t\t\t\tcpu, t, groupc->tasks[0],\n\t\t\t\t\tgroupc->tasks[1], groupc->tasks[2],\n\t\t\t\t\tgroupc->tasks[3], clear, set);\n\t\t\tpsi_bug = 1;\n\t\t}\n\t}\n\n\tfor (t = 0; set; set &= ~(1 << t), t++)\n\t\tif (set & (1 << t))\n\t\t\tgroupc->tasks[t]++;\n\n\tif (!group->enabled) {\n\t\t/*\n\t\t * On the first group change after disabling PSI, conclude\n\t\t * the current state and flush its time. This is unlikely\n\t\t * to matter to the user, but aggregation (get_recent_times)\n\t\t * may have already incorporated the live state into times_prev;\n\t\t * avoid a delta sample underflow when PSI is later re-enabled.\n\t\t */\n\t\tif (unlikely(groupc->state_mask & (1 << PSI_NONIDLE)))\n\t\t\trecord_times(groupc, now);\n\n\t\tgroupc->state_mask = state_mask;\n\n\t\twrite_seqcount_end(&groupc->seq);\n\t\treturn;\n\t}\n\n\tstate_mask = test_states(groupc->tasks, state_mask);\n\n\t/*\n\t * Since we care about lost potential, a memstall is FULL\n\t * when there are no other working tasks, but also when\n\t * the CPU is actively reclaiming and nothing productive\n\t * could run even if it were runnable. So when the current\n\t * task in a cgroup is in_memstall, the corresponding groupc\n\t * on that cpu is in PSI_MEM_FULL state.\n\t */\n\tif (unlikely((state_mask & PSI_ONCPU) && cpu_curr(cpu)->in_memstall))\n\t\tstate_mask |= (1 << PSI_MEM_FULL);\n\n\trecord_times(groupc, now);\n\n\tgroupc->state_mask = state_mask;\n\n\twrite_seqcount_end(&groupc->seq);\n\n\tif (state_mask & group->rtpoll_states)\n\t\tpsi_schedule_rtpoll_work(group, 1, false);\n\n\tif (wake_clock && !delayed_work_pending(&group->avgs_work))\n\t\tschedule_delayed_work(&group->avgs_work, PSI_FREQ);\n}\n\nstatic inline struct psi_group *task_psi_group(struct task_struct *task)\n{\n#ifdef CONFIG_CGROUPS\n\tif (static_branch_likely(&psi_cgroups_enabled))\n\t\treturn cgroup_psi(task_dfl_cgroup(task));\n#endif\n\treturn &psi_system;\n}\n\nstatic void psi_flags_change(struct task_struct *task, int clear, int set)\n{\n\tif (((task->psi_flags & set) ||\n\t     (task->psi_flags & clear) != clear) &&\n\t    !psi_bug) {\n\t\tprintk_deferred(KERN_ERR \"psi: inconsistent task state! task=%d:%s cpu=%d psi_flags=%x clear=%x set=%x\\n\",\n\t\t\t\ttask->pid, task->comm, task_cpu(task),\n\t\t\t\ttask->psi_flags, clear, set);\n\t\tpsi_bug = 1;\n\t}\n\n\ttask->psi_flags &= ~clear;\n\ttask->psi_flags |= set;\n}\n\nvoid psi_task_change(struct task_struct *task, int clear, int set)\n{\n\tint cpu = task_cpu(task);\n\tstruct psi_group *group;\n\n\tif (!task->pid)\n\t\treturn;\n\n\tpsi_flags_change(task, clear, set);\n\n\tgroup = task_psi_group(task);\n\tdo {\n\t\tpsi_group_change(group, cpu, clear, set, true);\n\t} while ((group = group->parent));\n}\n\nvoid psi_task_switch(struct task_struct *prev, struct task_struct *next,\n\t\t     bool sleep)\n{\n\tstruct psi_group *group, *common = NULL;\n\tint cpu = task_cpu(prev);\n\n\tif (next->pid) {\n\t\tpsi_flags_change(next, 0, TSK_ONCPU);\n\t\t/*\n\t\t * Set TSK_ONCPU on @next's cgroups. If @next shares any\n\t\t * ancestors with @prev, those will already have @prev's\n\t\t * TSK_ONCPU bit set, and we can stop the iteration there.\n\t\t */\n\t\tgroup = task_psi_group(next);\n\t\tdo {\n\t\t\tif (per_cpu_ptr(group->pcpu, cpu)->state_mask &\n\t\t\t    PSI_ONCPU) {\n\t\t\t\tcommon = group;\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tpsi_group_change(group, cpu, 0, TSK_ONCPU, true);\n\t\t} while ((group = group->parent));\n\t}\n\n\tif (prev->pid) {\n\t\tint clear = TSK_ONCPU, set = 0;\n\t\tbool wake_clock = true;\n\n\t\t/*\n\t\t * When we're going to sleep, psi_dequeue() lets us\n\t\t * handle TSK_RUNNING, TSK_MEMSTALL_RUNNING and\n\t\t * TSK_IOWAIT here, where we can combine it with\n\t\t * TSK_ONCPU and save walking common ancestors twice.\n\t\t */\n\t\tif (sleep) {\n\t\t\tclear |= TSK_RUNNING;\n\t\t\tif (prev->in_memstall)\n\t\t\t\tclear |= TSK_MEMSTALL_RUNNING;\n\t\t\tif (prev->in_iowait)\n\t\t\t\tset |= TSK_IOWAIT;\n\n\t\t\t/*\n\t\t\t * Periodic aggregation shuts off if there is a period of no\n\t\t\t * task changes, so we wake it back up if necessary. However,\n\t\t\t * don't do this if the task change is the aggregation worker\n\t\t\t * itself going to sleep, or we'll ping-pong forever.\n\t\t\t */\n\t\t\tif (unlikely((prev->flags & PF_WQ_WORKER) &&\n\t\t\t\t     wq_worker_last_func(prev) == psi_avgs_work))\n\t\t\t\twake_clock = false;\n\t\t}\n\n\t\tpsi_flags_change(prev, clear, set);\n\n\t\tgroup = task_psi_group(prev);\n\t\tdo {\n\t\t\tif (group == common)\n\t\t\t\tbreak;\n\t\t\tpsi_group_change(group, cpu, clear, set, wake_clock);\n\t\t} while ((group = group->parent));\n\n\t\t/*\n\t\t * TSK_ONCPU is handled up to the common ancestor. If there are\n\t\t * any other differences between the two tasks (e.g. prev goes\n\t\t * to sleep, or only one task is memstall), finish propagating\n\t\t * those differences all the way up to the root.\n\t\t */\n\t\tif ((prev->psi_flags ^ next->psi_flags) & ~TSK_ONCPU) {\n\t\t\tclear &= ~TSK_ONCPU;\n\t\t\tfor (; group; group = group->parent)\n\t\t\t\tpsi_group_change(group, cpu, clear, set, wake_clock);\n\t\t}\n\t}\n}\n\n#ifdef CONFIG_IRQ_TIME_ACCOUNTING\nvoid psi_account_irqtime(struct rq *rq, struct task_struct *curr, struct task_struct *prev)\n{\n\tint cpu = task_cpu(curr);\n\tstruct psi_group *group;\n\tstruct psi_group_cpu *groupc;\n\ts64 delta;\n\tu64 irq;\n\n\tif (static_branch_likely(&psi_disabled))\n\t\treturn;\n\n\tif (!curr->pid)\n\t\treturn;\n\n\tlockdep_assert_rq_held(rq);\n\tgroup = task_psi_group(curr);\n\tif (prev && task_psi_group(prev) == group)\n\t\treturn;\n\n\tirq = irq_time_read(cpu);\n\tdelta = (s64)(irq - rq->psi_irq_time);\n\tif (delta < 0)\n\t\treturn;\n\trq->psi_irq_time = irq;\n\n\tdo {\n\t\tu64 now;\n\n\t\tif (!group->enabled)\n\t\t\tcontinue;\n\n\t\tgroupc = per_cpu_ptr(group->pcpu, cpu);\n\n\t\twrite_seqcount_begin(&groupc->seq);\n\t\tnow = cpu_clock(cpu);\n\n\t\trecord_times(groupc, now);\n\t\tgroupc->times[PSI_IRQ_FULL] += delta;\n\n\t\twrite_seqcount_end(&groupc->seq);\n\n\t\tif (group->rtpoll_states & (1 << PSI_IRQ_FULL))\n\t\t\tpsi_schedule_rtpoll_work(group, 1, false);\n\t} while ((group = group->parent));\n}\n#endif\n\n/**\n * psi_memstall_enter - mark the beginning of a memory stall section\n * @flags: flags to handle nested sections\n *\n * Marks the calling task as being stalled due to a lack of memory,\n * such as waiting for a refault or performing reclaim.\n */\nvoid psi_memstall_enter(unsigned long *flags)\n{\n\tstruct rq_flags rf;\n\tstruct rq *rq;\n\n\tif (static_branch_likely(&psi_disabled))\n\t\treturn;\n\n\t*flags = current->in_memstall;\n\tif (*flags)\n\t\treturn;\n\t/*\n\t * in_memstall setting & accounting needs to be atomic wrt\n\t * changes to the task's scheduling state, otherwise we can\n\t * race with CPU migration.\n\t */\n\trq = this_rq_lock_irq(&rf);\n\n\tcurrent->in_memstall = 1;\n\tpsi_task_change(current, 0, TSK_MEMSTALL | TSK_MEMSTALL_RUNNING);\n\n\trq_unlock_irq(rq, &rf);\n}\nEXPORT_SYMBOL_GPL(psi_memstall_enter);\n\n/**\n * psi_memstall_leave - mark the end of an memory stall section\n * @flags: flags to handle nested memdelay sections\n *\n * Marks the calling task as no longer stalled due to lack of memory.\n */\nvoid psi_memstall_leave(unsigned long *flags)\n{\n\tstruct rq_flags rf;\n\tstruct rq *rq;\n\n\tif (static_branch_likely(&psi_disabled))\n\t\treturn;\n\n\tif (*flags)\n\t\treturn;\n\t/*\n\t * in_memstall clearing & accounting needs to be atomic wrt\n\t * changes to the task's scheduling state, otherwise we could\n\t * race with CPU migration.\n\t */\n\trq = this_rq_lock_irq(&rf);\n\n\tcurrent->in_memstall = 0;\n\tpsi_task_change(current, TSK_MEMSTALL | TSK_MEMSTALL_RUNNING, 0);\n\n\trq_unlock_irq(rq, &rf);\n}\nEXPORT_SYMBOL_GPL(psi_memstall_leave);\n\n#ifdef CONFIG_CGROUPS\nint psi_cgroup_alloc(struct cgroup *cgroup)\n{\n\tif (!static_branch_likely(&psi_cgroups_enabled))\n\t\treturn 0;\n\n\tcgroup->psi = kzalloc(sizeof(struct psi_group), GFP_KERNEL);\n\tif (!cgroup->psi)\n\t\treturn -ENOMEM;\n\n\tcgroup->psi->pcpu = alloc_percpu(struct psi_group_cpu);\n\tif (!cgroup->psi->pcpu) {\n\t\tkfree(cgroup->psi);\n\t\treturn -ENOMEM;\n\t}\n\tgroup_init(cgroup->psi);\n\tcgroup->psi->parent = cgroup_psi(cgroup_parent(cgroup));\n\treturn 0;\n}\n\nvoid psi_cgroup_free(struct cgroup *cgroup)\n{\n\tif (!static_branch_likely(&psi_cgroups_enabled))\n\t\treturn;\n\n\tcancel_delayed_work_sync(&cgroup->psi->avgs_work);\n\tfree_percpu(cgroup->psi->pcpu);\n\t/* All triggers must be removed by now */\n\tWARN_ONCE(cgroup->psi->rtpoll_states, \"psi: trigger leak\\n\");\n\tkfree(cgroup->psi);\n}\n\n/**\n * cgroup_move_task - move task to a different cgroup\n * @task: the task\n * @to: the target css_set\n *\n * Move task to a new cgroup and safely migrate its associated stall\n * state between the different groups.\n *\n * This function acquires the task's rq lock to lock out concurrent\n * changes to the task's scheduling state and - in case the task is\n * running - concurrent changes to its stall state.\n */\nvoid cgroup_move_task(struct task_struct *task, struct css_set *to)\n{\n\tunsigned int task_flags;\n\tstruct rq_flags rf;\n\tstruct rq *rq;\n\n\tif (!static_branch_likely(&psi_cgroups_enabled)) {\n\t\t/*\n\t\t * Lame to do this here, but the scheduler cannot be locked\n\t\t * from the outside, so we move cgroups from inside sched/.\n\t\t */\n\t\trcu_assign_pointer(task->cgroups, to);\n\t\treturn;\n\t}\n\n\trq = task_rq_lock(task, &rf);\n\n\t/*\n\t * We may race with schedule() dropping the rq lock between\n\t * deactivating prev and switching to next. Because the psi\n\t * updates from the deactivation are deferred to the switch\n\t * callback to save cgroup tree updates, the task's scheduling\n\t * state here is not coherent with its psi state:\n\t *\n\t * schedule()                   cgroup_move_task()\n\t *   rq_lock()\n\t *   deactivate_task()\n\t *     p->on_rq = 0\n\t *     psi_dequeue() // defers TSK_RUNNING & TSK_IOWAIT updates\n\t *   pick_next_task()\n\t *     rq_unlock()\n\t *                                rq_lock()\n\t *                                psi_task_change() // old cgroup\n\t *                                task->cgroups = to\n\t *                                psi_task_change() // new cgroup\n\t *                                rq_unlock()\n\t *     rq_lock()\n\t *   psi_sched_switch() // does deferred updates in new cgroup\n\t *\n\t * Don't rely on the scheduling state. Use psi_flags instead.\n\t */\n\ttask_flags = task->psi_flags;\n\n\tif (task_flags)\n\t\tpsi_task_change(task, task_flags, 0);\n\n\t/* See comment above */\n\trcu_assign_pointer(task->cgroups, to);\n\n\tif (task_flags)\n\t\tpsi_task_change(task, 0, task_flags);\n\n\ttask_rq_unlock(rq, task, &rf);\n}\n\nvoid psi_cgroup_restart(struct psi_group *group)\n{\n\tint cpu;\n\n\t/*\n\t * After we disable psi_group->enabled, we don't actually\n\t * stop percpu tasks accounting in each psi_group_cpu,\n\t * instead only stop test_states() loop, record_times()\n\t * and averaging worker, see psi_group_change() for details.\n\t *\n\t * When disable cgroup PSI, this function has nothing to sync\n\t * since cgroup pressure files are hidden and percpu psi_group_cpu\n\t * would see !psi_group->enabled and only do task accounting.\n\t *\n\t * When re-enable cgroup PSI, this function use psi_group_change()\n\t * to get correct state mask from test_states() loop on tasks[],\n\t * and restart groupc->state_start from now, use .clear = .set = 0\n\t * here since no task status really changed.\n\t */\n\tif (!group->enabled)\n\t\treturn;\n\n\tfor_each_possible_cpu(cpu) {\n\t\tstruct rq *rq = cpu_rq(cpu);\n\t\tstruct rq_flags rf;\n\n\t\trq_lock_irq(rq, &rf);\n\t\tpsi_group_change(group, cpu, 0, 0, true);\n\t\trq_unlock_irq(rq, &rf);\n\t}\n}\n#endif /* CONFIG_CGROUPS */\n\nint psi_show(struct seq_file *m, struct psi_group *group, enum psi_res res)\n{\n\tbool only_full = false;\n\tint full;\n\tu64 now;\n\n\tif (static_branch_likely(&psi_disabled))\n\t\treturn -EOPNOTSUPP;\n\n\t/* Update averages before reporting them */\n\tmutex_lock(&group->avgs_lock);\n\tnow = sched_clock();\n\tcollect_percpu_times(group, PSI_AVGS, NULL);\n\tif (now >= group->avg_next_update)\n\t\tgroup->avg_next_update = update_averages(group, now);\n\tmutex_unlock(&group->avgs_lock);\n\n#ifdef CONFIG_IRQ_TIME_ACCOUNTING\n\tonly_full = res == PSI_IRQ;\n#endif\n\n\tfor (full = 0; full < 2 - only_full; full++) {\n\t\tunsigned long avg[3] = { 0, };\n\t\tu64 total = 0;\n\t\tint w;\n\n\t\t/* CPU FULL is undefined at the system level */\n\t\tif (!(group == &psi_system && res == PSI_CPU && full)) {\n\t\t\tfor (w = 0; w < 3; w++)\n\t\t\t\tavg[w] = group->avg[res * 2 + full][w];\n\t\t\ttotal = div_u64(group->total[PSI_AVGS][res * 2 + full],\n\t\t\t\t\tNSEC_PER_USEC);\n\t\t}\n\n\t\tseq_printf(m, \"%s avg10=%lu.%02lu avg60=%lu.%02lu avg300=%lu.%02lu total=%llu\\n\",\n\t\t\t   full || only_full ? \"full\" : \"some\",\n\t\t\t   LOAD_INT(avg[0]), LOAD_FRAC(avg[0]),\n\t\t\t   LOAD_INT(avg[1]), LOAD_FRAC(avg[1]),\n\t\t\t   LOAD_INT(avg[2]), LOAD_FRAC(avg[2]),\n\t\t\t   total);\n\t}\n\n\treturn 0;\n}\n\nstruct psi_trigger *psi_trigger_create(struct psi_group *group, char *buf,\n\t\t\t\t       enum psi_res res, struct file *file,\n\t\t\t\t       struct kernfs_open_file *of)\n{\n\tstruct psi_trigger *t;\n\tenum psi_states state;\n\tu32 threshold_us;\n\tbool privileged;\n\tu32 window_us;\n\n\tif (static_branch_likely(&psi_disabled))\n\t\treturn ERR_PTR(-EOPNOTSUPP);\n\n\t/*\n\t * Checking the privilege here on file->f_cred implies that a privileged user\n\t * could open the file and delegate the write to an unprivileged one.\n\t */\n\tprivileged = cap_raised(file->f_cred->cap_effective, CAP_SYS_RESOURCE);\n\n\tif (sscanf(buf, \"some %u %u\", &threshold_us, &window_us) == 2)\n\t\tstate = PSI_IO_SOME + res * 2;\n\telse if (sscanf(buf, \"full %u %u\", &threshold_us, &window_us) == 2)\n\t\tstate = PSI_IO_FULL + res * 2;\n\telse\n\t\treturn ERR_PTR(-EINVAL);\n\n#ifdef CONFIG_IRQ_TIME_ACCOUNTING\n\tif (res == PSI_IRQ && --state != PSI_IRQ_FULL)\n\t\treturn ERR_PTR(-EINVAL);\n#endif\n\n\tif (state >= PSI_NONIDLE)\n\t\treturn ERR_PTR(-EINVAL);\n\n\tif (window_us == 0 || window_us > WINDOW_MAX_US)\n\t\treturn ERR_PTR(-EINVAL);\n\n\t/*\n\t * Unprivileged users can only use 2s windows so that averages aggregation\n\t * work is used, and no RT threads need to be spawned.\n\t */\n\tif (!privileged && window_us % 2000000)\n\t\treturn ERR_PTR(-EINVAL);\n\n\t/* Check threshold */\n\tif (threshold_us == 0 || threshold_us > window_us)\n\t\treturn ERR_PTR(-EINVAL);\n\n\tt = kmalloc(sizeof(*t), GFP_KERNEL);\n\tif (!t)\n\t\treturn ERR_PTR(-ENOMEM);\n\n\tt->group = group;\n\tt->state = state;\n\tt->threshold = threshold_us * NSEC_PER_USEC;\n\tt->win.size = window_us * NSEC_PER_USEC;\n\twindow_reset(&t->win, sched_clock(),\n\t\t\tgroup->total[PSI_POLL][t->state], 0);\n\n\tt->event = 0;\n\tt->last_event_time = 0;\n\tt->of = of;\n\tif (!of)\n\t\tinit_waitqueue_head(&t->event_wait);\n\tt->pending_event = false;\n\tt->aggregator = privileged ? PSI_POLL : PSI_AVGS;\n\n\tif (privileged) {\n\t\tmutex_lock(&group->rtpoll_trigger_lock);\n\n\t\tif (!rcu_access_pointer(group->rtpoll_task)) {\n\t\t\tstruct task_struct *task;\n\n\t\t\ttask = kthread_create(psi_rtpoll_worker, group, \"psimon\");\n\t\t\tif (IS_ERR(task)) {\n\t\t\t\tkfree(t);\n\t\t\t\tmutex_unlock(&group->rtpoll_trigger_lock);\n\t\t\t\treturn ERR_CAST(task);\n\t\t\t}\n\t\t\tatomic_set(&group->rtpoll_wakeup, 0);\n\t\t\twake_up_process(task);\n\t\t\trcu_assign_pointer(group->rtpoll_task, task);\n\t\t}\n\n\t\tlist_add(&t->node, &group->rtpoll_triggers);\n\t\tgroup->rtpoll_min_period = min(group->rtpoll_min_period,\n\t\t\tdiv_u64(t->win.size, UPDATES_PER_WINDOW));\n\t\tgroup->rtpoll_nr_triggers[t->state]++;\n\t\tgroup->rtpoll_states |= (1 << t->state);\n\n\t\tmutex_unlock(&group->rtpoll_trigger_lock);\n\t} else {\n\t\tmutex_lock(&group->avgs_lock);\n\n\t\tlist_add(&t->node, &group->avg_triggers);\n\t\tgroup->avg_nr_triggers[t->state]++;\n\n\t\tmutex_unlock(&group->avgs_lock);\n\t}\n\treturn t;\n}\n\nvoid psi_trigger_destroy(struct psi_trigger *t)\n{\n\tstruct psi_group *group;\n\tstruct task_struct *task_to_destroy = NULL;\n\n\t/*\n\t * We do not check psi_disabled since it might have been disabled after\n\t * the trigger got created.\n\t */\n\tif (!t)\n\t\treturn;\n\n\tgroup = t->group;\n\t/*\n\t * Wakeup waiters to stop polling and clear the queue to prevent it from\n\t * being accessed later. Can happen if cgroup is deleted from under a\n\t * polling process.\n\t */\n\tif (t->of)\n\t\tkernfs_notify(t->of->kn);\n\telse\n\t\twake_up_interruptible(&t->event_wait);\n\n\tif (t->aggregator == PSI_AVGS) {\n\t\tmutex_lock(&group->avgs_lock);\n\t\tif (!list_empty(&t->node)) {\n\t\t\tlist_del(&t->node);\n\t\t\tgroup->avg_nr_triggers[t->state]--;\n\t\t}\n\t\tmutex_unlock(&group->avgs_lock);\n\t} else {\n\t\tmutex_lock(&group->rtpoll_trigger_lock);\n\t\tif (!list_empty(&t->node)) {\n\t\t\tstruct psi_trigger *tmp;\n\t\t\tu64 period = ULLONG_MAX;\n\n\t\t\tlist_del(&t->node);\n\t\t\tgroup->rtpoll_nr_triggers[t->state]--;\n\t\t\tif (!group->rtpoll_nr_triggers[t->state])\n\t\t\t\tgroup->rtpoll_states &= ~(1 << t->state);\n\t\t\t/*\n\t\t\t * Reset min update period for the remaining triggers\n\t\t\t * iff the destroying trigger had the min window size.\n\t\t\t */\n\t\t\tif (group->rtpoll_min_period == div_u64(t->win.size, UPDATES_PER_WINDOW)) {\n\t\t\t\tlist_for_each_entry(tmp, &group->rtpoll_triggers, node)\n\t\t\t\t\tperiod = min(period, div_u64(tmp->win.size,\n\t\t\t\t\t\t\tUPDATES_PER_WINDOW));\n\t\t\t\tgroup->rtpoll_min_period = period;\n\t\t\t}\n\t\t\t/* Destroy rtpoll_task when the last trigger is destroyed */\n\t\t\tif (group->rtpoll_states == 0) {\n\t\t\t\tgroup->rtpoll_until = 0;\n\t\t\t\ttask_to_destroy = rcu_dereference_protected(\n\t\t\t\t\t\tgroup->rtpoll_task,\n\t\t\t\t\t\tlockdep_is_held(&group->rtpoll_trigger_lock));\n\t\t\t\trcu_assign_pointer(group->rtpoll_task, NULL);\n\t\t\t\tdel_timer(&group->rtpoll_timer);\n\t\t\t}\n\t\t}\n\t\tmutex_unlock(&group->rtpoll_trigger_lock);\n\t}\n\n\t/*\n\t * Wait for psi_schedule_rtpoll_work RCU to complete its read-side\n\t * critical section before destroying the trigger and optionally the\n\t * rtpoll_task.\n\t */\n\tsynchronize_rcu();\n\t/*\n\t * Stop kthread 'psimon' after releasing rtpoll_trigger_lock to prevent\n\t * a deadlock while waiting for psi_rtpoll_work to acquire\n\t * rtpoll_trigger_lock\n\t */\n\tif (task_to_destroy) {\n\t\t/*\n\t\t * After the RCU grace period has expired, the worker\n\t\t * can no longer be found through group->rtpoll_task.\n\t\t */\n\t\tkthread_stop(task_to_destroy);\n\t\tatomic_set(&group->rtpoll_scheduled, 0);\n\t}\n\tkfree(t);\n}\n\n__poll_t psi_trigger_poll(void **trigger_ptr,\n\t\t\t\tstruct file *file, poll_table *wait)\n{\n\t__poll_t ret = DEFAULT_POLLMASK;\n\tstruct psi_trigger *t;\n\n\tif (static_branch_likely(&psi_disabled))\n\t\treturn DEFAULT_POLLMASK | EPOLLERR | EPOLLPRI;\n\n\tt = smp_load_acquire(trigger_ptr);\n\tif (!t)\n\t\treturn DEFAULT_POLLMASK | EPOLLERR | EPOLLPRI;\n\n\tif (t->of)\n\t\tkernfs_generic_poll(t->of, wait);\n\telse\n\t\tpoll_wait(file, &t->event_wait, wait);\n\n\tif (cmpxchg(&t->event, 1, 0) == 1)\n\t\tret |= EPOLLPRI;\n\n\treturn ret;\n}\n\n#ifdef CONFIG_PROC_FS\nstatic int psi_io_show(struct seq_file *m, void *v)\n{\n\treturn psi_show(m, &psi_system, PSI_IO);\n}\n\nstatic int psi_memory_show(struct seq_file *m, void *v)\n{\n\treturn psi_show(m, &psi_system, PSI_MEM);\n}\n\nstatic int psi_cpu_show(struct seq_file *m, void *v)\n{\n\treturn psi_show(m, &psi_system, PSI_CPU);\n}\n\nstatic int psi_io_open(struct inode *inode, struct file *file)\n{\n\treturn single_open(file, psi_io_show, NULL);\n}\n\nstatic int psi_memory_open(struct inode *inode, struct file *file)\n{\n\treturn single_open(file, psi_memory_show, NULL);\n}\n\nstatic int psi_cpu_open(struct inode *inode, struct file *file)\n{\n\treturn single_open(file, psi_cpu_show, NULL);\n}\n\nstatic ssize_t psi_write(struct file *file, const char __user *user_buf,\n\t\t\t size_t nbytes, enum psi_res res)\n{\n\tchar buf[32];\n\tsize_t buf_size;\n\tstruct seq_file *seq;\n\tstruct psi_trigger *new;\n\n\tif (static_branch_likely(&psi_disabled))\n\t\treturn -EOPNOTSUPP;\n\n\tif (!nbytes)\n\t\treturn -EINVAL;\n\n\tbuf_size = min(nbytes, sizeof(buf));\n\tif (copy_from_user(buf, user_buf, buf_size))\n\t\treturn -EFAULT;\n\n\tbuf[buf_size - 1] = '\\0';\n\n\tseq = file->private_data;\n\n\t/* Take seq->lock to protect seq->private from concurrent writes */\n\tmutex_lock(&seq->lock);\n\n\t/* Allow only one trigger per file descriptor */\n\tif (seq->private) {\n\t\tmutex_unlock(&seq->lock);\n\t\treturn -EBUSY;\n\t}\n\n\tnew = psi_trigger_create(&psi_system, buf, res, file, NULL);\n\tif (IS_ERR(new)) {\n\t\tmutex_unlock(&seq->lock);\n\t\treturn PTR_ERR(new);\n\t}\n\n\tsmp_store_release(&seq->private, new);\n\tmutex_unlock(&seq->lock);\n\n\treturn nbytes;\n}\n\nstatic ssize_t psi_io_write(struct file *file, const char __user *user_buf,\n\t\t\t    size_t nbytes, loff_t *ppos)\n{\n\treturn psi_write(file, user_buf, nbytes, PSI_IO);\n}\n\nstatic ssize_t psi_memory_write(struct file *file, const char __user *user_buf,\n\t\t\t\tsize_t nbytes, loff_t *ppos)\n{\n\treturn psi_write(file, user_buf, nbytes, PSI_MEM);\n}\n\nstatic ssize_t psi_cpu_write(struct file *file, const char __user *user_buf,\n\t\t\t     size_t nbytes, loff_t *ppos)\n{\n\treturn psi_write(file, user_buf, nbytes, PSI_CPU);\n}\n\nstatic __poll_t psi_fop_poll(struct file *file, poll_table *wait)\n{\n\tstruct seq_file *seq = file->private_data;\n\n\treturn psi_trigger_poll(&seq->private, file, wait);\n}\n\nstatic int psi_fop_release(struct inode *inode, struct file *file)\n{\n\tstruct seq_file *seq = file->private_data;\n\n\tpsi_trigger_destroy(seq->private);\n\treturn single_release(inode, file);\n}\n\nstatic const struct proc_ops psi_io_proc_ops = {\n\t.proc_open\t= psi_io_open,\n\t.proc_read\t= seq_read,\n\t.proc_lseek\t= seq_lseek,\n\t.proc_write\t= psi_io_write,\n\t.proc_poll\t= psi_fop_poll,\n\t.proc_release\t= psi_fop_release,\n};\n\nstatic const struct proc_ops psi_memory_proc_ops = {\n\t.proc_open\t= psi_memory_open,\n\t.proc_read\t= seq_read,\n\t.proc_lseek\t= seq_lseek,\n\t.proc_write\t= psi_memory_write,\n\t.proc_poll\t= psi_fop_poll,\n\t.proc_release\t= psi_fop_release,\n};\n\nstatic const struct proc_ops psi_cpu_proc_ops = {\n\t.proc_open\t= psi_cpu_open,\n\t.proc_read\t= seq_read,\n\t.proc_lseek\t= seq_lseek,\n\t.proc_write\t= psi_cpu_write,\n\t.proc_poll\t= psi_fop_poll,\n\t.proc_release\t= psi_fop_release,\n};\n\n#ifdef CONFIG_IRQ_TIME_ACCOUNTING\nstatic int psi_irq_show(struct seq_file *m, void *v)\n{\n\treturn psi_show(m, &psi_system, PSI_IRQ);\n}\n\nstatic int psi_irq_open(struct inode *inode, struct file *file)\n{\n\treturn single_open(file, psi_irq_show, NULL);\n}\n\nstatic ssize_t psi_irq_write(struct file *file, const char __user *user_buf,\n\t\t\t     size_t nbytes, loff_t *ppos)\n{\n\treturn psi_write(file, user_buf, nbytes, PSI_IRQ);\n}\n\nstatic const struct proc_ops psi_irq_proc_ops = {\n\t.proc_open\t= psi_irq_open,\n\t.proc_read\t= seq_read,\n\t.proc_lseek\t= seq_lseek,\n\t.proc_write\t= psi_irq_write,\n\t.proc_poll\t= psi_fop_poll,\n\t.proc_release\t= psi_fop_release,\n};\n#endif\n\nstatic int __init psi_proc_init(void)\n{\n\tif (psi_enable) {\n\t\tproc_mkdir(\"pressure\", NULL);\n\t\tproc_create(\"pressure/io\", 0666, NULL, &psi_io_proc_ops);\n\t\tproc_create(\"pressure/memory\", 0666, NULL, &psi_memory_proc_ops);\n\t\tproc_create(\"pressure/cpu\", 0666, NULL, &psi_cpu_proc_ops);\n#ifdef CONFIG_IRQ_TIME_ACCOUNTING\n\t\tproc_create(\"pressure/irq\", 0666, NULL, &psi_irq_proc_ops);\n#endif\n\t}\n\treturn 0;\n}\nmodule_init(psi_proc_init);\n\n#endif /* CONFIG_PROC_FS */", "images": []}, {"ts": *************, "type": "say", "say": "api_req_started", "text": "{\"request\":\"<task>\\n// SPDX-License-Identifier: GPL-2.0\\n/*\\n * Pressure stall information for CPU, memory and IO\\n *\\n * Copyright (c) 2018 Facebook, Inc.\\n * Author: <PERSON> <<EMAIL>>\\n *\\n * Polling support by <PERSON><PERSON> <<EMAIL>>\\n * Copyright (c) 2018 Google, Inc.\\n *\\n * When CPU, memory and IO are contended, tasks experience delays that\\n * reduce throughput and introduce latencies into the workload. Memory\\n * and IO contention, in addition, can cause a full loss of forward\\n * progress in which the CPU goes idle.\\n *\\n * This code aggregates individual task delays into resource pressure\\n * metrics that indicate problems with both workload health and\\n * resource utilization.\\n *\\n *\\t\\t\\tModel\\n *\\n * The time in which a task can execute on a CPU is our baseline for\\n * productivity. Pressure expresses the amount of time in which this\\n * potential cannot be realized due to resource contention.\\n *\\n * This concept of productivity has two components: the workload and\\n * the CPU. To measure the impact of pressure on both, we define two\\n * contention states for a resource: SOME and FULL.\\n *\\n * In the SOME state of a given resource, one or more tasks are\\n * delayed on that resource. This affects the workload's ability to\\n * perform work, but the CPU may still be executing other tasks.\\n *\\n * In the FULL state of a given resource, all non-idle tasks are\\n * delayed on that resource such that nobody is advancing and the CPU\\n * goes idle. This leaves both workload and CPU unproductive.\\n *\\n *\\tSOME = nr_delayed_tasks != 0\\n *\\tFULL = nr_delayed_tasks != 0 && nr_productive_tasks == 0\\n *\\n * What it means for a task to be productive is defined differently\\n * for each resource. For IO, productive means a running task. For\\n * memory, productive means a running task that isn't a reclaimer. For\\n * CPU, productive means an on-CPU task.\\n *\\n * Naturally, the FULL state doesn't exist for the CPU resource at the\\n * system level, but exist at the cgroup level. At the cgroup level,\\n * FULL means all non-idle tasks in the cgroup are delayed on the CPU\\n * resource which is being used by others outside of the cgroup or\\n * throttled by the cgroup cpu.max configuration.\\n *\\n * The percentage of wall clock time spent in those compound stall\\n * states gives pressure numbers between 0 and 100 for each resource,\\n * where the SOME percentage indicates workload slowdowns and the FULL\\n * percentage indicates reduced CPU utilization:\\n *\\n *\\t%SOME = time(SOME) / period\\n *\\t%FULL = time(FULL) / period\\n *\\n *\\t\\t\\tMultiple CPUs\\n *\\n * The more tasks and available CPUs there are, the more work can be\\n * performed concurrently. This means that the potential that can go\\n * unrealized due to resource contention *also* scales with non-idle\\n * tasks and CPUs.\\n *\\n * Consider a scenario where 257 number crunching tasks are trying to\\n * run concurrently on 256 CPUs. If we simply aggregated the task\\n * states, we would have to conclude a CPU SOME pressure number of\\n * 100%, since *somebody* is waiting on a runqueue at all\\n * times. However, that is clearly not the amount of contention the\\n * workload is experiencing: only one out of 256 possible execution\\n * threads will be contended at any given time, or about 0.4%.\\n *\\n * Conversely, consider a scenario of 4 tasks and 4 CPUs where at any\\n * given time *one* of the tasks is delayed due to a lack of memory.\\n * Again, looking purely at the task state would yield a memory FULL\\n * pressure number of 0%, since *somebody* is always making forward\\n * progress. But again this wouldn't capture the amount of execution\\n * potential lost, which is 1 out of 4 CPUs, or 25%.\\n *\\n * To calculate wasted potential (pressure) with multiple processors,\\n * we have to base our calculation on the number of non-idle tasks in\\n * conjunction with the number of available CPUs, which is the number\\n * of potential execution threads. SOME becomes then the proportion of\\n * delayed tasks to possible threads, and FULL is the share of possible\\n * threads that are unproductive due to delays:\\n *\\n *\\tthreads = min(nr_nonidle_tasks, nr_cpus)\\n *\\t   SOME = min(nr_delayed_tasks / threads, 1)\\n *\\t   FULL = (threads - min(nr_productive_tasks, threads)) / threads\\n *\\n * For the 257 number crunchers on 256 CPUs, this yields:\\n *\\n *\\tthreads = min(257, 256)\\n *\\t   SOME = min(1 / 256, 1)             = 0.4%\\n *\\t   FULL = (256 - min(256, 256)) / 256 = 0%\\n *\\n * For the 1 out of 4 memory-delayed tasks, this yields:\\n *\\n *\\tthreads = min(4, 4)\\n *\\t   SOME = min(1 / 4, 1)               = 25%\\n *\\t   FULL = (4 - min(3, 4)) / 4         = 25%\\n *\\n * [ Substitute nr_cpus with 1, and you can see that it's a natural\\n *   extension of the single-CPU model. ]\\n *\\n *\\t\\t\\tImplementation\\n *\\n * To assess the precise time spent in each such state, we would have\\n * to freeze the system on task changes and start/stop the state\\n * clocks accordingly. Obviously that doesn't scale in practice.\\n *\\n * Because the scheduler aims to distribute the compute load evenly\\n * among the available CPUs, we can track task state locally to each\\n * CPU and, at much lower frequency, extrapolate the global state for\\n * the cumulative stall times and the running averages.\\n *\\n * For each runqueue, we track:\\n *\\n *\\t   tSOME[cpu] = time(nr_delayed_tasks[cpu] != 0)\\n *\\t   tFULL[cpu] = time(nr_delayed_tasks[cpu] && !nr_productive_tasks[cpu])\\n *\\ttNONIDLE[cpu] = time(nr_nonidle_tasks[cpu] != 0)\\n *\\n * and then periodically aggregate:\\n *\\n *\\ttNONIDLE = sum(tNONIDLE[i])\\n *\\n *\\t   tSOME = sum(tSOME[i] * tNONIDLE[i]) / tNONIDLE\\n *\\t   tFULL = sum(tFULL[i] * tNONIDLE[i]) / tNONIDLE\\n *\\n *\\t   %SOME = tSOME / period\\n *\\t   %FULL = tFULL / period\\n *\\n * This gives us an approximation of pressure that is practical\\n * cost-wise, yet way more sensitive and accurate than periodic\\n * sampling of the aggregate task states would be.\\n */\\n\\nstatic int psi_bug __read_mostly;\\n\\nDEFINE_STATIC_KEY_FALSE(psi_disabled);\\nstatic DEFINE_STATIC_KEY_TRUE(psi_cgroups_enabled);\\n\\n#ifdef CONFIG_PSI_DEFAULT_DISABLED\\nstatic bool psi_enable;\\n#else\\nstatic bool psi_enable = true;\\n#endif\\nstatic int __init setup_psi(char *str)\\n{\\n\\treturn kstrtobool(str, &psi_enable) == 0;\\n}\\n__setup(\\\"psi=\\\", setup_psi);\\n\\n/* Running averages - we need to be higher-res than loadavg */\\n#define PSI_FREQ\\t(2*HZ+1)\\t/* 2 sec intervals */\\n#define EXP_10s\\t\\t1677\\t\\t/* 1/exp(2s/10s) as fixed-point */\\n#define EXP_60s\\t\\t1981\\t\\t/* 1/exp(2s/60s) */\\n#define EXP_300s\\t2034\\t\\t/* 1/exp(2s/300s) */\\n\\n/* PSI trigger definitions */\\n#define WINDOW_MAX_US 10000000\\t/* Max window size is 10s */\\n#define UPDATES_PER_WINDOW 10\\t/* 10 updates per window */\\n\\n/* Sampling frequency in nanoseconds */\\nstatic u64 psi_period __read_mostly;\\n\\n/* System-level pressure and stall tracking */\\nstatic DEFINE_PER_CPU(struct psi_group_cpu, system_group_pcpu);\\nstruct psi_group psi_system = {\\n\\t.pcpu = &system_group_pcpu,\\n};\\n\\nstatic void psi_avgs_work(struct work_struct *work);\\n\\nstatic void poll_timer_fn(struct timer_list *t);\\n\\nstatic void group_init(struct psi_group *group)\\n{\\n\\tint cpu;\\n\\n\\tgroup->enabled = true;\\n\\tfor_each_possible_cpu(cpu)\\n\\t\\tseqcount_init(&per_cpu_ptr(group->pcpu, cpu)->seq);\\n\\tgroup->avg_last_update = sched_clock();\\n\\tgroup->avg_next_update = group->avg_last_update + psi_period;\\n\\tmutex_init(&group->avgs_lock);\\n\\n\\t/* Init avg trigger-related members */\\n\\tINIT_LIST_HEAD(&group->avg_triggers);\\n\\tmemset(group->avg_nr_triggers, 0, sizeof(group->avg_nr_triggers));\\n\\tINIT_DELAYED_WORK(&group->avgs_work, psi_avgs_work);\\n\\n\\t/* Init rtpoll trigger-related members */\\n\\tatomic_set(&group->rtpoll_scheduled, 0);\\n\\tmutex_init(&group->rtpoll_trigger_lock);\\n\\tINIT_LIST_HEAD(&group->rtpoll_triggers);\\n\\tgroup->rtpoll_min_period = U32_MAX;\\n\\tgroup->rtpoll_next_update = ULLONG_MAX;\\n\\tinit_waitqueue_head(&group->rtpoll_wait);\\n\\ttimer_setup(&group->rtpoll_timer, poll_timer_fn, 0);\\n\\trcu_assign_pointer(group->rtpoll_task, NULL);\\n}\\n\\nvoid __init psi_init(void)\\n{\\n\\tif (!psi_enable) {\\n\\t\\tstatic_branch_enable(&psi_disabled);\\n\\t\\tstatic_branch_disable(&psi_cgroups_enabled);\\n\\t\\treturn;\\n\\t}\\n\\n\\tif (!cgroup_psi_enabled())\\n\\t\\tstatic_branch_disable(&psi_cgroups_enabled);\\n\\n\\tpsi_period = jiffies_to_nsecs(PSI_FREQ);\\n\\tgroup_init(&psi_system);\\n}\\n\\nstatic u32 test_states(unsigned int *tasks, u32 state_mask)\\n{\\n\\tconst bool oncpu = state_mask & PSI_ONCPU;\\n\\n\\tif (tasks[NR_IOWAIT]) {\\n\\t\\tstate_mask |= BIT(PSI_IO_SOME);\\n\\t\\tif (!tasks[NR_RUNNING])\\n\\t\\t\\tstate_mask |= BIT(PSI_IO_FULL);\\n\\t}\\n\\n\\tif (tasks[NR_MEMSTALL]) {\\n\\t\\tstate_mask |= BIT(PSI_MEM_SOME);\\n\\t\\tif (tasks[NR_RUNNING] == tasks[NR_MEMSTALL_RUNNING])\\n\\t\\t\\tstate_mask |= BIT(PSI_MEM_FULL);\\n\\t}\\n\\n\\tif (tasks[NR_RUNNING] > oncpu)\\n\\t\\tstate_mask |= BIT(PSI_CPU_SOME);\\n\\n\\tif (tasks[NR_RUNNING] && !oncpu)\\n\\t\\tstate_mask |= BIT(PSI_CPU_FULL);\\n\\n\\tif (tasks[NR_IOWAIT] || tasks[NR_MEMSTALL] || tasks[NR_RUNNING])\\n\\t\\tstate_mask |= BIT(PSI_NONIDLE);\\n\\n\\treturn state_mask;\\n}\\n\\nstatic void get_recent_times(struct psi_group *group, int cpu,\\n\\t\\t\\t     enum psi_aggregators aggregator, u32 *times,\\n\\t\\t\\t     u32 *pchanged_states)\\n{\\n\\tstruct psi_group_cpu *groupc = per_cpu_ptr(group->pcpu, cpu);\\n\\tint current_cpu = raw_smp_processor_id();\\n\\tunsigned int tasks[NR_PSI_TASK_COUNTS];\\n\\tu64 now, state_start;\\n\\tenum psi_states s;\\n\\tunsigned int seq;\\n\\tu32 state_mask;\\n\\n\\t*pchanged_states = 0;\\n\\n\\t/* Snapshot a coherent view of the CPU state */\\n\\tdo {\\n\\t\\tseq = read_seqcount_begin(&groupc->seq);\\n\\t\\tnow = cpu_clock(cpu);\\n\\t\\tmemcpy(times, groupc->times, sizeof(groupc->times));\\n\\t\\tstate_mask = groupc->state_mask;\\n\\t\\tstate_start = groupc->state_start;\\n\\t\\tif (cpu == current_cpu)\\n\\t\\t\\tmemcpy(tasks, groupc->tasks, sizeof(groupc->tasks));\\n\\t} while (read_seqcount_retry(&groupc->seq, seq));\\n\\n\\t/* Calculate state time deltas against the previous snapshot */\\n\\tfor (s = 0; s < NR_PSI_STATES; s++) {\\n\\t\\tu32 delta;\\n\\t\\t/*\\n\\t\\t * In addition to already concluded states, we also\\n\\t\\t * incorporate currently active states on the CPU,\\n\\t\\t * since states may last for many sampling periods.\\n\\t\\t *\\n\\t\\t * This way we keep our delta sampling buckets small\\n\\t\\t * (u32) and our reported pressure close to what's\\n\\t\\t * actually happening.\\n\\t\\t */\\n\\t\\tif (state_mask & (1 << s))\\n\\t\\t\\ttimes[s] += now - state_start;\\n\\n\\t\\tdelta = times[s] - groupc->times_prev[aggregator][s];\\n\\t\\tgroupc->times_prev[aggregator][s] = times[s];\\n\\n\\t\\ttimes[s] = delta;\\n\\t\\tif (delta)\\n\\t\\t\\t*pchanged_states |= (1 << s);\\n\\t}\\n\\n\\t/*\\n\\t * When collect_percpu_times() from the avgs_work, we don't want to\\n\\t * re-arm avgs_work when all CPUs are IDLE. But the current CPU running\\n\\t * this avgs_work is never IDLE, cause avgs_work can't be shut off.\\n\\t * So for the current CPU, we need to re-arm avgs_work only when\\n\\t * (NR_RUNNING > 1 || NR_IOWAIT > 0 || NR_MEMSTALL > 0), for other CPUs\\n\\t * we can just check PSI_NONIDLE delta.\\n\\t */\\n\\tif (current_work() == &group->avgs_work.work) {\\n\\t\\tbool reschedule;\\n\\n\\t\\tif (cpu == current_cpu)\\n\\t\\t\\treschedule = tasks[NR_RUNNING] +\\n\\t\\t\\t\\t     tasks[NR_IOWAIT] +\\n\\t\\t\\t\\t     tasks[NR_MEMSTALL] > 1;\\n\\t\\telse\\n\\t\\t\\treschedule = *pchanged_states & (1 << PSI_NONIDLE);\\n\\n\\t\\tif (reschedule)\\n\\t\\t\\t*pchanged_states |= PSI_STATE_RESCHEDULE;\\n\\t}\\n}\\n\\nstatic void calc_avgs(unsigned long avg[3], int missed_periods,\\n\\t\\t      u64 time, u64 period)\\n{\\n\\tunsigned long pct;\\n\\n\\t/* Fill in zeroes for periods of no activity */\\n\\tif (missed_periods) {\\n\\t\\tavg[0] = calc_load_n(avg[0], EXP_10s, 0, missed_periods);\\n\\t\\tavg[1] = calc_load_n(avg[1], EXP_60s, 0, missed_periods);\\n\\t\\tavg[2] = calc_load_n(avg[2], EXP_300s, 0, missed_periods);\\n\\t}\\n\\n\\t/* Sample the most recent active period */\\n\\tpct = div_u64(time * 100, period);\\n\\tpct *= FIXED_1;\\n\\tavg[0] = calc_load(avg[0], EXP_10s, pct);\\n\\tavg[1] = calc_load(avg[1], EXP_60s, pct);\\n\\tavg[2] = calc_load(avg[2], EXP_300s, pct);\\n}\\n\\nstatic void collect_percpu_times(struct psi_group *group,\\n\\t\\t\\t\\t enum psi_aggregators aggregator,\\n\\t\\t\\t\\t u32 *pchanged_states)\\n{\\n\\tu64 deltas[NR_PSI_STATES - 1] = { 0, };\\n\\tunsigned long nonidle_total = 0;\\n\\tu32 changed_states = 0;\\n\\tint cpu;\\n\\tint s;\\n\\n\\t/*\\n\\t * Collect the per-cpu time buckets and average them into a\\n\\t * single time sample that is normalized to wall clock time.\\n\\t *\\n\\t * For averaging, each CPU is weighted by its non-idle time in\\n\\t * the sampling period. This eliminates artifacts from uneven\\n\\t * loading, or even entirely idle CPUs.\\n\\t */\\n\\tfor_each_possible_cpu(cpu) {\\n\\t\\tu32 times[NR_PSI_STATES];\\n\\t\\tu32 nonidle;\\n\\t\\tu32 cpu_changed_states;\\n\\n\\t\\tget_recent_times(group, cpu, aggregator, times,\\n\\t\\t\\t\\t&cpu_changed_states);\\n\\t\\tchanged_states |= cpu_changed_states;\\n\\n\\t\\tnonidle = nsecs_to_jiffies(times[PSI_NONIDLE]);\\n\\t\\tnonidle_total += nonidle;\\n\\n\\t\\tfor (s = 0; s < PSI_NONIDLE; s++)\\n\\t\\t\\tdeltas[s] += (u64)times[s] * nonidle;\\n\\t}\\n\\n\\t/*\\n\\t * Integrate the sample into the running statistics that are\\n\\t * reported to userspace: the cumulative stall times and the\\n\\t * decaying averages.\\n\\t *\\n\\t * Pressure percentages are sampled at PSI_FREQ. We might be\\n\\t * called more often when the user polls more frequently than\\n\\t * that; we might be called less often when there is no task\\n\\t * activity, thus no data, and clock ticks are sporadic. The\\n\\t * below handles both.\\n\\t */\\n\\n\\t/* total= */\\n\\tfor (s = 0; s < NR_PSI_STATES - 1; s++)\\n\\t\\tgroup->total[aggregator][s] +=\\n\\t\\t\\t\\tdiv_u64(deltas[s], max(nonidle_total, 1UL));\\n\\n\\tif (pchanged_states)\\n\\t\\t*pchanged_states = changed_states;\\n}\\n\\n/* Trigger tracking window manipulations */\\nstatic void window_reset(struct psi_window *win, u64 now, u64 value,\\n\\t\\t\\t u64 prev_growth)\\n{\\n\\twin->start_time = now;\\n\\twin->start_value = value;\\n\\twin->prev_growth = prev_growth;\\n}\\n\\n/*\\n * PSI growth tracking window update and growth calculation routine.\\n *\\n * This approximates a sliding tracking window by interpolating\\n * partially elapsed windows using historical growth data from the\\n * previous intervals. This minimizes memory requirements (by not storing\\n * all the intermediate values in the previous window) and simplifies\\n * the calculations. It works well because PSI signal changes only in\\n * positive direction and over relatively small window sizes the growth\\n * is close to linear.\\n */\\nstatic u64 window_update(struct psi_window *win, u64 now, u64 value)\\n{\\n\\tu64 elapsed;\\n\\tu64 growth;\\n\\n\\telapsed = now - win->start_time;\\n\\tgrowth = value - win->start_value;\\n\\t/*\\n\\t * After each tracking window passes win->start_value and\\n\\t * win->start_time get reset and win->prev_growth stores\\n\\t * the average per-window growth of the previous window.\\n\\t * win->prev_growth is then used to interpolate additional\\n\\t * growth from the previous window assuming it was linear.\\n\\t */\\n\\tif (elapsed > win->size)\\n\\t\\twindow_reset(win, now, value, growth);\\n\\telse {\\n\\t\\tu32 remaining;\\n\\n\\t\\tremaining = win->size - elapsed;\\n\\t\\tgrowth += div64_u64(win->prev_growth * remaining, win->size);\\n\\t}\\n\\n\\treturn growth;\\n}\\n\\nstatic void update_triggers(struct psi_group *group, u64 now,\\n\\t\\t\\t\\t\\t\\t   enum psi_aggregators aggregator)\\n{\\n\\tstruct psi_trigger *t;\\n\\tu64 *total = group->total[aggregator];\\n\\tstruct list_head *triggers;\\n\\tu64 *aggregator_total;\\n\\n\\tif (aggregator == PSI_AVGS) {\\n\\t\\ttriggers = &group->avg_triggers;\\n\\t\\taggregator_total = group->avg_total;\\n\\t} else {\\n\\t\\ttriggers = &group->rtpoll_triggers;\\n\\t\\taggregator_total = group->rtpoll_total;\\n\\t}\\n\\n\\t/*\\n\\t * On subsequent updates, calculate growth deltas and let\\n\\t * watchers know when their specified thresholds are exceeded.\\n\\t */\\n\\tlist_for_each_entry(t, triggers, node) {\\n\\t\\tu64 growth;\\n\\t\\tbool new_stall;\\n\\n\\t\\tnew_stall = aggregator_total[t->state] != total[t->state];\\n\\n\\t\\t/* Check for stall activity or a previous threshold breach */\\n\\t\\tif (!new_stall && !t->pending_event)\\n\\t\\t\\tcontinue;\\n\\t\\t/*\\n\\t\\t * Check for new stall activity, as well as deferred\\n\\t\\t * events that occurred in the last window after the\\n\\t\\t * trigger had already fired (we want to ratelimit\\n\\t\\t * events without dropping any).\\n\\t\\t */\\n\\t\\tif (new_stall) {\\n\\t\\t\\t/* Calculate growth since last update */\\n\\t\\t\\tgrowth = window_update(&t->win, now, total[t->state]);\\n\\t\\t\\tif (!t->pending_event) {\\n\\t\\t\\t\\tif (growth < t->threshold)\\n\\t\\t\\t\\t\\tcontinue;\\n\\n\\t\\t\\t\\tt->pending_event = true;\\n\\t\\t\\t}\\n\\t\\t}\\n\\t\\t/* Limit event signaling to once per window */\\n\\t\\tif (now < t->last_event_time + t->win.size)\\n\\t\\t\\tcontinue;\\n\\n\\t\\t/* Generate an event */\\n\\t\\tif (cmpxchg(&t->event, 0, 1) == 0) {\\n\\t\\t\\tif (t->of)\\n\\t\\t\\t\\tkernfs_notify(t->of->kn);\\n\\t\\t\\telse\\n\\t\\t\\t\\twake_up_interruptible(&t->event_wait);\\n\\t\\t}\\n\\t\\tt->last_event_time = now;\\n\\t\\t/* Reset threshold breach flag once event got generated */\\n\\t\\tt->pending_event = false;\\n\\t}\\n}\\n\\nstatic u64 update_averages(struct psi_group *group, u64 now)\\n{\\n\\tunsigned long missed_periods = 0;\\n\\tu64 expires, period;\\n\\tu64 avg_next_update;\\n\\tint s;\\n\\n\\t/* avgX= */\\n\\texpires = group->avg_next_update;\\n\\tif (now - expires >= psi_period)\\n\\t\\tmissed_periods = div_u64(now - expires, psi_period);\\n\\n\\t/*\\n\\t * The periodic clock tick can get delayed for various\\n\\t * reasons, especially on loaded systems. To avoid clock\\n\\t * drift, we schedule the clock in fixed psi_period intervals.\\n\\t * But the deltas we sample out of the per-cpu buckets above\\n\\t * are based on the actual time elapsing between clock ticks.\\n\\t */\\n\\tavg_next_update = expires + ((1 + missed_periods) * psi_period);\\n\\tperiod = now - (group->avg_last_update + (missed_periods * psi_period));\\n\\tgroup->avg_last_update = now;\\n\\n\\tfor (s = 0; s < NR_PSI_STATES - 1; s++) {\\n\\t\\tu32 sample;\\n\\n\\t\\tsample = group->total[PSI_AVGS][s] - group->avg_total[s];\\n\\t\\t/*\\n\\t\\t * Due to the lockless sampling of the time buckets,\\n\\t\\t * recorded time deltas can slip into the next period,\\n\\t\\t * which under full pressure can result in samples in\\n\\t\\t * excess of the period length.\\n\\t\\t *\\n\\t\\t * We don't want to report non-sensical pressures in\\n\\t\\t * excess of 100%, nor do we want to drop such events\\n\\t\\t * on the floor. Instead we punt any overage into the\\n\\t\\t * future until pressure subsides. By doing this we\\n\\t\\t * don't underreport the occurring pressure curve, we\\n\\t\\t * just report it delayed by one period length.\\n\\t\\t *\\n\\t\\t * The error isn't cumulative. As soon as another\\n\\t\\t * delta slips from a period P to P+1, by definition\\n\\t\\t * it frees up its time T in P.\\n\\t\\t */\\n\\t\\tif (sample > period)\\n\\t\\t\\tsample = period;\\n\\t\\tgroup->avg_total[s] += sample;\\n\\t\\tcalc_avgs(group->avg[s], missed_periods, sample, period);\\n\\t}\\n\\n\\treturn avg_next_update;\\n}\\n\\nstatic void psi_avgs_work(struct work_struct *work)\\n{\\n\\tstruct delayed_work *dwork;\\n\\tstruct psi_group *group;\\n\\tu32 changed_states;\\n\\tu64 now;\\n\\n\\tdwork = to_delayed_work(work);\\n\\tgroup = container_of(dwork, struct psi_group, avgs_work);\\n\\n\\tmutex_lock(&group->avgs_lock);\\n\\n\\tnow = sched_clock();\\n\\n\\tcollect_percpu_times(group, PSI_AVGS, &changed_states);\\n\\t/*\\n\\t * If there is task activity, periodically fold the per-cpu\\n\\t * times and feed samples into the running averages. If things\\n\\t * are idle and there is no data to process, stop the clock.\\n\\t * Once restarted, we'll catch up the running averages in one\\n\\t * go - see calc_avgs() and missed_periods.\\n\\t */\\n\\tif (now >= group->avg_next_update) {\\n\\t\\tupdate_triggers(group, now, PSI_AVGS);\\n\\t\\tgroup->avg_next_update = update_averages(group, now);\\n\\t}\\n\\n\\tif (changed_states & PSI_STATE_RESCHEDULE) {\\n\\t\\tschedule_delayed_work(dwork, nsecs_to_jiffies(\\n\\t\\t\\t\\tgroup->avg_next_update - now) + 1);\\n\\t}\\n\\n\\tmutex_unlock(&group->avgs_lock);\\n}\\n\\nstatic void init_rtpoll_triggers(struct psi_group *group, u64 now)\\n{\\n\\tstruct psi_trigger *t;\\n\\n\\tlist_for_each_entry(t, &group->rtpoll_triggers, node)\\n\\t\\twindow_reset(&t->win, now,\\n\\t\\t\\t\\tgroup->total[PSI_POLL][t->state], 0);\\n\\tmemcpy(group->rtpoll_total, group->total[PSI_POLL],\\n\\t\\t   sizeof(group->rtpoll_total));\\n\\tgroup->rtpoll_next_update = now + group->rtpoll_min_period;\\n}\\n\\n/* Schedule rtpolling if it's not already scheduled or forced. */\\nstatic void psi_schedule_rtpoll_work(struct psi_group *group, unsigned long delay,\\n\\t\\t\\t\\t   bool force)\\n{\\n\\tstruct task_struct *task;\\n\\n\\t/*\\n\\t * atomic_xchg should be called even when !force to provide a\\n\\t * full memory barrier (see the comment inside psi_rtpoll_work).\\n\\t */\\n\\tif (atomic_xchg(&group->rtpoll_scheduled, 1) && !force)\\n\\t\\treturn;\\n\\n\\trcu_read_lock();\\n\\n\\ttask = rcu_dereference(group->rtpoll_task);\\n\\t/*\\n\\t * kworker might be NULL in case psi_trigger_destroy races with\\n\\t * psi_task_change (hotpath) which can't use locks\\n\\t */\\n\\tif (likely(task))\\n\\t\\tmod_timer(&group->rtpoll_timer, jiffies + delay);\\n\\telse\\n\\t\\tatomic_set(&group->rtpoll_scheduled, 0);\\n\\n\\trcu_read_unlock();\\n}\\n\\nstatic void psi_rtpoll_work(struct psi_group *group)\\n{\\n\\tbool force_reschedule = false;\\n\\tu32 changed_states;\\n\\tu64 now;\\n\\n\\tmutex_lock(&group->rtpoll_trigger_lock);\\n\\n\\tnow = sched_clock();\\n\\n\\tif (now > group->rtpoll_until) {\\n\\t\\t/*\\n\\t\\t * We are either about to start or might stop rtpolling if no\\n\\t\\t * state change was recorded. Resetting rtpoll_scheduled leaves\\n\\t\\t * a small window for psi_group_change to sneak in and schedule\\n\\t\\t * an immediate rtpoll_work before we get to rescheduling. One\\n\\t\\t * potential extra wakeup at the end of the rtpolling window\\n\\t\\t * should be negligible and rtpoll_next_update still keeps\\n\\t\\t * updates correctly on schedule.\\n\\t\\t */\\n\\t\\tatomic_set(&group->rtpoll_scheduled, 0);\\n\\t\\t/*\\n\\t\\t * A task change can race with the rtpoll worker that is supposed to\\n\\t\\t * report on it. To avoid missing events, ensure ordering between\\n\\t\\t * rtpoll_scheduled and the task state accesses, such that if the\\n\\t\\t * rtpoll worker misses the state update, the task change is\\n\\t\\t * guaranteed to reschedule the rtpoll worker:\\n\\t\\t *\\n\\t\\t * rtpoll worker:\\n\\t\\t *   atomic_set(rtpoll_scheduled, 0)\\n\\t\\t *   smp_mb()\\n\\t\\t *   LOAD states\\n\\t\\t *\\n\\t\\t * task change:\\n\\t\\t *   STORE states\\n\\t\\t *   if atomic_xchg(rtpoll_scheduled, 1) == 0:\\n\\t\\t *     schedule rtpoll worker\\n\\t\\t *\\n\\t\\t * The atomic_xchg() implies a full barrier.\\n\\t\\t */\\n\\t\\tsmp_mb();\\n\\t} else {\\n\\t\\t/* The rtpolling window is not over, keep rescheduling */\\n\\t\\tforce_reschedule = true;\\n\\t}\\n\\n\\n\\tcollect_percpu_times(group, PSI_POLL, &changed_states);\\n\\n\\tif (changed_states & group->rtpoll_states) {\\n\\t\\t/* Initialize trigger windows when entering rtpolling mode */\\n\\t\\tif (now > group->rtpoll_until)\\n\\t\\t\\tinit_rtpoll_triggers(group, now);\\n\\n\\t\\t/*\\n\\t\\t * Keep the monitor active for at least the duration of the\\n\\t\\t * minimum tracking window as long as monitor states are\\n\\t\\t * changing.\\n\\t\\t */\\n\\t\\tgroup->rtpoll_until = now +\\n\\t\\t\\tgroup->rtpoll_min_period * UPDATES_PER_WINDOW;\\n\\t}\\n\\n\\tif (now > group->rtpoll_until) {\\n\\t\\tgroup->rtpoll_next_update = ULLONG_MAX;\\n\\t\\tgoto out;\\n\\t}\\n\\n\\tif (now >= group->rtpoll_next_update) {\\n\\t\\tif (changed_states & group->rtpoll_states) {\\n\\t\\t\\tupdate_triggers(group, now, PSI_POLL);\\n\\t\\t\\tmemcpy(group->rtpoll_total, group->total[PSI_POLL],\\n\\t\\t\\t\\t   sizeof(group->rtpoll_total));\\n\\t\\t}\\n\\t\\tgroup->rtpoll_next_update = now + group->rtpoll_min_period;\\n\\t}\\n\\n\\tpsi_schedule_rtpoll_work(group,\\n\\t\\tnsecs_to_jiffies(group->rtpoll_next_update - now) + 1,\\n\\t\\tforce_reschedule);\\n\\nout:\\n\\tmutex_unlock(&group->rtpoll_trigger_lock);\\n}\\n\\nstatic int psi_rtpoll_worker(void *data)\\n{\\n\\tstruct psi_group *group = (struct psi_group *)data;\\n\\n\\tsched_set_fifo_low(current);\\n\\n\\twhile (true) {\\n\\t\\twait_event_interruptible(group->rtpoll_wait,\\n\\t\\t\\t\\tatomic_cmpxchg(&group->rtpoll_wakeup, 1, 0) ||\\n\\t\\t\\t\\tkthread_should_stop());\\n\\t\\tif (kthread_should_stop())\\n\\t\\t\\tbreak;\\n\\n\\t\\tpsi_rtpoll_work(group);\\n\\t}\\n\\treturn 0;\\n}\\n\\nstatic void poll_timer_fn(struct timer_list *t)\\n{\\n\\tstruct psi_group *group = from_timer(group, t, rtpoll_timer);\\n\\n\\tatomic_set(&group->rtpoll_wakeup, 1);\\n\\twake_up_interruptible(&group->rtpoll_wait);\\n}\\n\\nstatic void record_times(struct psi_group_cpu *groupc, u64 now)\\n{\\n\\tu32 delta;\\n\\n\\tdelta = now - groupc->state_start;\\n\\tgroupc->state_start = now;\\n\\n\\tif (groupc->state_mask & (1 << PSI_IO_SOME)) {\\n\\t\\tgroupc->times[PSI_IO_SOME] += delta;\\n\\t\\tif (groupc->state_mask & (1 << PSI_IO_FULL))\\n\\t\\t\\tgroupc->times[PSI_IO_FULL] += delta;\\n\\t}\\n\\n\\tif (groupc->state_mask & (1 << PSI_MEM_SOME)) {\\n\\t\\tgroupc->times[PSI_MEM_SOME] += delta;\\n\\t\\tif (groupc->state_mask & (1 << PSI_MEM_FULL))\\n\\t\\t\\tgroupc->times[PSI_MEM_FULL] += delta;\\n\\t}\\n\\n\\tif (groupc->state_mask & (1 << PSI_CPU_SOME)) {\\n\\t\\tgroupc->times[PSI_CPU_SOME] += delta;\\n\\t\\tif (groupc->state_mask & (1 << PSI_CPU_FULL))\\n\\t\\t\\tgroupc->times[PSI_CPU_FULL] += delta;\\n\\t}\\n\\n\\tif (groupc->state_mask & (1 << PSI_NONIDLE))\\n\\t\\tgroupc->times[PSI_NONIDLE] += delta;\\n}\\n\\nstatic void psi_group_change(struct psi_group *group, int cpu,\\n\\t\\t\\t     unsigned int clear, unsigned int set,\\n\\t\\t\\t     bool wake_clock)\\n{\\n\\tstruct psi_group_cpu *groupc;\\n\\tunsigned int t, m;\\n\\tu32 state_mask;\\n\\tu64 now;\\n\\n\\tlockdep_assert_rq_held(cpu_rq(cpu));\\n\\tgroupc = per_cpu_ptr(group->pcpu, cpu);\\n\\n\\t/*\\n\\t * First we update the task counts according to the state\\n\\t * change requested through the @clear and @set bits.\\n\\t *\\n\\t * Then if the cgroup PSI stats accounting enabled, we\\n\\t * assess the aggregate resource states this CPU's tasks\\n\\t * have been in since the last change, and account any\\n\\t * SOME and FULL time these may have resulted in.\\n\\t */\\n\\twrite_seqcount_begin(&groupc->seq);\\n\\tnow = cpu_clock(cpu);\\n\\n\\t/*\\n\\t * Start with TSK_ONCPU, which doesn't have a corresponding\\n\\t * task count - it's just a boolean flag directly encoded in\\n\\t * the state mask. Clear, set, or carry the current state if\\n\\t * no changes are requested.\\n\\t */\\n\\tif (unlikely(clear & TSK_ONCPU)) {\\n\\t\\tstate_mask = 0;\\n\\t\\tclear &= ~TSK_ONCPU;\\n\\t} else if (unlikely(set & TSK_ONCPU)) {\\n\\t\\tstate_mask = PSI_ONCPU;\\n\\t\\tset &= ~TSK_ONCPU;\\n\\t} else {\\n\\t\\tstate_mask = groupc->state_mask & PSI_ONCPU;\\n\\t}\\n\\n\\t/*\\n\\t * The rest of the state mask is calculated based on the task\\n\\t * counts. Update those first, then construct the mask.\\n\\t */\\n\\tfor (t = 0, m = clear; m; m &= ~(1 << t), t++) {\\n\\t\\tif (!(m & (1 << t)))\\n\\t\\t\\tcontinue;\\n\\t\\tif (groupc->tasks[t]) {\\n\\t\\t\\tgroupc->tasks[t]--;\\n\\t\\t} else if (!psi_bug) {\\n\\t\\t\\tprintk_deferred(KERN_ERR \\\"psi: task underflow! cpu=%d t=%d tasks=[%u %u %u %u] clear=%x set=%x\\\\n\\\",\\n\\t\\t\\t\\t\\tcpu, t, groupc->tasks[0],\\n\\t\\t\\t\\t\\tgroupc->tasks[1], groupc->tasks[2],\\n\\t\\t\\t\\t\\tgroupc->tasks[3], clear, set);\\n\\t\\t\\tpsi_bug = 1;\\n\\t\\t}\\n\\t}\\n\\n\\tfor (t = 0; set; set &= ~(1 << t), t++)\\n\\t\\tif (set & (1 << t))\\n\\t\\t\\tgroupc->tasks[t]++;\\n\\n\\tif (!group->enabled) {\\n\\t\\t/*\\n\\t\\t * On the first group change after disabling PSI, conclude\\n\\t\\t * the current state and flush its time. This is unlikely\\n\\t\\t * to matter to the user, but aggregation (get_recent_times)\\n\\t\\t * may have already incorporated the live state into times_prev;\\n\\t\\t * avoid a delta sample underflow when PSI is later re-enabled.\\n\\t\\t */\\n\\t\\tif (unlikely(groupc->state_mask & (1 << PSI_NONIDLE)))\\n\\t\\t\\trecord_times(groupc, now);\\n\\n\\t\\tgroupc->state_mask = state_mask;\\n\\n\\t\\twrite_seqcount_end(&groupc->seq);\\n\\t\\treturn;\\n\\t}\\n\\n\\tstate_mask = test_states(groupc->tasks, state_mask);\\n\\n\\t/*\\n\\t * Since we care about lost potential, a memstall is FULL\\n\\t * when there are no other working tasks, but also when\\n\\t * the CPU is actively reclaiming and nothing productive\\n\\t * could run even if it were runnable. So when the current\\n\\t * task in a cgroup is in_memstall, the corresponding groupc\\n\\t * on that cpu is in PSI_MEM_FULL state.\\n\\t */\\n\\tif (unlikely((state_mask & PSI_ONCPU) && cpu_curr(cpu)->in_memstall))\\n\\t\\tstate_mask |= (1 << PSI_MEM_FULL);\\n\\n\\trecord_times(groupc, now);\\n\\n\\tgroupc->state_mask = state_mask;\\n\\n\\twrite_seqcount_end(&groupc->seq);\\n\\n\\tif (state_mask & group->rtpoll_states)\\n\\t\\tpsi_schedule_rtpoll_work(group, 1, false);\\n\\n\\tif (wake_clock && !delayed_work_pending(&group->avgs_work))\\n\\t\\tschedule_delayed_work(&group->avgs_work, PSI_FREQ);\\n}\\n\\nstatic inline struct psi_group *task_psi_group(struct task_struct *task)\\n{\\n#ifdef CONFIG_CGROUPS\\n\\tif (static_branch_likely(&psi_cgroups_enabled))\\n\\t\\treturn cgroup_psi(task_dfl_cgroup(task));\\n#endif\\n\\treturn &psi_system;\\n}\\n\\nstatic void psi_flags_change(struct task_struct *task, int clear, int set)\\n{\\n\\tif (((task->psi_flags & set) ||\\n\\t     (task->psi_flags & clear) != clear) &&\\n\\t    !psi_bug) {\\n\\t\\tprintk_deferred(KERN_ERR \\\"psi: inconsistent task state! task=%d:%s cpu=%d psi_flags=%x clear=%x set=%x\\\\n\\\",\\n\\t\\t\\t\\ttask->pid, task->comm, task_cpu(task),\\n\\t\\t\\t\\ttask->psi_flags, clear, set);\\n\\t\\tpsi_bug = 1;\\n\\t}\\n\\n\\ttask->psi_flags &= ~clear;\\n\\ttask->psi_flags |= set;\\n}\\n\\nvoid psi_task_change(struct task_struct *task, int clear, int set)\\n{\\n\\tint cpu = task_cpu(task);\\n\\tstruct psi_group *group;\\n\\n\\tif (!task->pid)\\n\\t\\treturn;\\n\\n\\tpsi_flags_change(task, clear, set);\\n\\n\\tgroup = task_psi_group(task);\\n\\tdo {\\n\\t\\tpsi_group_change(group, cpu, clear, set, true);\\n\\t} while ((group = group->parent));\\n}\\n\\nvoid psi_task_switch(struct task_struct *prev, struct task_struct *next,\\n\\t\\t     bool sleep)\\n{\\n\\tstruct psi_group *group, *common = NULL;\\n\\tint cpu = task_cpu(prev);\\n\\n\\tif (next->pid) {\\n\\t\\tpsi_flags_change(next, 0, TSK_ONCPU);\\n\\t\\t/*\\n\\t\\t * Set TSK_ONCPU on @next's cgroups. If @next shares any\\n\\t\\t * ancestors with @prev, those will already have @prev's\\n\\t\\t * TSK_ONCPU bit set, and we can stop the iteration there.\\n\\t\\t */\\n\\t\\tgroup = task_psi_group(next);\\n\\t\\tdo {\\n\\t\\t\\tif (per_cpu_ptr(group->pcpu, cpu)->state_mask &\\n\\t\\t\\t    PSI_ONCPU) {\\n\\t\\t\\t\\tcommon = group;\\n\\t\\t\\t\\tbreak;\\n\\t\\t\\t}\\n\\n\\t\\t\\tpsi_group_change(group, cpu, 0, TSK_ONCPU, true);\\n\\t\\t} while ((group = group->parent));\\n\\t}\\n\\n\\tif (prev->pid) {\\n\\t\\tint clear = TSK_ONCPU, set = 0;\\n\\t\\tbool wake_clock = true;\\n\\n\\t\\t/*\\n\\t\\t * When we're going to sleep, psi_dequeue() lets us\\n\\t\\t * handle TSK_RUNNING, TSK_MEMSTALL_RUNNING and\\n\\t\\t * TSK_IOWAIT here, where we can combine it with\\n\\t\\t * TSK_ONCPU and save walking common ancestors twice.\\n\\t\\t */\\n\\t\\tif (sleep) {\\n\\t\\t\\tclear |= TSK_RUNNING;\\n\\t\\t\\tif (prev->in_memstall)\\n\\t\\t\\t\\tclear |= TSK_MEMSTALL_RUNNING;\\n\\t\\t\\tif (prev->in_iowait)\\n\\t\\t\\t\\tset |= TSK_IOWAIT;\\n\\n\\t\\t\\t/*\\n\\t\\t\\t * Periodic aggregation shuts off if there is a period of no\\n\\t\\t\\t * task changes, so we wake it back up if necessary. However,\\n\\t\\t\\t * don't do this if the task change is the aggregation worker\\n\\t\\t\\t * itself going to sleep, or we'll ping-pong forever.\\n\\t\\t\\t */\\n\\t\\t\\tif (unlikely((prev->flags & PF_WQ_WORKER) &&\\n\\t\\t\\t\\t     wq_worker_last_func(prev) == psi_avgs_work))\\n\\t\\t\\t\\twake_clock = false;\\n\\t\\t}\\n\\n\\t\\tpsi_flags_change(prev, clear, set);\\n\\n\\t\\tgroup = task_psi_group(prev);\\n\\t\\tdo {\\n\\t\\t\\tif (group == common)\\n\\t\\t\\t\\tbreak;\\n\\t\\t\\tpsi_group_change(group, cpu, clear, set, wake_clock);\\n\\t\\t} while ((group = group->parent));\\n\\n\\t\\t/*\\n\\t\\t * TSK_ONCPU is handled up to the common ancestor. If there are\\n\\t\\t * any other differences between the two tasks (e.g. prev goes\\n\\t\\t * to sleep, or only one task is memstall), finish propagating\\n\\t\\t * those differences all the way up to the root.\\n\\t\\t */\\n\\t\\tif ((prev->psi_flags ^ next->psi_flags) & ~TSK_ONCPU) {\\n\\t\\t\\tclear &= ~TSK_ONCPU;\\n\\t\\t\\tfor (; group; group = group->parent)\\n\\t\\t\\t\\tpsi_group_change(group, cpu, clear, set, wake_clock);\\n\\t\\t}\\n\\t}\\n}\\n\\n#ifdef CONFIG_IRQ_TIME_ACCOUNTING\\nvoid psi_account_irqtime(struct rq *rq, struct task_struct *curr, struct task_struct *prev)\\n{\\n\\tint cpu = task_cpu(curr);\\n\\tstruct psi_group *group;\\n\\tstruct psi_group_cpu *groupc;\\n\\ts64 delta;\\n\\tu64 irq;\\n\\n\\tif (static_branch_likely(&psi_disabled))\\n\\t\\treturn;\\n\\n\\tif (!curr->pid)\\n\\t\\treturn;\\n\\n\\tlockdep_assert_rq_held(rq);\\n\\tgroup = task_psi_group(curr);\\n\\tif (prev && task_psi_group(prev) == group)\\n\\t\\treturn;\\n\\n\\tirq = irq_time_read(cpu);\\n\\tdelta = (s64)(irq - rq->psi_irq_time);\\n\\tif (delta < 0)\\n\\t\\treturn;\\n\\trq->psi_irq_time = irq;\\n\\n\\tdo {\\n\\t\\tu64 now;\\n\\n\\t\\tif (!group->enabled)\\n\\t\\t\\tcontinue;\\n\\n\\t\\tgroupc = per_cpu_ptr(group->pcpu, cpu);\\n\\n\\t\\twrite_seqcount_begin(&groupc->seq);\\n\\t\\tnow = cpu_clock(cpu);\\n\\n\\t\\trecord_times(groupc, now);\\n\\t\\tgroupc->times[PSI_IRQ_FULL] += delta;\\n\\n\\t\\twrite_seqcount_end(&groupc->seq);\\n\\n\\t\\tif (group->rtpoll_states & (1 << PSI_IRQ_FULL))\\n\\t\\t\\tpsi_schedule_rtpoll_work(group, 1, false);\\n\\t} while ((group = group->parent));\\n}\\n#endif\\n\\n/**\\n * psi_memstall_enter - mark the beginning of a memory stall section\\n * @flags: flags to handle nested sections\\n *\\n * Marks the calling task as being stalled due to a lack of memory,\\n * such as waiting for a refault or performing reclaim.\\n */\\nvoid psi_memstall_enter(unsigned long *flags)\\n{\\n\\tstruct rq_flags rf;\\n\\tstruct rq *rq;\\n\\n\\tif (static_branch_likely(&psi_disabled))\\n\\t\\treturn;\\n\\n\\t*flags = current->in_memstall;\\n\\tif (*flags)\\n\\t\\treturn;\\n\\t/*\\n\\t * in_memstall setting & accounting needs to be atomic wrt\\n\\t * changes to the task's scheduling state, otherwise we can\\n\\t * race with CPU migration.\\n\\t */\\n\\trq = this_rq_lock_irq(&rf);\\n\\n\\tcurrent->in_memstall = 1;\\n\\tpsi_task_change(current, 0, TSK_MEMSTALL | TSK_MEMSTALL_RUNNING);\\n\\n\\trq_unlock_irq(rq, &rf);\\n}\\nEXPORT_SYMBOL_GPL(psi_memstall_enter);\\n\\n/**\\n * psi_memstall_leave - mark the end of an memory stall section\\n * @flags: flags to handle nested memdelay sections\\n *\\n * Marks the calling task as no longer stalled due to lack of memory.\\n */\\nvoid psi_memstall_leave(unsigned long *flags)\\n{\\n\\tstruct rq_flags rf;\\n\\tstruct rq *rq;\\n\\n\\tif (static_branch_likely(&psi_disabled))\\n\\t\\treturn;\\n\\n\\tif (*flags)\\n\\t\\treturn;\\n\\t/*\\n\\t * in_memstall clearing & accounting needs to be atomic wrt\\n\\t * changes to the task's scheduling state, otherwise we could\\n\\t * race with CPU migration.\\n\\t */\\n\\trq = this_rq_lock_irq(&rf);\\n\\n\\tcurrent->in_memstall = 0;\\n\\tpsi_task_change(current, TSK_MEMSTALL | TSK_MEMSTALL_RUNNING, 0);\\n\\n\\trq_unlock_irq(rq, &rf);\\n}\\nEXPORT_SYMBOL_GPL(psi_memstall_leave);\\n\\n#ifdef CONFIG_CGROUPS\\nint psi_cgroup_alloc(struct cgroup *cgroup)\\n{\\n\\tif (!static_branch_likely(&psi_cgroups_enabled))\\n\\t\\treturn 0;\\n\\n\\tcgroup->psi = kzalloc(sizeof(struct psi_group), GFP_KERNEL);\\n\\tif (!cgroup->psi)\\n\\t\\treturn -ENOMEM;\\n\\n\\tcgroup->psi->pcpu = alloc_percpu(struct psi_group_cpu);\\n\\tif (!cgroup->psi->pcpu) {\\n\\t\\tkfree(cgroup->psi);\\n\\t\\treturn -ENOMEM;\\n\\t}\\n\\tgroup_init(cgroup->psi);\\n\\tcgroup->psi->parent = cgroup_psi(cgroup_parent(cgroup));\\n\\treturn 0;\\n}\\n\\nvoid psi_cgroup_free(struct cgroup *cgroup)\\n{\\n\\tif (!static_branch_likely(&psi_cgroups_enabled))\\n\\t\\treturn;\\n\\n\\tcancel_delayed_work_sync(&cgroup->psi->avgs_work);\\n\\tfree_percpu(cgroup->psi->pcpu);\\n\\t/* All triggers must be removed by now */\\n\\tWARN_ONCE(cgroup->psi->rtpoll_states, \\\"psi: trigger leak\\\\n\\\");\\n\\tkfree(cgroup->psi);\\n}\\n\\n/**\\n * cgroup_move_task - move task to a different cgroup\\n * @task: the task\\n * @to: the target css_set\\n *\\n * Move task to a new cgroup and safely migrate its associated stall\\n * state between the different groups.\\n *\\n * This function acquires the task's rq lock to lock out concurrent\\n * changes to the task's scheduling state and - in case the task is\\n * running - concurrent changes to its stall state.\\n */\\nvoid cgroup_move_task(struct task_struct *task, struct css_set *to)\\n{\\n\\tunsigned int task_flags;\\n\\tstruct rq_flags rf;\\n\\tstruct rq *rq;\\n\\n\\tif (!static_branch_likely(&psi_cgroups_enabled)) {\\n\\t\\t/*\\n\\t\\t * Lame to do this here, but the scheduler cannot be locked\\n\\t\\t * from the outside, so we move cgroups from inside sched/.\\n\\t\\t */\\n\\t\\trcu_assign_pointer(task->cgroups, to);\\n\\t\\treturn;\\n\\t}\\n\\n\\trq = task_rq_lock(task, &rf);\\n\\n\\t/*\\n\\t * We may race with schedule() dropping the rq lock between\\n\\t * deactivating prev and switching to next. Because the psi\\n\\t * updates from the deactivation are deferred to the switch\\n\\t * callback to save cgroup tree updates, the task's scheduling\\n\\t * state here is not coherent with its psi state:\\n\\t *\\n\\t * schedule()                   cgroup_move_task()\\n\\t *   rq_lock()\\n\\t *   deactivate_task()\\n\\t *     p->on_rq = 0\\n\\t *     psi_dequeue() // defers TSK_RUNNING & TSK_IOWAIT updates\\n\\t *   pick_next_task()\\n\\t *     rq_unlock()\\n\\t *                                rq_lock()\\n\\t *                                psi_task_change() // old cgroup\\n\\t *                                task->cgroups = to\\n\\t *                                psi_task_change() // new cgroup\\n\\t *                                rq_unlock()\\n\\t *     rq_lock()\\n\\t *   psi_sched_switch() // does deferred updates in new cgroup\\n\\t *\\n\\t * Don't rely on the scheduling state. Use psi_flags instead.\\n\\t */\\n\\ttask_flags = task->psi_flags;\\n\\n\\tif (task_flags)\\n\\t\\tpsi_task_change(task, task_flags, 0);\\n\\n\\t/* See comment above */\\n\\trcu_assign_pointer(task->cgroups, to);\\n\\n\\tif (task_flags)\\n\\t\\tpsi_task_change(task, 0, task_flags);\\n\\n\\ttask_rq_unlock(rq, task, &rf);\\n}\\n\\nvoid psi_cgroup_restart(struct psi_group *group)\\n{\\n\\tint cpu;\\n\\n\\t/*\\n\\t * After we disable psi_group->enabled, we don't actually\\n\\t * stop percpu tasks accounting in each psi_group_cpu,\\n\\t * instead only stop test_states() loop, record_times()\\n\\t * and averaging worker, see psi_group_change() for details.\\n\\t *\\n\\t * When disable cgroup PSI, this function has nothing to sync\\n\\t * since cgroup pressure files are hidden and percpu psi_group_cpu\\n\\t * would see !psi_group->enabled and only do task accounting.\\n\\t *\\n\\t * When re-enable cgroup PSI, this function use psi_group_change()\\n\\t * to get correct state mask from test_states() loop on tasks[],\\n\\t * and restart groupc->state_start from now, use .clear = .set = 0\\n\\t * here since no task status really changed.\\n\\t */\\n\\tif (!group->enabled)\\n\\t\\treturn;\\n\\n\\tfor_each_possible_cpu(cpu) {\\n\\t\\tstruct rq *rq = cpu_rq(cpu);\\n\\t\\tstruct rq_flags rf;\\n\\n\\t\\trq_lock_irq(rq, &rf);\\n\\t\\tpsi_group_change(group, cpu, 0, 0, true);\\n\\t\\trq_unlock_irq(rq, &rf);\\n\\t}\\n}\\n#endif /* CONFIG_CGROUPS */\\n\\nint psi_show(struct seq_file *m, struct psi_group *group, enum psi_res res)\\n{\\n\\tbool only_full = false;\\n\\tint full;\\n\\tu64 now;\\n\\n\\tif (static_branch_likely(&psi_disabled))\\n\\t\\treturn -EOPNOTSUPP;\\n\\n\\t/* Update averages before reporting them */\\n\\tmutex_lock(&group->avgs_lock);\\n\\tnow = sched_clock();\\n\\tcollect_percpu_times(group, PSI_AVGS, NULL);\\n\\tif (now >= group->avg_next_update)\\n\\t\\tgroup->avg_next_update = update_averages(group, now);\\n\\tmutex_unlock(&group->avgs_lock);\\n\\n#ifdef CONFIG_IRQ_TIME_ACCOUNTING\\n\\tonly_full = res == PSI_IRQ;\\n#endif\\n\\n\\tfor (full = 0; full < 2 - only_full; full++) {\\n\\t\\tunsigned long avg[3] = { 0, };\\n\\t\\tu64 total = 0;\\n\\t\\tint w;\\n\\n\\t\\t/* CPU FULL is undefined at the system level */\\n\\t\\tif (!(group == &psi_system && res == PSI_CPU && full)) {\\n\\t\\t\\tfor (w = 0; w < 3; w++)\\n\\t\\t\\t\\tavg[w] = group->avg[res * 2 + full][w];\\n\\t\\t\\ttotal = div_u64(group->total[PSI_AVGS][res * 2 + full],\\n\\t\\t\\t\\t\\tNSEC_PER_USEC);\\n\\t\\t}\\n\\n\\t\\tseq_printf(m, \\\"%s avg10=%lu.%02lu avg60=%lu.%02lu avg300=%lu.%02lu total=%llu\\\\n\\\",\\n\\t\\t\\t   full || only_full ? \\\"full\\\" : \\\"some\\\",\\n\\t\\t\\t   LOAD_INT(avg[0]), LOAD_FRAC(avg[0]),\\n\\t\\t\\t   LOAD_INT(avg[1]), LOAD_FRAC(avg[1]),\\n\\t\\t\\t   LOAD_INT(avg[2]), LOAD_FRAC(avg[2]),\\n\\t\\t\\t   total);\\n\\t}\\n\\n\\treturn 0;\\n}\\n\\nstruct psi_trigger *psi_trigger_create(struct psi_group *group, char *buf,\\n\\t\\t\\t\\t       enum psi_res res, struct file *file,\\n\\t\\t\\t\\t       struct kernfs_open_file *of)\\n{\\n\\tstruct psi_trigger *t;\\n\\tenum psi_states state;\\n\\tu32 threshold_us;\\n\\tbool privileged;\\n\\tu32 window_us;\\n\\n\\tif (static_branch_likely(&psi_disabled))\\n\\t\\treturn ERR_PTR(-EOPNOTSUPP);\\n\\n\\t/*\\n\\t * Checking the privilege here on file->f_cred implies that a privileged user\\n\\t * could open the file and delegate the write to an unprivileged one.\\n\\t */\\n\\tprivileged = cap_raised(file->f_cred->cap_effective, CAP_SYS_RESOURCE);\\n\\n\\tif (sscanf(buf, \\\"some %u %u\\\", &threshold_us, &window_us) == 2)\\n\\t\\tstate = PSI_IO_SOME + res * 2;\\n\\telse if (sscanf(buf, \\\"full %u %u\\\", &threshold_us, &window_us) == 2)\\n\\t\\tstate = PSI_IO_FULL + res * 2;\\n\\telse\\n\\t\\treturn ERR_PTR(-EINVAL);\\n\\n#ifdef CONFIG_IRQ_TIME_ACCOUNTING\\n\\tif (res == PSI_IRQ && --state != PSI_IRQ_FULL)\\n\\t\\treturn ERR_PTR(-EINVAL);\\n#endif\\n\\n\\tif (state >= PSI_NONIDLE)\\n\\t\\treturn ERR_PTR(-EINVAL);\\n\\n\\tif (window_us == 0 || window_us > WINDOW_MAX_US)\\n\\t\\treturn ERR_PTR(-EINVAL);\\n\\n\\t/*\\n\\t * Unprivileged users can only use 2s windows so that averages aggregation\\n\\t * work is used, and no RT threads need to be spawned.\\n\\t */\\n\\tif (!privileged && window_us % 2000000)\\n\\t\\treturn ERR_PTR(-EINVAL);\\n\\n\\t/* Check threshold */\\n\\tif (threshold_us == 0 || threshold_us > window_us)\\n\\t\\treturn ERR_PTR(-EINVAL);\\n\\n\\tt = kmalloc(sizeof(*t), GFP_KERNEL);\\n\\tif (!t)\\n\\t\\treturn ERR_PTR(-ENOMEM);\\n\\n\\tt->group = group;\\n\\tt->state = state;\\n\\tt->threshold = threshold_us * NSEC_PER_USEC;\\n\\tt->win.size = window_us * NSEC_PER_USEC;\\n\\twindow_reset(&t->win, sched_clock(),\\n\\t\\t\\tgroup->total[PSI_POLL][t->state], 0);\\n\\n\\tt->event = 0;\\n\\tt->last_event_time = 0;\\n\\tt->of = of;\\n\\tif (!of)\\n\\t\\tinit_waitqueue_head(&t->event_wait);\\n\\tt->pending_event = false;\\n\\tt->aggregator = privileged ? PSI_POLL : PSI_AVGS;\\n\\n\\tif (privileged) {\\n\\t\\tmutex_lock(&group->rtpoll_trigger_lock);\\n\\n\\t\\tif (!rcu_access_pointer(group->rtpoll_task)) {\\n\\t\\t\\tstruct task_struct *task;\\n\\n\\t\\t\\ttask = kthread_create(psi_rtpoll_worker, group, \\\"psimon\\\");\\n\\t\\t\\tif (IS_ERR(task)) {\\n\\t\\t\\t\\tkfree(t);\\n\\t\\t\\t\\tmutex_unlock(&group->rtpoll_trigger_lock);\\n\\t\\t\\t\\treturn ERR_CAST(task);\\n\\t\\t\\t}\\n\\t\\t\\tatomic_set(&group->rtpoll_wakeup, 0);\\n\\t\\t\\twake_up_process(task);\\n\\t\\t\\trcu_assign_pointer(group->rtpoll_task, task);\\n\\t\\t}\\n\\n\\t\\tlist_add(&t->node, &group->rtpoll_triggers);\\n\\t\\tgroup->rtpoll_min_period = min(group->rtpoll_min_period,\\n\\t\\t\\tdiv_u64(t->win.size, UPDATES_PER_WINDOW));\\n\\t\\tgroup->rtpoll_nr_triggers[t->state]++;\\n\\t\\tgroup->rtpoll_states |= (1 << t->state);\\n\\n\\t\\tmutex_unlock(&group->rtpoll_trigger_lock);\\n\\t} else {\\n\\t\\tmutex_lock(&group->avgs_lock);\\n\\n\\t\\tlist_add(&t->node, &group->avg_triggers);\\n\\t\\tgroup->avg_nr_triggers[t->state]++;\\n\\n\\t\\tmutex_unlock(&group->avgs_lock);\\n\\t}\\n\\treturn t;\\n}\\n\\nvoid psi_trigger_destroy(struct psi_trigger *t)\\n{\\n\\tstruct psi_group *group;\\n\\tstruct task_struct *task_to_destroy = NULL;\\n\\n\\t/*\\n\\t * We do not check psi_disabled since it might have been disabled after\\n\\t * the trigger got created.\\n\\t */\\n\\tif (!t)\\n\\t\\treturn;\\n\\n\\tgroup = t->group;\\n\\t/*\\n\\t * Wakeup waiters to stop polling and clear the queue to prevent it from\\n\\t * being accessed later. Can happen if cgroup is deleted from under a\\n\\t * polling process.\\n\\t */\\n\\tif (t->of)\\n\\t\\tkernfs_notify(t->of->kn);\\n\\telse\\n\\t\\twake_up_interruptible(&t->event_wait);\\n\\n\\tif (t->aggregator == PSI_AVGS) {\\n\\t\\tmutex_lock(&group->avgs_lock);\\n\\t\\tif (!list_empty(&t->node)) {\\n\\t\\t\\tlist_del(&t->node);\\n\\t\\t\\tgroup->avg_nr_triggers[t->state]--;\\n\\t\\t}\\n\\t\\tmutex_unlock(&group->avgs_lock);\\n\\t} else {\\n\\t\\tmutex_lock(&group->rtpoll_trigger_lock);\\n\\t\\tif (!list_empty(&t->node)) {\\n\\t\\t\\tstruct psi_trigger *tmp;\\n\\t\\t\\tu64 period = ULLONG_MAX;\\n\\n\\t\\t\\tlist_del(&t->node);\\n\\t\\t\\tgroup->rtpoll_nr_triggers[t->state]--;\\n\\t\\t\\tif (!group->rtpoll_nr_triggers[t->state])\\n\\t\\t\\t\\tgroup->rtpoll_states &= ~(1 << t->state);\\n\\t\\t\\t/*\\n\\t\\t\\t * Reset min update period for the remaining triggers\\n\\t\\t\\t * iff the destroying trigger had the min window size.\\n\\t\\t\\t */\\n\\t\\t\\tif (group->rtpoll_min_period == div_u64(t->win.size, UPDATES_PER_WINDOW)) {\\n\\t\\t\\t\\tlist_for_each_entry(tmp, &group->rtpoll_triggers, node)\\n\\t\\t\\t\\t\\tperiod = min(period, div_u64(tmp->win.size,\\n\\t\\t\\t\\t\\t\\t\\tUPDATES_PER_WINDOW));\\n\\t\\t\\t\\tgroup->rtpoll_min_period = period;\\n\\t\\t\\t}\\n\\t\\t\\t/* Destroy rtpoll_task when the last trigger is destroyed */\\n\\t\\t\\tif (group->rtpoll_states == 0) {\\n\\t\\t\\t\\tgroup->rtpoll_until = 0;\\n\\t\\t\\t\\ttask_to_destroy = rcu_dereference_protected(\\n\\t\\t\\t\\t\\t\\tgroup->rtpoll_task,\\n\\t\\t\\t\\t\\t\\tlockdep_is_held(&group->rtpoll_trigger_lock));\\n\\t\\t\\t\\trcu_assign_pointer(group->rtpoll_task, NULL);\\n\\t\\t\\t\\tdel_timer(&group->rtpoll_timer);\\n\\t\\t\\t}\\n\\t\\t}\\n\\t\\tmutex_unlock(&group->rtpoll_trigger_lock);\\n\\t}\\n\\n\\t/*\\n\\t * Wait for psi_schedule_rtpoll_work RCU to complete its read-side\\n\\t * critical section before destroying the trigger and optionally the\\n\\t * rtpoll_task.\\n\\t */\\n\\tsynchronize_rcu();\\n\\t/*\\n\\t * Stop kthread 'psimon' after releasing rtpoll_trigger_lock to prevent\\n\\t * a deadlock while waiting for psi_rtpoll_work to acquire\\n\\t * rtpoll_trigger_lock\\n\\t */\\n\\tif (task_to_destroy) {\\n\\t\\t/*\\n\\t\\t * After the RCU grace period has expired, the worker\\n\\t\\t * can no longer be found through group->rtpoll_task.\\n\\t\\t */\\n\\t\\tkthread_stop(task_to_destroy);\\n\\t\\tatomic_set(&group->rtpoll_scheduled, 0);\\n\\t}\\n\\tkfree(t);\\n}\\n\\n__poll_t psi_trigger_poll(void **trigger_ptr,\\n\\t\\t\\t\\tstruct file *file, poll_table *wait)\\n{\\n\\t__poll_t ret = DEFAULT_POLLMASK;\\n\\tstruct psi_trigger *t;\\n\\n\\tif (static_branch_likely(&psi_disabled))\\n\\t\\treturn DEFAULT_POLLMASK | EPOLLERR | EPOLLPRI;\\n\\n\\tt = smp_load_acquire(trigger_ptr);\\n\\tif (!t)\\n\\t\\treturn DEFAULT_POLLMASK | EPOLLERR | EPOLLPRI;\\n\\n\\tif (t->of)\\n\\t\\tkernfs_generic_poll(t->of, wait);\\n\\telse\\n\\t\\tpoll_wait(file, &t->event_wait, wait);\\n\\n\\tif (cmpxchg(&t->event, 1, 0) == 1)\\n\\t\\tret |= EPOLLPRI;\\n\\n\\treturn ret;\\n}\\n\\n#ifdef CONFIG_PROC_FS\\nstatic int psi_io_show(struct seq_file *m, void *v)\\n{\\n\\treturn psi_show(m, &psi_system, PSI_IO);\\n}\\n\\nstatic int psi_memory_show(struct seq_file *m, void *v)\\n{\\n\\treturn psi_show(m, &psi_system, PSI_MEM);\\n}\\n\\nstatic int psi_cpu_show(struct seq_file *m, void *v)\\n{\\n\\treturn psi_show(m, &psi_system, PSI_CPU);\\n}\\n\\nstatic int psi_io_open(struct inode *inode, struct file *file)\\n{\\n\\treturn single_open(file, psi_io_show, NULL);\\n}\\n\\nstatic int psi_memory_open(struct inode *inode, struct file *file)\\n{\\n\\treturn single_open(file, psi_memory_show, NULL);\\n}\\n\\nstatic int psi_cpu_open(struct inode *inode, struct file *file)\\n{\\n\\treturn single_open(file, psi_cpu_show, NULL);\\n}\\n\\nstatic ssize_t psi_write(struct file *file, const char __user *user_buf,\\n\\t\\t\\t size_t nbytes, enum psi_res res)\\n{\\n\\tchar buf[32];\\n\\tsize_t buf_size;\\n\\tstruct seq_file *seq;\\n\\tstruct psi_trigger *new;\\n\\n\\tif (static_branch_likely(&psi_disabled))\\n\\t\\treturn -EOPNOTSUPP;\\n\\n\\tif (!nbytes)\\n\\t\\treturn -EINVAL;\\n\\n\\tbuf_size = min(nbytes, sizeof(buf));\\n\\tif (copy_from_user(buf, user_buf, buf_size))\\n\\t\\treturn -EFAULT;\\n\\n\\tbuf[buf_size - 1] = '\\\\0';\\n\\n\\tseq = file->private_data;\\n\\n\\t/* Take seq->lock to protect seq->private from concurrent writes */\\n\\tmutex_lock(&seq->lock);\\n\\n\\t/* Allow only one trigger per file descriptor */\\n\\tif (seq->private) {\\n\\t\\tmutex_unlock(&seq->lock);\\n\\t\\treturn -EBUSY;\\n\\t}\\n\\n\\tnew = psi_trigger_create(&psi_system, buf, res, file, NULL);\\n\\tif (IS_ERR(new)) {\\n\\t\\tmutex_unlock(&seq->lock);\\n\\t\\treturn PTR_ERR(new);\\n\\t}\\n\\n\\tsmp_store_release(&seq->private, new);\\n\\tmutex_unlock(&seq->lock);\\n\\n\\treturn nbytes;\\n}\\n\\nstatic ssize_t psi_io_write(struct file *file, const char __user *user_buf,\\n\\t\\t\\t    size_t nbytes, loff_t *ppos)\\n{\\n\\treturn psi_write(file, user_buf, nbytes, PSI_IO);\\n}\\n\\nstatic ssize_t psi_memory_write(struct file *file, const char __user *user_buf,\\n\\t\\t\\t\\tsize_t nbytes, loff_t *ppos)\\n{\\n\\treturn psi_write(file, user_buf, nbytes, PSI_MEM);\\n}\\n\\nstatic ssize_t psi_cpu_write(struct file *file, const char __user *user_buf,\\n\\t\\t\\t     size_t nbytes, loff_t *ppos)\\n{\\n\\treturn psi_write(file, user_buf, nbytes, PSI_CPU);\\n}\\n\\nstatic __poll_t psi_fop_poll(struct file *file, poll_table *wait)\\n{\\n\\tstruct seq_file *seq = file->private_data;\\n\\n\\treturn psi_trigger_poll(&seq->private, file, wait);\\n}\\n\\nstatic int psi_fop_release(struct inode *inode, struct file *file)\\n{\\n\\tstruct seq_file *seq = file->private_data;\\n\\n\\tpsi_trigger_destroy(seq->private);\\n\\treturn single_release(inode, file);\\n}\\n\\nstatic const struct proc_ops psi_io_proc_ops = {\\n\\t.proc_open\\t= psi_io_open,\\n\\t.proc_read\\t= seq_read,\\n\\t.proc_lseek\\t= seq_lseek,\\n\\t.proc_write\\t= psi_io_write,\\n\\t.proc_poll\\t= psi_fop_poll,\\n\\t.proc_release\\t= psi_fop_release,\\n};\\n\\nstatic const struct proc_ops psi_memory_proc_ops = {\\n\\t.proc_open\\t= psi_memory_open,\\n\\t.proc_read\\t= seq_read,\\n\\t.proc_lseek\\t= seq_lseek,\\n\\t.proc_write\\t= psi_memory_write,\\n\\t.proc_poll\\t= psi_fop_poll,\\n\\t.proc_release\\t= psi_fop_release,\\n};\\n\\nstatic const struct proc_ops psi_cpu_proc_ops = {\\n\\t.proc_open\\t= psi_cpu_open,\\n\\t.proc_read\\t= seq_read,\\n\\t.proc_lseek\\t= seq_lseek,\\n\\t.proc_write\\t= psi_cpu_write,\\n\\t.proc_poll\\t= psi_fop_poll,\\n\\t.proc_release\\t= psi_fop_release,\\n};\\n\\n#ifdef CONFIG_IRQ_TIME_ACCOUNTING\\nstatic int psi_irq_show(struct seq_file *m, void *v)\\n{\\n\\treturn psi_show(m, &psi_system, PSI_IRQ);\\n}\\n\\nstatic int psi_irq_open(struct inode *inode, struct file *file)\\n{\\n\\treturn single_open(file, psi_irq_show, NULL);\\n}\\n\\nstatic ssize_t psi_irq_write(struct file *file, const char __user *user_buf,\\n\\t\\t\\t     size_t nbytes, loff_t *ppos)\\n{\\n\\treturn psi_write(file, user_buf, nbytes, PSI_IRQ);\\n}\\n\\nstatic const struct proc_ops psi_irq_proc_ops = {\\n\\t.proc_open\\t= psi_irq_open,\\n\\t.proc_read\\t= seq_read,\\n\\t.proc_lseek\\t= seq_lseek,\\n\\t.proc_write\\t= psi_irq_write,\\n\\t.proc_poll\\t= psi_fop_poll,\\n\\t.proc_release\\t= psi_fop_release,\\n};\\n#endif\\n\\nstatic int __init psi_proc_init(void)\\n{\\n\\tif (psi_enable) {\\n\\t\\tproc_mkdir(\\\"pressure\\\", NULL);\\n\\t\\tproc_create(\\\"pressure/io\\\", 0666, NULL, &psi_io_proc_ops);\\n\\t\\tproc_create(\\\"pressure/memory\\\", 0666, NULL, &psi_memory_proc_ops);\\n\\t\\tproc_create(\\\"pressure/cpu\\\", 0666, NULL, &psi_cpu_proc_ops);\\n#ifdef CONFIG_IRQ_TIME_ACCOUNTING\\n\\t\\tproc_create(\\\"pressure/irq\\\", 0666, NULL, &psi_irq_proc_ops);\\n#endif\\n\\t}\\n\\treturn 0;\\n}\\nmodule_init(psi_proc_init);\\n\\n#endif /* CONFIG_PROC_FS */\\n</task>\\n\\n<environment_details>\\n# VSCode Visible Files\\n\\n\\n# VSCode Open Tabs\\n\\n\\n# Current Time\\n2025/5/25 下午2:55:57 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n(Not available)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>ask</slug>\\n<name>❓ Ask</name>\\n<model>anthropic.claude-3-5-sonnet-********-v2:0</model>\\n\\n\\nNOTE: You are currently in '❓ Ask' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as '💻 Code' mode.\\n\\n# Current Workspace Directory (/Users/<USER>/Documents/work/JoyCoder-Plugin) Files\\n.commitlintrc.js\\n.editorconfig\\n.eslintrc.json\\n.gitignore\\n.npmrc\\n.prettierignore\\n.prettierrc\\n.vscodeignore\\nCHANGELOG.md\\nCONTRIBUTING.md\\nesbuild.js\\nLICENSE.md\\nmodel.json\\npackage.json\\npnpm-lock.yaml\\npnpm-workspace.yaml\\nREADME.md\\nassets/\\nassets/activitybar.svg\\nassets/code-review.svg\\nassets/logo-close.svg\\nassets/agent/AI-dark.svg\\nassets/agent/AI-light.svg\\nassets/agent/mcp-hub-dark.svg\\nassets/agent/mcp-hub-light.svg\\nassets/agent/mcp-hub.svg\\nassets/agent/new-chat.svg\\nassets/agent/default-themes/dark_modern.json\\nassets/agent/default-themes/dark_plus.json\\nassets/agent/default-themes/dark_vs.json\\nassets/agent/default-themes/hc_black.json\\nassets/agent/default-themes/hc_light.json\\nassets/agent/default-themes/light_modern.json\\nassets/agent/default-themes/light_plus.json\\nassets/agent/default-themes/light_vs.json\\nassets/wasm/tree-sitter-c_sharp.wasm\\nassets/wasm/tree-sitter-c.wasm\\nassets/wasm/tree-sitter-cpp.wasm\\nassets/wasm/tree-sitter-dart.wasm\\nassets/wasm/tree-sitter-go.wasm\\nassets/wasm/tree-sitter-html.wasm\\nassets/wasm/tree-sitter-java.wasm\\nassets/wasm/tree-sitter-javascript.wasm\\nassets/wasm/tree-sitter-php.wasm\\nassets/wasm/tree-sitter-python.wasm\\nassets/wasm/tree-sitter-ruby.wasm\\nassets/wasm/tree-sitter-rust.wasm\\nassets/wasm/tree-sitter-swift.wasm\\nassets/wasm/tree-sitter-tsx.wasm\\nassets/wasm/tree-sitter-typescript.wasm\\nassets/wasm/tree-sitter-yaml.wasm\\nassets/wasm/tree-sitter.wasm\\ndoc/\\ndoc/rules.md\\ndoc/testing.md\\npackages/\\npackages/agent-driven/.eslintrc.json\\npackages/agent-driven/.prettierignore \\npackages/agent-driven/esbuild.js\\npackages/agent-driven/initAgentDriven.ts\\npackages/agent-driven/jest.config.js\\npackages/agent-driven/jest.config.min.js\\npackages/agent-driven/LICENSE\\npackages/agent-driven/package.json\\npackages/agent-driven/tsconfig.json\\npackages/agent-driven/src/adaptor/translate/message.ts\\npackages/agent-driven/src/exports/index.ts\\npackages/agent-driven/src/exports/JoyCoderAPI.ts\\npackages/agent-driven/src/exports/README.md\\npackages/agent-driven/src/shared/api.ts\\npackages/agent-driven/src/shared/array.test.ts\\npackages/agent-driven/src/shared/array.ts\\npackages/agent-driven/src/shared/AutoApprovalSettings.ts\\npackages/agent-driven/src/shared/BrowserSettings.ts\\npackages/agent-driven/src/shared/ChatSettings.ts\\npackages/agent-driven/src/shared/combineApiRequests.ts\\npackages/agent-driven/src/shared/combineCommandSequences.ts\\npackages/agent-driven/src/shared/context-mentions.ts\\npackages/agent-driven/src/shared/experiments.ts\\npackages/agent-driven/src/shared/ExtensionMessage.ts\\npackages/agent-driven/src/shared/getApiMetrics.ts\\npackages/agent-driven/src/shared/HistoryItem.ts\\npackages/agent-driven/src/shared/language.ts\\npackages/agent-driven/src/shared/mcp.ts\\npackages/agent-driven/src/shared/modes.ts\\npackages/agent-driven/src/shared/tools.ts\\npackages/agent-driven/src/shared/types.ts\\npackages/agent-driven/src/shared/vsCodeSelectorUtils.ts\\npackages/agent-driven/src/shared/WebviewMessage.ts\\npackages/agent-driven/src/utils/cost.test.ts\\npackages/agent-driven/src/utils/cost.ts\\npackages/agent-driven/src/utils/fs.test.ts\\npackages/agent-driven/src/utils/fs.ts\\npackages/agent-driven/src/utils/git.ts\\npackages/agent-driven/src/utils/path.test.ts\\npackages/agent-driven/src/utils/pathUtils.ts\\npackages/agent-driven/src/utils/remoteOSName.ts\\npackages/agent-driven/src/utils/shell.ts\\npackages/agent-driven/src/utils/single-completion-handler.ts\\npackages/agent-driven/src/utils/storage.ts\\npackages/agent-driven/src/utils/string.ts\\npackages/agent-driven/src/utils/text-normalization.ts\\npackages/agent-driven/src/utils/time.ts\\npackages/agent-driven/src/utils/type-fu.ts\\npackages/agent-driven/src/utils/xml.ts\\npackages/agent-runtime/CODY.md\\npackages/plugin-base-ai-d2c/src/index.ts\\npackages/plugin-base-ai-d2c/src/interface.ts\\npackages/plugin-base-ai-d2c/src/agents/component.ts\\npackages/plugin-base-ai-d2c/src/agents/componentByAST.ts\\npackages/plugin-base-ai-d2c/src/agents/d2c.ts\\npackages/plugin-base-ai-d2c/src/agents/event.ts\\npackages/plugin-base-ai-d2c/src/bridge/index.ts\\npackages/plugin-base-ai-d2c/src/functions/base.ts\\npackages/plugin-base-ai-d2c/src/utils/ast.ts\\npackages/plugin-base-ai-d2c/src/utils/chatToFiles.ts\\npackages/plugin-base-ai-d2c/src/utils/logs.ts\\npackages/plugin-base-browser/package.json\\npackages/plugin-base-browser/tsconfig.json\\npackages/plugin-base-browser/src/index.ts\\npackages/plugin-base-browser/src/browser/BrowserClient.ts\\npackages/plugin-base-browser/src/browser/BrowserPage.ts\\npackages/plugin-base-browser/src/shared/constant.ts\\npackages/plugin-base-browser/src/shared/interface.ts\\npackages/plugin-base-browser/src/shared/utils/CommonTool.ts\\npackages/plugin-base-browser/src/webview-panel/WebviewPanel.ts\\npackages/plugin-base-browser/src/webview-panel/WebviewPanelManager.ts\\npackages/plugin-base-dynamic-snippets/package.json\\npackages/plugin-base-dynamic-snippets/README.md\\npackages/plugin-base-dynamic-snippets/tsconfig.json\\npackages/plugin-base-dynamic-snippets/src/index.ts\\npackages/plugin-base-dynamic-snippets/src/tools/mergeTools.ts\\npackages/plugin-base-hover-provider/package.json\\npackages/plugin-base-hover-provider/README.md\\npackages/plugin-base-hover-provider/tsconfig.json\\npackages/plugin-base-hover-provider/src/index.ts\\npackages/plugin-base-style-helper/package.json\\npackages/plugin-base-style-helper/README.md\\npackages/plugin-base-style-helper/tsconfig.json\\npackages/plugin-base-style-helper/src/html2scss/converter.ts\\npackages/plugin-base-style-helper/src/html2scss/index.ts\\npackages/plugin-base-style-helper/src/html2scss/utils.ts\\npackages/plugin-base-style-helper/src/html2scss/interfaces/dom.ts\\npackages/plugin-base-style-helper/src/html2scss/interfaces/options.ts\\npackages/plugin-base-style-helper/src/html2scss/test/index.ts\\npackages/plugin-base-tree-view/src/commands.ts\\npackages/plugin-base-tree-view/src/index.ts\\npackages/plugin-base-tree-view/src/viewProvider.ts\\npackages/plugin-base-upload-image/package.json\\npackages/plugin-base-upload-image/README.md\\npackages/plugin-base-upload-image/tsconfig.json\\npackages/plugin-base-upload-image/src/index.ts\\npackages/plugin-base-upload-image/src/uploadImage/index.ts\\npackages/vscode-business/.vscodeignore\\npackages/vscode-business/CHANGELOG.md\\npackages/vscode-business/LICENSE.md\\npackages/vscode-business/package.json\\npackages/vscode-business/README.md\\npackages/vscode-business/tsconfig.json\\npackages/vscode-business/webpack.config.js\\npackages/vscode-business/assets/activitybar.svg\\npackages/vscode-business/assets/chatgpt.png\\npackages/vscode-business/assets/logo-close.svg\\npackages/vscode-business/assets/dark/completion.svg\\npackages/vscode-business/assets/dark/refresh.svg\\npackages/vscode-business/assets/dark/run.svg\\npackages/vscode-business/assets/dark/setting.svg\\npackages/vscode-business/src/extension.ts\\npackages/vscode-IDE/CHANGELOG.md\\npackages/vscode-IDE/src/extension.ts\\nremote_binary/\\nremote_binary/stat/statCode-darwin-arm_1.0.0.zip\\nremote_binary/stat/statCode-darwin-x64_1.0.0.zip\\nremote_binary/stat/statCode-linux-x64_1.0.0.zip\\nremote_binary/stat/statCode-win32-x64_1.0.0.zip\\nremote_binary/stat/statCodeDarwinAmd\\nremote_binary/stat/statCodeDarwinArm\\nremote_binary/stat/statCodeLinuxAmd\\nremote_binary/stat/statCodeWindows.exe\\nscripts/\\nscripts/fakeSnippet.js\\nscripts/build/utils.js\\nscripts/os/linux.sh\\nscripts/os/mac.applescript\\nscripts/os/pc.ps1\\nsnippets/\\nsnippets/src/demo.json\\nsnippets/src/cpp/index.json\\nsnippets/src/css/css.json\\nsnippets/src/go/index.json\\nsnippets/src/html/html.json\\nsnippets/src/java/index.json\\nsnippets/src/js/react/hooks.json\\nsnippets/src/js/react/jsx.json\\nsnippets/src/js/taro/api.json\\nsnippets/src/js/taro/condition.json\\nsnippets/src/js/taro/hooks.json\\nsnippets/src/js/taro/jsx.json\\nsrc/\\ntypings/\\n\\n(File list truncated. Use list_files on specific subdirectories if you need to explore further.)\\n</environment_details>\",\"tokensIn\":0,\"tokensOut\":0,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1748156157654, "type": "say", "say": "error", "text": "Unexpected API Response: The language model did not provide any assistant messages. This may indicate an issue with the API or the model's output."}, {"ts": 1748156157664, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[ERROR] You did not use a tool in your previous response! Please retry with a tool use.\\n\\n# Reminder: Instructions for Tool Use\\n\\nTool uses are formatted using XML-style tags. The tool name itself becomes the XML tag name. Each parameter is enclosed within its own set of tags. Here's the structure:\\n\\n<actual_tool_name>\\n<parameter1_name>value1</parameter1_name>\\n<parameter2_name>value2</parameter2_name>\\n...\\n</actual_tool_name>\\n\\nFor example, to use the attempt_completion tool:\\n\\n<attempt_completion>\\n<result>\\nI have completed the task...\\n</result>\\n</attempt_completion>\\n\\nAlways use the actual tool name as the XML tag name for proper parsing and execution.\\n\\n# Next Steps\\n\\nIf you have completed the user's task, use the attempt_completion tool. \\nIf you require additional information from the user, use the ask_followup_question tool. \\nOtherwise, if you have not completed the task and do not need additional information, then proceed with the next step of the task. \\n(This is an automated message, so do not respond to it conversationally.)\\n\\n<environment_details>\\n# VSCode Visible Files\\n\\n\\n# VSCode Open Tabs\\n\\n\\n# Current Time\\n2025/5/25 下午2:55:57 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n(Not available)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>ask</slug>\\n<name>❓ Ask</name>\\n<model>anthropic.claude-3-5-sonnet-********-v2:0</model>\\n\\n\\nNOTE: You are currently in '❓ Ask' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as '💻 Code' mode.\\n</environment_details>\",\"tokensIn\":0,\"tokensOut\":0,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1748156158192, "type": "say", "say": "error", "text": "Unexpected API Response: The language model did not provide any assistant messages. This may indicate an issue with the API or the model's output."}, {"ts": 1748156158200, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[ERROR] You did not use a tool in your previous response! Please retry with a tool use.\\n\\n# Reminder: Instructions for Tool Use\\n\\nTool uses are formatted using XML-style tags. The tool name itself becomes the XML tag name. Each parameter is enclosed within its own set of tags. Here's the structure:\\n\\n<actual_tool_name>\\n<parameter1_name>value1</parameter1_name>\\n<parameter2_name>value2</parameter2_name>\\n...\\n</actual_tool_name>\\n\\nFor example, to use the attempt_completion tool:\\n\\n<attempt_completion>\\n<result>\\nI have completed the task...\\n</result>\\n</attempt_completion>\\n\\nAlways use the actual tool name as the XML tag name for proper parsing and execution.\\n\\n# Next Steps\\n\\nIf you have completed the user's task, use the attempt_completion tool. \\nIf you require additional information from the user, use the ask_followup_question tool. \\nOtherwise, if you have not completed the task and do not need additional information, then proceed with the next step of the task. \\n(This is an automated message, so do not respond to it conversationally.)\\n\\n<environment_details>\\n# VSCode Visible Files\\n\\n\\n# VSCode Open Tabs\\n\\n\\n# Current Time\\n2025/5/25 下午2:55:58 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n(Not available)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>ask</slug>\\n<name>❓ Ask</name>\\n<model>anthropic.claude-3-5-sonnet-********-v2:0</model>\\n\\n\\nNOTE: You are currently in '❓ Ask' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as '💻 Code' mode.\\n</environment_details>\",\"tokensIn\":0,\"tokensOut\":0,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1748156158343, "type": "say", "say": "error", "text": "Unexpected API Response: The language model did not provide any assistant messages. This may indicate an issue with the API or the model's output."}, {"ts": 1748156158351, "type": "ask", "ask": "mistake_limit_reached", "text": "This may indicate a failure in his thought process or inability to use a tool properly, which can be mitigated with some user guidance (e.g. \"Try breaking down the task into smaller steps\")."}, {"ts": 1748156162147, "type": "say", "say": "checkpoint_saved", "text": "dc8d3969649342022425db1ccfcdf651ff19d456", "checkpoint": {"isFirst": true, "from": "dc8d3969649342022425db1ccfcdf651ff19d456", "to": "dc8d3969649342022425db1ccfcdf651ff19d456"}}, {"ts": 1748156167540, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[ERROR] You did not use a tool in your previous response! Please retry with a tool use.\\n\\n# Reminder: Instructions for Tool Use\\n\\nTool uses are formatted using XML-style tags. The tool name itself becomes the XML tag name. Each parameter is enclosed within its own set of tags. Here's the structure:\\n\\n<actual_tool_name>\\n<parameter1_name>value1</parameter1_name>\\n<parameter2_name>value2</parameter2_name>\\n...\\n</actual_tool_name>\\n\\nFor example, to use the attempt_completion tool:\\n\\n<attempt_completion>\\n<result>\\nI have completed the task...\\n</result>\\n</attempt_completion>\\n\\nAlways use the actual tool name as the XML tag name for proper parsing and execution.\\n\\n# Next Steps\\n\\nIf you have completed the user's task, use the attempt_completion tool. \\nIf you require additional information from the user, use the ask_followup_question tool. \\nOtherwise, if you have not completed the task and do not need additional information, then proceed with the next step of the task. \\n(This is an automated message, so do not respond to it conversationally.)\\n\\n<environment_details>\\n# VSCode Visible Files\\n\\n\\n# VSCode Open Tabs\\n\\n\\n# Current Time\\n2025/5/25 下午2:56:07 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n(Not available)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>ask</slug>\\n<name>❓ Ask</name>\\n<model>anthropic.claude-3-5-sonnet-********-v2:0</model>\\n\\n\\nNOTE: You are currently in '❓ Ask' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as '💻 Code' mode.\\n</environment_details>\",\"tokensIn\":0,\"tokensOut\":0,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1748156168195, "type": "say", "say": "error", "text": "Unexpected API Response: The language model did not provide any assistant messages. This may indicate an issue with the API or the model's output."}, {"ts": 1748156168210, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[ERROR] You did not use a tool in your previous response! Please retry with a tool use.\\n\\n# Reminder: Instructions for Tool Use\\n\\nTool uses are formatted using XML-style tags. The tool name itself becomes the XML tag name. Each parameter is enclosed within its own set of tags. Here's the structure:\\n\\n<actual_tool_name>\\n<parameter1_name>value1</parameter1_name>\\n<parameter2_name>value2</parameter2_name>\\n...\\n</actual_tool_name>\\n\\nFor example, to use the attempt_completion tool:\\n\\n<attempt_completion>\\n<result>\\nI have completed the task...\\n</result>\\n</attempt_completion>\\n\\nAlways use the actual tool name as the XML tag name for proper parsing and execution.\\n\\n# Next Steps\\n\\nIf you have completed the user's task, use the attempt_completion tool. \\nIf you require additional information from the user, use the ask_followup_question tool. \\nOtherwise, if you have not completed the task and do not need additional information, then proceed with the next step of the task. \\n(This is an automated message, so do not respond to it conversationally.)\\n\\n<environment_details>\\n# VSCode Visible Files\\n\\n\\n# VSCode Open Tabs\\n\\n\\n# Current Time\\n2025/5/25 下午2:56:08 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n(Not available)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>ask</slug>\\n<name>❓ Ask</name>\\n<model>anthropic.claude-3-5-sonnet-********-v2:0</model>\\n\\n\\nNOTE: You are currently in '❓ Ask' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as '💻 Code' mode.\\n</environment_details>\",\"tokensIn\":0,\"tokensOut\":0,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1748156168682, "type": "say", "say": "error", "text": "Unexpected API Response: The language model did not provide any assistant messages. This may indicate an issue with the API or the model's output."}, {"ts": 1748156168696, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[ERROR] You did not use a tool in your previous response! Please retry with a tool use.\\n\\n# Reminder: Instructions for Tool Use\\n\\nTool uses are formatted using XML-style tags. The tool name itself becomes the XML tag name. Each parameter is enclosed within its own set of tags. Here's the structure:\\n\\n<actual_tool_name>\\n<parameter1_name>value1</parameter1_name>\\n<parameter2_name>value2</parameter2_name>\\n...\\n</actual_tool_name>\\n\\nFor example, to use the attempt_completion tool:\\n\\n<attempt_completion>\\n<result>\\nI have completed the task...\\n</result>\\n</attempt_completion>\\n\\nAlways use the actual tool name as the XML tag name for proper parsing and execution.\\n\\n# Next Steps\\n\\nIf you have completed the user's task, use the attempt_completion tool. \\nIf you require additional information from the user, use the ask_followup_question tool. \\nOtherwise, if you have not completed the task and do not need additional information, then proceed with the next step of the task. \\n(This is an automated message, so do not respond to it conversationally.)\\n\\n<environment_details>\\n# VSCode Visible Files\\n\\n\\n# VSCode Open Tabs\\n\\n\\n# Current Time\\n2025/5/25 下午2:56:08 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n(Not available)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>ask</slug>\\n<name>❓ Ask</name>\\n<model>anthropic.claude-3-5-sonnet-********-v2:0</model>\\n\\n\\nNOTE: You are currently in '❓ Ask' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as '💻 Code' mode.\\n</environment_details>\",\"tokensIn\":0,\"tokensOut\":0,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1748156169098, "type": "say", "say": "error", "text": "Unexpected API Response: The language model did not provide any assistant messages. This may indicate an issue with the API or the model's output."}, {"ts": 1748156169111, "type": "ask", "ask": "mistake_limit_reached", "text": "This may indicate a failure in his thought process or inability to use a tool properly, which can be mitigated with some user guidance (e.g. \"Try breaking down the task into smaller steps\")."}]