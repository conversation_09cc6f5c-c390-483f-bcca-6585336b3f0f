[{"ts": 1751461534924, "type": "say", "say": "text", "text": "基于taro的最新版本，帮我整理一份taro的简述文档，包括简介、产物支持的范围、功能分类、项目结构、底层原理、初始化项目、包含生态、样式总览、API分类、hooks分类、组件分类、配置等，帮我将结果写入到MD文档中，放在系统桌面上", "images": []}, {"ts": 1751461534962, "type": "say", "say": "api_req_started", "text": "{\"request\":\"<task>\\n基于taro的最新版本，帮我整理一份taro的简述文档，包括简介、产物支持的范围、功能分类、项目结构、底层原理、初始化项目、包含生态、样式总览、API分类、hooks分类、组件分类、配置等，帮我将结果写入到MD文档中，放在系统桌面上\\n</task>\\n\\n<environment_details>\\n# VSCode Visible Files\\npackages/vscode-IDE/package.json\\n\\n# VSCode Open Tabs\\npackages/agent-setting/src/components/AgentManagement/index.tsx,packages/agent-driven/src/core/config/CustomModesManager.ts,packages/agent-setting/src/components/setting/index.tsx,packages/agent-setting/src/components/mcp/index.tsx,packages/agent-setting/src/components/mcp/McpInstalledView.tsx,packages/vscode-IDE/package.json\\n\\n# Current Time\\n2025/7/2 下午9:05:34 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n(Not available)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>code</slug>\\n<name>💻 Code</name>\\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\\n\\n\\n# Current Workspace Directory (/Users/<USER>/Documents/work/JoyCoder-Plugin) Files\\nassets/\\nassets/agent/\\nassets/agent/default-themes/\\nassets/codicon/\\nassets/d2c/\\nassets/dark/\\nassets/iconfont/\\nassets/iconfont/joycode-iconfont/\\nassets/light/\\nassets/sec/\\nassets/translation/\\nassets/treeView/\\nassets/wasm/\\ndoc/\\npackages/\\npackages/agent-common/\\npackages/agent-common/src/\\npackages/agent-common/src/demo/\\npackages/agent-common/src/tools/\\npackages/agent-common/src/utils/\\npackages/agent-common/src/vscode/\\npackages/agent-driven/\\npackages/agent-driven/src/\\npackages/agent-driven/src/__mocks__/\\npackages/agent-driven/src/adaptor/\\npackages/agent-driven/src/adaptor/api/\\npackages/agent-driven/src/adaptor/api/providers/\\npackages/agent-driven/src/adaptor/api/transform/\\npackages/agent-driven/src/adaptor/contextMenu/\\npackages/agent-driven/src/adaptor/translate/\\npackages/agent-driven/src/core/\\npackages/agent-driven/src/core/assistant-message/\\npackages/agent-driven/src/core/config/\\npackages/agent-driven/src/core/context-management/\\npackages/agent-driven/src/core/context-management/__tests__/\\npackages/agent-driven/src/core/context-tracking/\\npackages/agent-driven/src/core/diff/\\npackages/agent-driven/src/core/diff/strategies/\\npackages/agent-driven/src/core/diff/strategies/__tests__/\\npackages/agent-driven/src/core/ignore/\\npackages/agent-driven/src/core/JoyCoder/\\npackages/agent-driven/src/core/JoyCoder/tools/\\npackages/agent-driven/src/core/mentions/\\npackages/agent-driven/src/core/prompts/\\npackages/agent-driven/src/core/prompts/__tests__/\\npackages/agent-driven/src/core/prompts/instructions/\\npackages/agent-driven/src/core/prompts/sections/\\npackages/agent-driven/src/core/prompts/tools/\\npackages/agent-driven/src/core/slash-commands/\\npackages/agent-driven/src/core/sliding-window/\\npackages/agent-driven/src/core/storage/\\npackages/agent-driven/src/core/user-pormpt/\\npackages/agent-driven/src/core/webview/\\npackages/agent-driven/src/exports/\\npackages/agent-driven/src/integrations/\\npackages/agent-driven/src/integrations/checkpoints/\\npackages/agent-driven/src/integrations/debug/\\npackages/agent-driven/src/integrations/diagnostics/\\npackages/agent-driven/src/integrations/editor/\\npackages/agent-driven/src/integrations/misc/\\npackages/agent-driven/src/integrations/notifications/\\npackages/agent-driven/src/integrations/terminal/\\npackages/agent-driven/src/integrations/theme/\\npackages/agent-driven/src/integrations/theme/default-themes/\\npackages/agent-driven/src/integrations/workspace/\\npackages/agent-driven/src/schemas/\\npackages/agent-driven/src/services/\\npackages/agent-driven/src/services/auth/\\npackages/agent-driven/src/services/browser/\\npackages/agent-driven/src/services/code-index/\\npackages/agent-driven/src/services/code-index/__tests__/\\npackages/agent-driven/src/services/code-index/constants/\\npackages/agent-driven/src/services/code-index/embedders/\\npackages/agent-driven/src/services/code-index/embedders/__tests__/\\npackages/agent-driven/src/services/code-index/interfaces/\\npackages/agent-driven/src/services/code-index/server/\\npackages/agent-driven/src/services/code-index/server/__tests__/\\npackages/agent-driven/src/services/code-index/shared/\\npackages/agent-driven/src/services/code-index/vector-store/\\npackages/agent-driven/src/services/code-index/vector-store/__tests__/\\npackages/agent-driven/src/services/glob/\\npackages/agent-driven/src/services/mcp/\\npackages/agent-driven/src/services/ripgrep/\\npackages/agent-driven/src/services/tree-sitter/\\npackages/agent-driven/src/services/tree-sitter/queries/\\npackages/agent-driven/src/shared/\\npackages/agent-driven/src/test/\\npackages/agent-driven/src/test/suite/\\npackages/agent-driven/src/test/webview/\\npackages/agent-driven/src/utils/\\npackages/agent-driven/web-agent/\\npackages/agent-driven/web-agent/public/\\npackages/agent-driven/web-agent/scripts/\\npackages/agent-driven/web-agent/src/\\npackages/agent-driven/web-agent/src/adaptor/\\npackages/agent-driven/web-agent/src/adaptor/components/\\npackages/agent-driven/web-agent/src/adaptor/locales/\\npackages/agent-driven/web-agent/src/adaptor/login/\\npackages/agent-driven/web-agent/src/assets/\\npackages/agent-driven/web-agent/src/assets/animations/\\npackages/agent-driven/web-agent/src/assets/images/\\npackages/agent-driven/web-agent/src/components/\\npackages/agent-driven/web-agent/src/components/account/\\npackages/agent-driven/web-agent/src/components/browser/\\npackages/agent-driven/web-agent/src/components/chat/\\npackages/agent-driven/web-agent/src/components/chat/__tests__/\\npackages/agent-driven/web-agent/src/components/common/\\npackages/agent-driven/web-agent/src/components/history/\\npackages/agent-driven/web-agent/src/components/mcp/\\npackages/agent-driven/web-agent/src/components/prompts/\\npackages/agent-driven/web-agent/src/components/settings/\\npackages/agent-driven/web-agent/src/components/settings/__tests__/\\npackages/agent-driven/web-agent/src/components/welcome/\\npackages/agent-driven/web-agent/src/context/\\npackages/agent-driven/web-agent/src/types/\\npackages/agent-driven/web-agent/src/utils/\\npackages/agent-driven/web-agent/src/utils/__tests__/\\npackages/agent-runtime/\\npackages/agent-runtime/agent/\\npackages/agent-runtime/agent/bindings/\\npackages/agent-runtime/agent/bindings/kotlin/\\npackages/agent-runtime/agent/bindings/kotlin/gradle/\\npackages/agent-runtime/agent/bindings/kotlin/gradle/wrapper/\\npackages/agent-runtime/agent/bindings/kotlin/lib/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/com/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/com/sourcegraph/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/com/sourcegraph/cody/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/com/sourcegraph/cody/agent/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/main/kotlin/com/sourcegraph/cody/agent/protocol_generated/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/test/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/test/kotlin/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/test/kotlin/org/\\npackages/agent-runtime/agent/bindings/kotlin/lib/src/test/kotlin/org/example/\\npackages/agent-runtime/agent/bindings/kotlin/src/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/kotlin/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/kotlin/com/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/kotlin/com/sourcegraph/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/kotlin/com/sourcegraph/cody/\\npackages/agent-runtime/agent/bindings/kotlin/src/main/kotlin/com/sourcegraph/cody/protocol_generated/\\npackages/agent-runtime/agent/demo/\\npackages/agent-runtime/agent/recordings/\\npackages/agent-runtime/agent/recordings/auth_2503977039/\\npackages/agent-runtime/agent/recordings/autocomplete_2136217965/\\npackages/agent-runtime/agent/recordings/cody-chat_103640681/\\npackages/agent-runtime/agent/recordings/customCommandsClient_509552979/\\npackages/agent-runtime/agent/recordings/defaultClient_631904893/\\npackages/agent-runtime/agent/recordings/document-code_965949506/\\npackages/agent-runtime/agent/recordings/edit_1541920145/\\npackages/agent-runtime/agent/recordings/enterpriseClient_3965582033/\\npackages/agent-runtime/agent/recordings/enterpriseMainBranchClient_759014996/\\npackages/agent-runtime/agent/recordings/fix_3001320056/\\npackages/agent-runtime/agent/recordings/generate-unit-test_1969275822/\\npackages/agent-runtime/agent/recordings/rateLimitedClient_2043004676/\\npackages/agent-runtime/agent/recordings/unauthed_2245427793/\\npackages/agent-runtime/agent/recordings/vscode_525627603/\\npackages/agent-runtime/agent/scripts/\\npackages/agent-runtime/agent/src/\\npackages/agent-runtime/agent/src/__snapshots__/\\npackages/agent-runtime/agent/src/__tests__/\\npackages/agent-runtime/agent/src/__tests__/auth/\\npackages/agent-runtime/agent/src/__tests__/autocomplete/\\npackages/agent-runtime/agent/src/__tests__/autocomplete/src/\\npackages/agent-runtime/agent/src/__tests__/chat-response-quality/\\npackages/agent-runtime/agent/src/__tests__/chat-response-quality/grpc/\\npackages/agent-runtime/agent/src/__tests__/chat-response-quality/grpc/chunk/\\npackages/agent-runtime/agent/src/__tests__/chat-response-quality/shards/\\npackages/agent-runtime/agent/src/__tests__/cody-cli-chat/\\npackages/agent-runtime/agent/src/__tests__/custom-commands/\\npackages/agent-runtime/agent/src/__tests__/custom-commands/src/\\npackages/agent-runtime/agent/src/__tests__/document-code/\\npackages/agent-runtime/agent/src/__tests__/document-code/src/\\npackages/agent-runtime/agent/src/__tests__/edit-code/\\npackages/agent-runtime/agent/src/__tests__/edit-code/src/\\npackages/agent-runtime/agent/src/__tests__/example-ts/\\npackages/agent-runtime/agent/src/__tests__/example-ts/src/\\npackages/agent-runtime/agent/src/__tests__/fix/\\npackages/agent-runtime/agent/src/__tests__/fix/src/\\npackages/agent-runtime/agent/src/__tests__/generate-unit-test/\\npackages/agent-runtime/agent/src/__tests__/generate-unit-test/src/\\npackages/agent-runtime/agent/src/__tests__/graph-test/\\npackages/agent-runtime/agent/src/__tests__/graph-test/src/\\npackages/agent-runtime/agent/src/cli/\\npackages/agent-runtime/agent/src/cli/__snapshots__/\\npackages/agent-runtime/agent/src/cli/command-auth/\\npackages/agent-runtime/agent/src/cli/command-bench/\\npackages/agent-runtime/agent/src/cli/command-bench/__snapshots__/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/cpp/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/csharp/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/dart/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/go/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/java/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/javascript/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/php/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/python/\\npackages/agent-runtime/agent/src/cli/command-bench/queries/tsx/\\n\\n(File list truncated. Use list_files on specific subdirectories if you need to explore further.)\\n</environment_details>\",\"tokensIn\":16310,\"tokensOut\":140,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1751461538145, "type": "say", "say": "text", "text": "我需要完成以下任务:\n1. 收集最新版本Taro的相关信息\n2. 整理成一份完整的MD文档,包含所有要求的内容点\n3. 将文档保存到系统桌面\n\n让我先使用browser_action", "partial": true}, {"ts": 1751461540195, "type": "say", "say": "checkpoint_saved", "text": "046ecc23a0db4f1b8e9468eb7341efe5a941eedb", "checkpoint": {"isFirst": true, "from": "046ecc23a0db4f1b8e9468eb7341efe5a941eedb", "to": "046ecc23a0db4f1b8e9468eb7341efe5a941eedb"}}, {"ts": 1751461540242, "type": "say", "say": "text", "text": "我需要完成以下任务:\n1. 收集最新版本Taro的相关信息\n2. 整理成一份完整的MD文档,包含所有要求的内容点\n3. 将文档保存到系统桌面\n\n让我先使用browser_action工具访问Taro官网,获取最新信息。", "partial": false}, {"ts": 1751461540890, "type": "ask", "ask": "browser_action_launch", "text": "https://taro.zone/", "partial": false, "isProtected": false}, {"ts": 1751461541360, "type": "say", "say": "browser_action_result", "text": ""}, {"ts": 1751461549380, "type": "say", "say": "browser_action_result", "text": "{\"screenshot\":\"data:image/webp;base64,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\",\"logs\":\"[warn] [mobx.array] Attempt to read an array index (0) that is out of bounds (0). Please check length first. Out of bound indices will not be tracked by MobX\\n[error] Failed to load resource: net::ERR_NAME_NOT_RESOLVED\\n[error] Failed to load resource: net::ERR_NAME_NOT_RESOLVED\",\"currentUrl\":\"https://taro.zone/\"}"}, {"ts": 1751461549408, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[browser_action for 'launch'] Result:\\n\\nThe browser action has been executed. The console logs and screenshot have been captured for your analysis.\\n\\nConsole logs:\\n[warn] [mobx.array] Attempt to read an array index (0) that is out of bounds (0). Please check length first. Out of bound indices will not be tracked by MobX\\n[error] Failed to load resource: net::ERR_NAME_NOT_RESOLVED\\n[error] Failed to load resource: net::ERR_NAME_NOT_RESOLVED\\n\\n(REMEMBER: if you need to proceed to using non-`browser_action` tools or launch a new browser, you MUST first close cline browser. For example, if after analyzing the logs and screenshot you need to edit a file, you must first close the browser before you can use the write_to_file tool.)\\n\\n[Image]\\n\\n<environment_details>\\n# VSCode Visible Files\\npackages/vscode-IDE/package.json\\n\\n# VSCode Open Tabs\\npackages/agent-setting/src/components/AgentManagement/index.tsx,packages/agent-driven/src/core/config/CustomModesManager.ts,packages/agent-setting/src/components/setting/index.tsx,packages/agent-setting/src/components/mcp/index.tsx,packages/agent-setting/src/components/mcp/McpInstalledView.tsx,packages/vscode-IDE/package.json\\n\\n# Current Time\\n2025/7/2 下午9:05:49 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n16,450 (13%)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>code</slug>\\n<name>💻 Code</name>\\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\\n</environment_details>\",\"tokensIn\":0,\"tokensOut\":0,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1751461554254, "type": "say", "say": "text", "text": "看起来taro.zone域名无法访问。让我尝试访问Taro的GitHub仓库来获取最新信息。", "partial": false}, {"ts": 1751461555939, "type": "say", "say": "browser_action", "text": "{\"action\":\"close\"}", "partial": false}, {"ts": 1751461556534, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[browser_action for 'close'] Result:\\n\\nThe browser has been closed. You may now proceed to using other tools.\\n\\nTool [browser_action] was not executed because a tool has already been used in this message. Only one tool may be used per message. You must assess the first tool's result before proceeding to use the next tool.\\n\\n<environment_details>\\n# VSCode Visible Files\\npackages/vscode-IDE/package.json\\n\\n# VSCode Open Tabs\\npackages/agent-setting/src/components/AgentManagement/index.tsx,packages/agent-driven/src/core/config/CustomModesManager.ts,packages/agent-setting/src/components/setting/index.tsx,packages/agent-setting/src/components/mcp/index.tsx,packages/agent-setting/src/components/mcp/McpInstalledView.tsx,packages/vscode-IDE/package.json\\n\\n# Current Time\\n2025/7/2 下午9:05:56 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n16,450 (13%)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>code</slug>\\n<name>💻 Code</name>\\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\\n</environment_details>\",\"tokensIn\":18072,\"tokensOut\":40,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1751461561260, "type": "say", "say": "text", "text": "", "partial": false}, {"ts": 1751461561296, "type": "ask", "ask": "browser_action_launch", "text": "https://github.com/nervjs/taro", "partial": false, "isProtected": false}, {"ts": 1751461561505, "type": "say", "say": "browser_action_result", "text": ""}, {"ts": 1751461568455, "type": "say", "say": "browser_action_result", "text": "{\"screenshot\":\"data:image/webp;base64,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\",\"logs\":\"\",\"currentUrl\":\"https://github.com/nervjs/taro\"}"}, {"ts": 1751461568506, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[browser_action for 'launch'] Result:\\n\\nThe browser action has been executed. The console logs and screenshot have been captured for your analysis.\\n\\nConsole logs:\\n(No new logs)\\n\\n(REMEMBER: if you need to proceed to using non-`browser_action` tools or launch a new browser, you MUST first close cline browser. For example, if after analyzing the logs and screenshot you need to edit a file, you must first close the browser before you can use the write_to_file tool.)\\n\\n[Image]\\n\\n<environment_details>\\n# VSCode Visible Files\\npackages/vscode-IDE/package.json\\n\\n# VSCode Open Tabs\\npackages/agent-setting/src/components/AgentManagement/index.tsx,packages/agent-driven/src/core/config/CustomModesManager.ts,packages/agent-setting/src/components/setting/index.tsx,packages/agent-setting/src/components/mcp/index.tsx,packages/agent-setting/src/components/mcp/McpInstalledView.tsx,packages/vscode-IDE/package.json\\n\\n# Current Time\\n2025/7/2 下午9:06:08 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n18,112 (14%)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>code</slug>\\n<name>💻 Code</name>\\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\\n</environment_details>\",\"tokensIn\":0,\"tokensOut\":0,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1751461572775, "type": "say", "say": "text", "text": "已经获取到了Taro的基本信息。现在让我关闭浏览器,开始编写MD文档。我需要将文档保存到桌面,所以先查看桌面路径。", "partial": false}, {"ts": 1751461574558, "type": "say", "say": "browser_action", "text": "{\"action\":\"close\"}", "partial": false}, {"ts": 1751461574872, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[browser_action for 'close'] Result:\\n\\nThe browser has been closed. You may now proceed to using other tools.\\n\\nTool [list_files] was not executed because a tool has already been used in this message. Only one tool may be used per message. You must assess the first tool's result before proceeding to use the next tool.\\n\\n<environment_details>\\n# VSCode Visible Files\\npackages/vscode-IDE/package.json\\n\\n# VSCode Open Tabs\\npackages/agent-setting/src/components/AgentManagement/index.tsx,packages/agent-driven/src/core/config/CustomModesManager.ts,packages/agent-setting/src/components/setting/index.tsx,packages/agent-setting/src/components/mcp/index.tsx,packages/agent-setting/src/components/mcp/McpInstalledView.tsx,packages/vscode-IDE/package.json\\n\\n# Current Time\\n2025/7/2 下午9:06:14 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n18,112 (14%)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>code</slug>\\n<name>💻 Code</name>\\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\\n</environment_details>\",\"tokensIn\":19657,\"tokensOut\":38,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1751461579912, "type": "say", "say": "text", "text": "", "partial": false}, {"ts": 1751461579973, "type": "ask", "ask": "tool", "text": "{\"tool\":\"listFilesTopLevel\",\"path\":\"/Users/<USER>/Desktop\",\"isOutsideWorkspace\":true,\"content\":\"~$北京市居住证.docx\\n~$aching plan IK4 12月大班.docx\\n成果展示(1).mov\\n谷歌智能体技术白皮书2025（中文）.pdf\\n归档.zip\\n聊聊 AI Coding 时代生产力工具的工程架构-杨吉明.pdf\\n武戈.pdf\\n赵慧敏.pdf\\nAI代码插件核心能力建设.md\\ndownload.zip\\njoycoder-fe-2.9.0-430版.vsix\\nry.bat\\nthemes.zip\\n技术调研/\\n简历/\\n日常文档/\\n应用/\\naaa_64/\\nfont_4926664_4idpd6g7pkh/\\nxmind/\"}", "partial": false, "isProtected": false}, {"ts": 1751461581789, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[list_files for '/Users/<USER>/Desktop'] Result:\\n\\n~$北京市居住证.docx\\n~$aching plan IK4 12月大班.docx\\n成果展示(1).mov\\n谷歌智能体技术白皮书2025（中文）.pdf\\n归档.zip\\n聊聊 AI Coding 时代生产力工具的工程架构-杨吉明.pdf\\n武戈.pdf\\n赵慧敏.pdf\\nAI代码插件核心能力建设.md\\ndownload.zip\\njoycoder-fe-2.9.0-430版.vsix\\nry.bat\\nthemes.zip\\n技术调研/\\n简历/\\n日常文档/\\n应用/\\naaa_64/\\nfont_4926664_4idpd6g7pkh/\\nxmind/\\n\\n<environment_details>\\n# VSCode Visible Files\\npackages/vscode-IDE/package.json\\n\\n# VSCode Open Tabs\\npackages/agent-setting/src/components/AgentManagement/index.tsx,packages/agent-driven/src/core/config/CustomModesManager.ts,packages/agent-setting/src/components/setting/index.tsx,packages/agent-setting/src/components/mcp/index.tsx,packages/agent-setting/src/components/mcp/McpInstalledView.tsx,packages/vscode-IDE/package.json\\n\\n# Current Time\\n2025/7/2 下午9:06:21 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n19,695 (15%)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>code</slug>\\n<name>💻 Code</name>\\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\\n</environment_details>\",\"tokensIn\":20197,\"tokensOut\":1587,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1751461586350, "type": "say", "say": "text", "text": "", "partial": false}, {"ts": 1751461587064, "type": "ask", "ask": "tool", "text": "{\"tool\":\"newFileCreated\",\"path\":\"/Users/<USER>/Desktop/taro-overview.md\",\"content\":\"# Taro 多端统一开发解决方案\\n\\n## 简介\\nTaro 是一个开放式跨端跨框架解决方案，支持使用 React/Vue/Nerv 等框架来开发微信/京东/百度/支付宝/字节跳动/QQ 小程序/H5/React Native 等应用。\\n\\n## 产物支持范围\\n- 小程序平台\\n  - 微信小程序\\n  - 京东小程序\\n  - 百度小程序\\n  - 支付宝小程序\\n  - 字节跳动小程序\\n  - QQ小程序\\n- H5平台\\n- React Native\\n- Harmony OS（鸿蒙）应用\\n\\n## 功能分类\\n1. 框架支持\\n   - React 框架支持\\n   - Vue 框架支持\\n   - Nerv 框架支持\\n\\n2. 跨端能力\\n   - 统一的开发体验\\n   - 多端代码复用\\n   - 条件编译\\n   - 统一的接口规范\\n\\n3. 性能优化\\n   - 小程序预渲染\\n   - 分包配置\\n   - 按需加载\\n\\n## 项目结构\\n```\\n├── dist                   编译结果目录\\n├── config                 配置目录\\n|   ├── dev.js            开发时配置\\n|   ├── index.js          默认配置\\n|   └── prod.js           打包时配置\\n├── src                   源码目录\\n|   ├── pages             页面文件目录\\n|   |   └── index        index页面目录\\n|   |       ├── index.js  index页面逻辑\\n|   |       └── index.css index页面样式\\n|   ├── app.css          项目总通用样式\\n|   └── app.js           项目入口文件\\n└── package.json\\n```\\n\\n## 底层原理\\n1. 编译原理\\n   - 使用 Babel 进行语法转换\\n   - AST 操作实现代码转换\\n   - 模板引擎处理模板语法\\n\\n2. 运行时框架\\n   - 统一的事件系统\\n   - 虚拟DOM Diff算法\\n   - 生命周期管理\\n\\n## 初始化项目\\n```bash\\n# 使用 npm 安装 CLI\\nnpm install -g @tarojs/cli\\n\\n# 初始化项目\\ntaro init myApp\\n\\n# 开发命令\\nnpm run dev:weapp     # 微信小程序\\nnpm run dev:h5        # H5\\nnpm run dev:rn        # React Native\\n```\\n\\n## 生态系统\\n1. UI 框架\\n   - Taro UI：多端 UI 组件库\\n   - NutUI：京东风格的轻量级组件库\\n\\n2. 状态管理\\n   - Redux\\n   - Mobx\\n   - Pinia（Vue）\\n\\n3. 开发工具\\n   - Taro CLI\\n   - Taro Doctor\\n   - VSCode 插件\\n\\n## 样式开发\\n1. 支持的预处理器\\n   - Sass/Scss\\n   - Less\\n   - Stylus\\n\\n2. 样式导入方式\\n   - 全局样式\\n   - 组件样式\\n   - CSS Modules\\n\\n3. 平台适配\\n   - 条件编译\\n   - Platform API\\n   - 样式隔离\\n\\n## API 分类\\n1. 基础 API\\n   - 网络请求\\n   - 数据缓存\\n   - 文件系统\\n   - 路由导航\\n\\n2. 界面 API\\n   - 交互反馈\\n   - 导航栏\\n   - 菜单\\n   - 窗口\\n\\n3. 设备 API\\n   - 网络状态\\n   - 地理位置\\n   - 加速度计\\n   - 罗盘\\n\\n## Hooks 分类\\n1. 生命周期 Hooks\\n   - useDidShow\\n   - useDidHide\\n   - useReady\\n   - useRouter\\n\\n2. 功能性 Hooks\\n   - usePageScroll\\n   - usePullDownRefresh\\n   - useReachBottom\\n   - useShareAppMessage\\n\\n## 组件分类\\n1. 视图容器\\n   - View\\n   - ScrollView\\n   - Swiper\\n   - MovableView\\n\\n2. 基础内容\\n   - Text\\n   - Icon\\n   - Progress\\n   - RichText\\n\\n3. 表单组件\\n   - Button\\n   - Form\\n   - Input\\n   - Checkbox\\n   - Radio\\n\\n4. 媒体组件\\n   - Image\\n   - Video\\n   - Camera\\n   - Audio\\n\\n## 配置详解\\n1. 全局配置\\n```javascript\\n// app.config.js\\nexport default {\\n  pages: ['pages/index/index'],\\n  window: {\\n    navigationBarTitleText: '应用标题'\\n  }\\n}\\n```\\n\\n2. 页面配置\\n```javascript\\n// page.config.js\\nexport default {\\n  navigationBarTitleText: '页面标题',\\n  enablePullDownRefresh: true\\n}\\n```\\n\\n3. 项目配置\\n```javascript\\n// config/index.js\\nconst config = {\\n  projectName: 'myProject',\\n  date: '2023-7-2',\\n  designWidth: 750,\\n  deviceRatio: {\\n    640: 2.34 / 2,\\n    750: 1,\\n    828: 1.81 / 2\\n  },\\n  sourceRoot: 'src',\\n  outputRoot: 'dist'\\n}\\n\\nmodule.exports = function (merge) {\\n  if (process.env.NODE_ENV === 'development') {\\n    return merge({}, config, require('./dev'))\\n  }\\n  return merge({}, config, require('./prod'))\\n}\",\"isOutsideWorkspace\":true,\"isProtected\":false}", "partial": false, "isProtected": false}, {"ts": 1751461629078, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[write_to_file for '/Users/<USER>/Desktop/taro-overview.md'] Result:\\n\\n<file_write_result>\\n<path>/Users/<USER>/Desktop/taro-overview.md</path>\\n<operation>created</operation>\\n<notice>\\n<i>You do not need to re-read the file, as you have seen all changes</i>\\n<i>Proceed with the task using these changes as the new baseline.</i>\\n</notice>\\n</file_write_result>\\n\\n\\n<environment_details>\\n# VSCode Visible Files\\n../../../Desktop/taro-overview.md\\n\\n# VSCode Open Tabs\\npackages/agent-setting/src/components/AgentManagement/index.tsx,packages/agent-driven/src/core/config/CustomModesManager.ts,packages/agent-setting/src/components/setting/index.tsx,packages/agent-setting/src/components/mcp/index.tsx,packages/agent-setting/src/components/mcp/McpInstalledView.tsx,packages/vscode-IDE/package.json,../../../Desktop/taro-overview.md\\n\\n# Current Time\\n2025/7/2 下午9:07:09 (Asia/Shanghai, UTC+8:00)\\n\\n# Current Context Size (Tokens)\\n21,784 (17%)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>code</slug>\\n<name>💻 Code</name>\\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\\n</environment_details>\",\"tokensIn\":22171,\"tokensOut\":335,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1751461634249, "type": "say", "say": "text", "text": "我已经成功创建了一个全面的Taro文档，包含了所有要求的内容点：\n1. 简介\n2. 产物支持范围\n3. 功能分类\n4. 项目结构\n5. 底层原理\n6. 初始化项目\n7. 生态系统\n8. 样式开发\n9. API分类\n10. Hooks分类\n11. 组件分类\n12. 配置详解\n\n文档已经保存到用户桌面的taro-overview.md文件中。现在可以完成任务了。", "partial": false}, {"ts": 1751461638536, "type": "say", "say": "completion_result", "text": "我已经创建了一份完整的Taro文档 'taro-overview.md'，并将其保存到您的桌面。该文档涵盖了Taro的所有主要方面，包括简介、产物支持范围、功能分类、项目结构、底层原理、初始化步骤、生态系统、样式开发、API分类、Hooks分类、组件分类以及详细的配置说明。文档采用Markdown格式编写，便于阅读和维护。", "partial": false}, {"ts": 1751461641647, "type": "ask", "ask": "completion_result", "text": ""}, {"ts": 1751874292791, "type": "ask", "ask": "resume_completed_task"}]