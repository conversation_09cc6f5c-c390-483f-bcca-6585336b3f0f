{"files_in_context": [{"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "read_tool", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": null}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750504423288}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750506058323}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750506215803}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750506461813}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750506480320}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750506546954}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750506604781}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750506858291}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750506980854}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750507084602}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750507163241}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750507182003}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750507547402}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750508738761}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750508740800}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750508742399}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750508745391}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750508748550}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750508769811}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750509000800}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750509033383}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750510068580}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750510070108}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750510071992}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750510077574}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750519409080}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750519417304}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750519421005}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750519422657}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750519932657}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750519935818}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750561041319}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750561045380}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750561646401}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750563060005}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750567589824}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750567596376}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750567601628}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750567607604}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750567610640}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750567946046}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750568022223}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750568061932}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750578856000}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750578875463}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750578948310}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750650738888}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750650886191}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750656246083}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750656250188}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750657447248}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750657452656}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750657544932}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750657590852}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750657706488}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750657909429}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750658162683}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750658318421}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750658614209}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750659297996}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750659316577}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750660408534}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750660412542}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750660480390}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750660487172}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750660975237}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750661274063}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750661289956}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750661295680}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750661304006}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750661306680}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750661333850}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750661438457}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750661440831}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750661442223}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750661498734}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750661500009}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750661528513}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750661530079}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750661540531}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750661541790}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750661543673}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "active", "record_source": "user_edited", "roo_read_date": 1750504342218, "roo_edit_date": null, "user_edit_date": 1750661569904}]}