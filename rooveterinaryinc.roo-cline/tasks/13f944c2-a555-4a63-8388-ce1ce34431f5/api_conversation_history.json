[{"role": "user", "content": [{"type": "text", "text": "<task>\nvscode插件放到vscode中，vscode启动时，插件自动能加载吗\n</task>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n\n\n# VSCode Open Tabs\n\n\n# Current Time\n2025/7/18 下午2:26:48 (Asia/Shanghai, UTC+8:00)\n\n# Current Cost\n$0.00\n\n# Current Mode\n<slug>ask</slug>\n<name>❓ Ask</name>\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\n\n\n# Current Workspace Directory (/Users/<USER>/Documents/work/repos/vscode-copilot-chat) Files\nassets/\ndocs/\ndocs/media/\nscript/\nscript/electron/\nscript/setup/\nscript/test/\nscript/testGeneration/\nsrc/\nsrc/extension/\nsrc/extension/api/\nsrc/extension/api/vscode/\nsrc/extension/authentication/\nsrc/extension/authentication/vscode-node/\nsrc/extension/byok/\nsrc/extension/byok/common/\nsrc/extension/byok/node/\nsrc/extension/byok/vscode-node/\nsrc/extension/chat/\nsrc/extension/chat/vscode-node/\nsrc/extension/codeBlocks/\nsrc/extension/codeBlocks/node/\nsrc/extension/codeBlocks/node/test/\nsrc/extension/codeBlocks/vscode-node/\nsrc/extension/commands/\nsrc/extension/commands/node/\nsrc/extension/common/\nsrc/extension/configuration/\nsrc/extension/configuration/vscode-node/\nsrc/extension/context/\nsrc/extension/context/node/\nsrc/extension/context/node/resolvers/\nsrc/extension/context/vscode/\nsrc/extension/contextKeys/\nsrc/extension/contextKeys/vscode-node/\nsrc/extension/conversation/\nsrc/extension/conversation/common/\nsrc/extension/conversation/node/\nsrc/extension/conversation/vscode-node/\nsrc/extension/conversation/vscode-node/test/\nsrc/extension/conversationStore/\nsrc/extension/conversationStore/node/\nsrc/extension/extension/\nsrc/extension/extension/vscode/\nsrc/extension/extension/vscode-node/\nsrc/extension/extension/vscode-worker/\nsrc/extension/getting-started/\nsrc/extension/getting-started/common/\nsrc/extension/getting-started/vscode-node/\nsrc/extension/ignore/\nsrc/extension/ignore/vscode-node/\nsrc/extension/inlineChat/\nsrc/extension/inlineChat/node/\nsrc/extension/inlineChat/test/\nsrc/extension/inlineChat/test/vscode-node/\nsrc/extension/inlineChat/vscode-node/\nsrc/extension/inlineEdits/\nsrc/extension/inlineEdits/common/\nsrc/extension/inlineEdits/node/\nsrc/extension/inlineEdits/test/\nsrc/extension/inlineEdits/test/common/\nsrc/extension/inlineEdits/test/node/\nsrc/extension/inlineEdits/test/node/recordings/\nsrc/extension/inlineEdits/test/vscode-node/\nsrc/extension/inlineEdits/vscode-node/\nsrc/extension/inlineEdits/vscode-node/components/\nsrc/extension/inlineEdits/vscode-node/components/test/\nsrc/extension/inlineEdits/vscode-node/features/\nsrc/extension/inlineEdits/vscode-node/features/diagnosticsBasedCompletions/\nsrc/extension/inlineEdits/vscode-node/parts/\nsrc/extension/inlineEdits/vscode-node/utils/\nsrc/extension/intents/\nsrc/extension/intents/common/\nsrc/extension/intents/node/\nsrc/extension/intents/node/testIntent/\nsrc/extension/intents/test/\nsrc/extension/intents/test/node/\nsrc/extension/intents/vscode-node/\nsrc/extension/linkify/\nsrc/extension/linkify/common/\nsrc/extension/linkify/test/\nsrc/extension/linkify/test/node/\nsrc/extension/linkify/test/vscode-node/\nsrc/extension/linkify/vscode-node/\nsrc/extension/log/\nsrc/extension/log/vscode-node/\nsrc/extension/mcp/\nsrc/extension/mcp/vscode-node/\nsrc/extension/notebook/\nsrc/extension/notebook/vscode-node/\nsrc/extension/onboardDebug/\nsrc/extension/onboardDebug/common/\nsrc/extension/onboardDebug/node/\nsrc/extension/onboardDebug/node/copilotDebugWorker/\nsrc/extension/onboardDebug/test/\nsrc/extension/onboardDebug/test/node/\nsrc/extension/onboardDebug/vscode/\nsrc/extension/onboardDebug/vscode-node/\nsrc/extension/prompt/\nsrc/extension/prompt/common/\nsrc/extension/prompt/node/\nsrc/extension/prompt/node/test/\nsrc/extension/prompt/node/test/__snapshots__/\nsrc/extension/prompt/test/\nsrc/extension/prompt/test/common/\nsrc/extension/prompt/test/node/\nsrc/extension/prompt/vscode-node/\nsrc/extension/prompts/\nsrc/extension/prompts/node/\nsrc/extension/prompts/node/agent/\nsrc/extension/prompts/node/agent/test/\nsrc/extension/prompts/node/agent/test/__snapshots__/\nsrc/extension/prompts/node/base/\nsrc/extension/prompts/node/codeMapper/\nsrc/extension/prompts/node/devcontainer/\nsrc/extension/prompts/node/feedback/\nsrc/extension/prompts/node/git/\nsrc/extension/prompts/node/github/\nsrc/extension/prompts/node/inline/\nsrc/extension/prompts/node/inline/summarizedDocument/\nsrc/extension/prompts/node/inline/utils/\nsrc/extension/prompts/node/notebook/\nsrc/extension/prompts/node/notebook/alternateContent/\nsrc/extension/prompts/node/notebook/alternateContent/test/\nsrc/extension/prompts/node/notebook/alternateContent/test/fixtures/\nsrc/extension/prompts/node/panel/\nsrc/extension/prompts/node/panel/newWorkspace/\nsrc/extension/prompts/node/panel/test/\nsrc/extension/prompts/node/panel/test/__snapshots__/\nsrc/extension/prompts/node/panel/workspace/\nsrc/extension/prompts/node/panel/workspace/test/\nsrc/extension/prompts/node/settingsEditor/\nsrc/extension/prompts/node/test/\nsrc/extension/prompts/node/test/fixtures/\nsrc/extension/relatedFiles/\nsrc/extension/relatedFiles/node/\nsrc/extension/relatedFiles/vscode-node/\nsrc/extension/renameSuggestions/\nsrc/extension/renameSuggestions/common/\nsrc/extension/renameSuggestions/node/\nsrc/extension/renameSuggestions/test/\nsrc/extension/renameSuggestions/test/common/\nsrc/extension/renameSuggestions/test/node/\nsrc/extension/review/\nsrc/extension/review/node/\nsrc/extension/search/\nsrc/extension/search/vscode-node/\nsrc/extension/settingsSchema/\nsrc/extension/settingsSchema/vscode-node/\nsrc/extension/survey/\nsrc/extension/survey/vscode-node/\nsrc/extension/telemetry/\nsrc/extension/telemetry/common/\nsrc/extension/telemetry/vscode/\nsrc/extension/test/\nsrc/extension/test/common/\nsrc/extension/test/node/\nsrc/extension/test/node/fixtures/\nsrc/extension/test/node/fixtures/gitdiff/\nsrc/extension/test/node/fixtures/patch/\nsrc/extension/test/node/fixtures/patch/basic/\nsrc/extension/test/node/fixtures/patch/indentation/\nsrc/extension/test/node/fixtures/patch/out-20240514-153256/\nsrc/extension/test/node/fixtures/pseudodiff/\nsrc/extension/test/vscode-node/\nsrc/extension/testing/\nsrc/extension/testing/common/\nsrc/extension/testing/node/\nsrc/extension/testing/vscode/\nsrc/extension/tools/\nsrc/extension/tools/common/\nsrc/extension/tools/common/terminal/\nsrc/extension/tools/node/\nsrc/extension/tools/node/applyPatch/\nsrc/extension/tools/node/newWorkspace/\nsrc/extension/tools/node/test/\nsrc/extension/tools/node/test/__snapshots__/\nsrc/extension/tools/node/test/editFileToolUtilsFixtures/\nsrc/extension/tools/test/\nsrc/extension/tools/test/common/\nsrc/extension/tools/test/node/\nsrc/extension/tools/test/node/applyPatch/\nsrc/extension/tools/test/node/applyPatch/corpus/\nsrc/extension/tools/vscode-node/\nsrc/extension/typescriptContext/\nsrc/extension/typescriptContext/common/\nsrc/extension/typescriptContext/serverPlugin/\nsrc/extension/typescriptContext/serverPlugin/fixtures/\nsrc/extension/typescriptContext/serverPlugin/fixtures/p1/\nsrc/extension/typescriptContext/serverPlugin/fixtures/p1/source/\nsrc/extension/typescriptContext/serverPlugin/fixtures/p2/\nsrc/extension/typescriptContext/serverPlugin/fixtures/p2/source/\nsrc/extension/typescriptContext/serverPlugin/fixtures/p10/\nsrc/extension/typescriptContext/serverPlugin/fixtures/p10/source/\nsrc/extension/typescriptContext/serverPlugin/fixtures/p11/\nsrc/extension/typescriptContext/serverPlugin/fixtures/p11/source/\nsrc/extension/typescriptContext/serverPlugin/fixtures/p12/\nsrc/extension/typescriptContext/serverPlugin/fixtures/p12/source/\nsrc/extension/typescriptContext/serverPlugin/fixtures/p13/\nsrc/extension/typescriptContext/serverPlugin/fixtures/p13/source/\n\n(File list truncated. Use list_files on specific subdirectories if you need to explore further.)\nYou have not created a todo list yet. Create one with `update_todo_list` if your task is complicated or involves multiple steps.\n</environment_details>"}], "ts": 1752820008941}, {"role": "assistant", "content": [{"type": "text", "text": "<thinking>\n我需要解释VSCode插件的加载机制。从环境信息中我看到这是一个VSCode插件项目，这有助于我更准确地回答这个问题。让我详细解释VSCode插件的自动加载机制。\n</thinking>\n\n是的，VSCode插件在VSCode启动时可以自动加载，这个过程由VSCode的插件系统自动管理。具体来说：\n\n1. 插件安装位置：\n- 用户插件目录：`~/.vscode/extensions/`（macOS/Linux）或 `%USERPROFILE%\\.vscode\\extensions\\`（Windows）\n- VSCode安装目录下的插件目录\n\n2. 加载时机：\n- VSCode启动时会自动扫描这些插件目录\n- 根据插件的[`package.json`](src/package.json)中的`activationEvents`配置决定加载时机：\n  - `*`：VSCode启动时立即加载\n  - `onStartupFinished`：VSCode启动完成后加载\n  - `onLanguage`：打开特定语言文件时加载\n  - `onCommand`：执行特定命令时加载\n  - 其他触发条件...\n\n3. 加载流程：\n```mermaid\ngraph TD\n    A[VSCode启动] --> B[扫描插件目录]\n    B --> C[读取package.json]\n    C --> D{检查activationEvents}\n    D -->|* 配置| E[立即加载]\n    D -->|特定事件| F[等待事件触发]\n    F --> G[按需加载]\n```\n\n4. 注意事项：\n- 插件必须在[`package.json`](src/package.json)中正确配置`engines.vscode`版本要求\n- 建议使用合适的`activationEvents`来优化VSCode的启动性能\n- 插件可以通过`contributes`字段注册各种扩展点\n\n这种机制确保了插件能够在合适的时机自动加载，同时也保证了VSCode的性能和用户体验。"}], "ts": 1752820028168}, {"role": "user", "content": [{"type": "text", "text": "[ERROR] You did not use a tool in your previous response! Please retry with a tool use.\n\n# Reminder: Instructions for Tool Use\n\nTool uses are formatted using XML-style tags. The tool name itself becomes the XML tag name. Each parameter is enclosed within its own set of tags. Here's the structure:\n\n<actual_tool_name>\n<parameter1_name>value1</parameter1_name>\n<parameter2_name>value2</parameter2_name>\n...\n</actual_tool_name>\n\nFor example, to use the attempt_completion tool:\n\n<attempt_completion>\n<result>\nI have completed the task...\n</result>\n</attempt_completion>\n\nAlways use the actual tool name as the XML tag name for proper parsing and execution.\n\n# Next Steps\n\nIf you have completed the user's task, use the attempt_completion tool. \nIf you require additional information from the user, use the ask_followup_question tool. \nOtherwise, if you have not completed the task and do not need additional information, then proceed with the next step of the task. \n(This is an automated message, so do not respond to it conversationally.)"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n\n\n# VSCode Open Tabs\n\n\n# Current Time\n2025/7/18 下午2:27:08 (Asia/Shanghai, UTC+8:00)\n\n# Current Cost\n$0.00\n\n# Current Mode\n<slug>ask</slug>\n<name>❓ Ask</name>\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\nYou have not created a todo list yet. Create one with `update_todo_list` if your task is complicated or involves multiple steps.\n</environment_details>"}], "ts": 1752820028175}, {"role": "assistant", "content": [{"type": "text", "text": "<thinking>\n让我查看这个项目的package.json文件，以便更准确地解释VSCode插件的加载机制。\n</thinking>\n\n<read_file>\n<args>\n<file>\n<path>package.json</path>\n</file>\n</args>\n</read_file>\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]"}], "ts": 1752820033234}, {"role": "user", "content": [{"type": "text", "text": "[read_file for 'package.json'. Reading multiple files at once is more efficient for the LLM. If other files are relevant to your current task, please read them simultaneously.] Result:"}, {"type": "text", "text": "<files>\n<file><path>package.json</path>\n<content lines=\"1-500\">\n  1 | {\n  2 | \t\"name\": \"copilot-chat\",\n  3 | \t\"displayName\": \"GitHub Copilot Chat\",\n  4 | \t\"description\": \"AI chat features powered by Copilot\",\n  5 | \t\"version\": \"0.30.0\",\n  6 | \t\"build\": \"1\",\n  7 | \t\"internalAIKey\": \"1058ec22-3c95-4951-8443-f26c1f325911\",\n  8 | \t\"internalLargeStorageAriaKey\": \"ec712b3202c5462fb6877acae7f1f9d7-c19ad55e-3e3c-4f99-984b-827f6d95bd9e-6917\",\n  9 | \t\"ariaKey\": \"0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255\",\n 10 | \t\"buildType\": \"dev\",\n 11 | \t\"publisher\": \"GitHub\",\n 12 | \t\"homepage\": \"https://github.com/features/copilot?editor=vscode\",\n 13 | \t\"license\": \"SEE LICENSE IN LICENSE.txt\",\n 14 | \t\"repository\": {\n 15 | \t\t\"type\": \"git\",\n 16 | \t\t\"url\": \"https://github.com/microsoft/vscode-copilot-chat\"\n 17 | \t},\n 18 | \t\"bugs\": {\n 19 | \t\t\"url\": \"https://github.com/microsoft/vscode/issues\"\n 20 | \t},\n 21 | \t\"qna\": \"https://github.com/github-community/community/discussions/categories/copilot\",\n 22 | \t\"icon\": \"assets/copilot.png\",\n 23 | \t\"pricing\": \"Trial\",\n 24 | \t\"engines\": {\n 25 | \t\t\"vscode\": \"^1.103.0\",\n 26 | \t\t\"npm\": \">=9.0.0\",\n 27 | \t\t\"node\": \">=22.14.0\"\n 28 | \t},\n 29 | \t\"categories\": [\n 30 | \t\t\"AI\",\n 31 | \t\t\"Chat\",\n 32 | \t\t\"Programming Languages\",\n 33 | \t\t\"Machine Learning\"\n 34 | \t],\n 35 | \t\"keywords\": [\n 36 | \t\t\"ai\",\n 37 | \t\t\"openai\",\n 38 | \t\t\"codex\",\n 39 | \t\t\"pilot\",\n 40 | \t\t\"snippets\",\n 41 | \t\t\"documentation\",\n 42 | \t\t\"autocomplete\",\n 43 | \t\t\"intellisense\",\n 44 | \t\t\"refactor\",\n 45 | \t\t\"javascript\",\n 46 | \t\t\"python\",\n 47 | \t\t\"typescript\",\n 48 | \t\t\"php\",\n 49 | \t\t\"go\",\n 50 | \t\t\"golang\",\n 51 | \t\t\"ruby\",\n 52 | \t\t\"c++\",\n 53 | \t\t\"c#\",\n 54 | \t\t\"java\",\n 55 | \t\t\"kotlin\",\n 56 | \t\t\"co-pilot\"\n 57 | \t],\n 58 | \t\"badges\": [\n 59 | \t\t{\n 60 | \t\t\t\"url\": \"https://img.shields.io/badge/GitHub%20Copilot-Subscription%20Required-orange\",\n 61 | \t\t\t\"href\": \"https://github.com/github-copilot/signup?editor=vscode\",\n 62 | \t\t\t\"description\": \"%github.copilot.badge.signUp%\"\n 63 | \t\t},\n 64 | \t\t{\n 65 | \t\t\t\"url\": \"https://img.shields.io/github/stars/github/copilot-docs?style=social\",\n 66 | \t\t\t\"href\": \"https://github.com/github/copilot-docs\",\n 67 | \t\t\t\"description\": \"%github.copilot.badge.star%\"\n 68 | \t\t},\n 69 | \t\t{\n 70 | \t\t\t\"url\": \"https://img.shields.io/youtube/channel/views/UC7c3Kb6jYCRj4JOHHZTxKsQ?style=social\",\n 71 | \t\t\t\"href\": \"https://www.youtube.com/@GitHub/search?query=copilot\",\n 72 | \t\t\t\"description\": \"%github.copilot.badge.youtube%\"\n 73 | \t\t},\n 74 | \t\t{\n 75 | \t\t\t\"url\": \"https://img.shields.io/twitter/follow/github?style=social\",\n 76 | \t\t\t\"href\": \"https://twitter.com/github\",\n 77 | \t\t\t\"description\": \"%github.copilot.badge.twitter%\"\n 78 | \t\t}\n 79 | \t],\n 80 | \t\"activationEvents\": [\n 81 | \t\t\"onStartupFinished\",\n 82 | \t\t\"onLanguageModelChat:copilot\",\n 83 | \t\t\"onUri\",\n 84 | \t\t\"onFileSystem:ccreq\",\n 85 | \t\t\"onFileSystem:ccsettings\"\n 86 | \t],\n 87 | \t\"main\": \"./dist/extension\",\n 88 | \t\"l10n\": \"./l10n\",\n 89 | \t\"enabledApiProposals\": [\n 90 | \t\t\"extensionsAny\",\n 91 | \t\t\"newSymbolNamesProvider\",\n 92 | \t\t\"interactive\",\n 93 | \t\t\"codeActionAI\",\n 94 | \t\t\"activeComment\",\n 95 | \t\t\"commentReveal\",\n 96 | \t\t\"contribCommentThreadAdditionalMenu\",\n 97 | \t\t\"contribCommentsViewThreadMenus\",\n 98 | \t\t\"documentFiltersExclusive\",\n 99 | \t\t\"embeddings\",\n100 | \t\t\"findTextInFiles\",\n101 | \t\t\"findTextInFiles2\",\n102 | \t\t\"findFiles2@2\",\n103 | \t\t\"textSearchProvider\",\n104 | \t\t\"terminalDataWriteEvent\",\n105 | \t\t\"terminalExecuteCommandEvent\",\n106 | \t\t\"terminalSelection\",\n107 | \t\t\"terminalQuickFixProvider\",\n108 | \t\t\"mappedEditsProvider\",\n109 | \t\t\"aiRelatedInformation\",\n110 | \t\t\"aiSettingsSearch\",\n111 | \t\t\"chatParticipantAdditions\",\n112 | \t\t\"chatEditing\",\n113 | \t\t\"defaultChatParticipant@4\",\n114 | \t\t\"contribSourceControlInputBoxMenu\",\n115 | \t\t\"authLearnMore\",\n116 | \t\t\"testObserver\",\n117 | \t\t\"aiTextSearchProvider@2\",\n118 | \t\t\"chatParticipantPrivate@9\",\n119 | \t\t\"chatProvider\",\n120 | \t\t\"contribDebugCreateConfiguration\",\n121 | \t\t\"chatReferenceDiagnostic\",\n122 | \t\t\"textSearchProvider2\",\n123 | \t\t\"chatReferenceBinaryData\",\n124 | \t\t\"languageModelSystem\",\n125 | \t\t\"languageModelCapabilities\",\n126 | \t\t\"inlineCompletionsAdditions\",\n127 | \t\t\"languageModelDataPart@3\",\n128 | \t\t\"chatStatusItem\",\n129 | \t\t\"taskProblemMatcherStatus\",\n130 | \t\t\"contribLanguageModelToolSets\",\n131 | \t\t\"textDocumentChangeReason\",\n132 | \t\t\"resolvers\",\n133 | \t\t\"taskExecutionTerminal\",\n134 | \t\t\"dataChannels\"\n135 | \t],\n136 | \t\"contributes\": {\n137 | \t\t\"languageModelTools\": [\n138 | \t\t\t{\n139 | \t\t\t\t\"name\": \"copilot_searchCodebase\",\n140 | \t\t\t\t\"toolReferenceName\": \"codebase\",\n141 | \t\t\t\t\"displayName\": \"%copilot.tools.searchCodebase.name%\",\n142 | \t\t\t\t\"icon\": \"$(folder)\",\n143 | \t\t\t\t\"canBeReferencedInPrompt\": true,\n144 | \t\t\t\t\"userDescription\": \"%copilot.codebase.tool.description%\",\n145 | \t\t\t\t\"modelDescription\": \"Run a natural language search for relevant code or documentation comments from the user's current workspace. Returns relevant code snippets from the user's current workspace if it is large, or the full contents of the workspace if it is small.\",\n146 | \t\t\t\t\"tags\": [\n147 | \t\t\t\t\t\"codesearch\",\n148 | \t\t\t\t\t\"vscode_codesearch\"\n149 | \t\t\t\t],\n150 | \t\t\t\t\"inputSchema\": {\n151 | \t\t\t\t\t\"type\": \"object\",\n152 | \t\t\t\t\t\"properties\": {\n153 | \t\t\t\t\t\t\"query\": {\n154 | \t\t\t\t\t\t\t\"type\": \"string\",\n155 | \t\t\t\t\t\t\t\"description\": \"The query to search the codebase for. Should contain all relevant context. Should ideally be text that might appear in the codebase, such as function names, variable names, or comments.\"\n156 | \t\t\t\t\t\t}\n157 | \t\t\t\t\t},\n158 | \t\t\t\t\t\"required\": [\n159 | \t\t\t\t\t\t\"query\"\n160 | \t\t\t\t\t]\n161 | \t\t\t\t}\n162 | \t\t\t},\n163 | \t\t\t{\n164 | \t\t\t\t\"name\": \"copilot_searchWorkspaceSymbols\",\n165 | \t\t\t\t\"toolReferenceName\": \"symbols\",\n166 | \t\t\t\t\"displayName\": \"%copilot.tools.searchWorkspaceSymbols.name%\",\n167 | \t\t\t\t\"icon\": \"$(symbol)\",\n168 | \t\t\t\t\"userDescription\": \"%copilot.workspaceSymbols.tool.description%\",\n169 | \t\t\t\t\"modelDescription\": \"Search the user's workspace for code symbols using language services. Use this tool when the user is looking for a specific symbol in their workspace.\",\n170 | \t\t\t\t\"tags\": [\n171 | \t\t\t\t\t\"vscode_codesearch\"\n172 | \t\t\t\t],\n173 | \t\t\t\t\"inputSchema\": {\n174 | \t\t\t\t\t\"type\": \"object\",\n175 | \t\t\t\t\t\"properties\": {\n176 | \t\t\t\t\t\t\"symbolName\": {\n177 | \t\t\t\t\t\t\t\"type\": \"string\",\n178 | \t\t\t\t\t\t\t\"description\": \"The symbol to search for, such as a function name, class name, or variable name.\"\n179 | \t\t\t\t\t\t}\n180 | \t\t\t\t\t},\n181 | \t\t\t\t\t\"required\": [\n182 | \t\t\t\t\t\t\"symbolName\"\n183 | \t\t\t\t\t]\n184 | \t\t\t\t}\n185 | \t\t\t},\n186 | \t\t\t{\n187 | \t\t\t\t\"name\": \"copilot_listCodeUsages\",\n188 | \t\t\t\t\"toolReferenceName\": \"usages\",\n189 | \t\t\t\t\"displayName\": \"%copilot.tools.listCodeUsages.name%\",\n190 | \t\t\t\t\"icon\": \"$(references)\",\n191 | \t\t\t\t\"canBeReferencedInPrompt\": true,\n192 | \t\t\t\t\"userDescription\": \"%copilot.listCodeUsages.tool.description%\",\n193 | \t\t\t\t\"modelDescription\": \"Request to list all usages (references, definitions, implementations etc) of a function, class, method, variable etc. Use this tool when \\n1. Looking for a sample implementation of an interface or class\\n2. Checking how a function is used throughout the codebase.\\n3. Including and updating all usages when changing a function, method, or constructor\",\n194 | \t\t\t\t\"tags\": [\n195 | \t\t\t\t\t\"vscode_codesearch\"\n196 | \t\t\t\t],\n197 | \t\t\t\t\"inputSchema\": {\n198 | \t\t\t\t\t\"type\": \"object\",\n199 | \t\t\t\t\t\"properties\": {\n200 | \t\t\t\t\t\t\"symbolName\": {\n201 | \t\t\t\t\t\t\t\"type\": \"string\",\n202 | \t\t\t\t\t\t\t\"description\": \"The name of the symbol, such as a function name, class name, method name, variable name, etc.\"\n203 | \t\t\t\t\t\t},\n204 | \t\t\t\t\t\t\"filePaths\": {\n205 | \t\t\t\t\t\t\t\"type\": \"array\",\n206 | \t\t\t\t\t\t\t\"description\": \"One or more file paths which likely contain the definition of the symbol. For instance the file which declares a class or function. This is optional but will speed up the invocation of this tool and improve the quality of its output.\",\n207 | \t\t\t\t\t\t\t\"items\": {\n208 | \t\t\t\t\t\t\t\t\"type\": \"string\"\n209 | \t\t\t\t\t\t\t}\n210 | \t\t\t\t\t\t}\n211 | \t\t\t\t\t},\n212 | \t\t\t\t\t\"required\": [\n213 | \t\t\t\t\t\t\"symbolName\"\n214 | \t\t\t\t\t]\n215 | \t\t\t\t}\n216 | \t\t\t},\n217 | \t\t\t{\n218 | \t\t\t\t\"name\": \"copilot_getVSCodeAPI\",\n219 | \t\t\t\t\"toolReferenceName\": \"vscodeAPI\",\n220 | \t\t\t\t\"displayName\": \"%copilot.tools.getVSCodeAPI.name%\",\n221 | \t\t\t\t\"icon\": \"$(references)\",\n222 | \t\t\t\t\"userDescription\": \"%copilot.vscode.tool.description%\",\n223 | \t\t\t\t\"modelDescription\": \"Get relevant VS Code API references to answer questions about VS Code extension development. Use this tool when the user asks about VS Code APIs, capabilities, or best practices related to developing VS Code extensions. Use it in all VS Code extension development workspaces.\",\n224 | \t\t\t\t\"inputSchema\": {\n225 | \t\t\t\t\t\"type\": \"object\",\n226 | \t\t\t\t\t\"properties\": {\n227 | \t\t\t\t\t\t\"query\": {\n228 | \t\t\t\t\t\t\t\"type\": \"string\",\n229 | \t\t\t\t\t\t\t\"description\": \"The query to search vscode documentation for. Should contain all relevant context.\"\n230 | \t\t\t\t\t\t}\n231 | \t\t\t\t\t},\n232 | \t\t\t\t\t\"required\": [\n233 | \t\t\t\t\t\t\"query\"\n234 | \t\t\t\t\t]\n235 | \t\t\t\t},\n236 | \t\t\t\t\"tags\": [],\n237 | \t\t\t\t\"canBeReferencedInPrompt\": true\n238 | \t\t\t},\n239 | \t\t\t{\n240 | \t\t\t\t\"displayName\": \"%copilot.tools.think.name%\",\n241 | \t\t\t\t\"name\": \"copilot_think\",\n242 | \t\t\t\t\"tags\": [],\n243 | \t\t\t\t\"when\": \"config.github.copilot.chat.agent.thinkingTool\",\n244 | \t\t\t\t\"modelDescription\": \"Use this tool to think deeply about the user's request and organize your thoughts. This tool helps improve response quality by allowing the model to consider the request carefully, brainstorm solutions, and plan complex tasks. It's particularly useful for:\\n\\n1. Exploring repository issues and brainstorming bug fixes\\n2. Analyzing test results and planning fixes\\n3. Planning complex refactoring approaches\\n4. Designing new features and architecture\\n5. Organizing debugging hypotheses\\n\\nThe tool logs your thought process for transparency but doesn't execute any code or make changes.\",\n245 | \t\t\t\t\"inputSchema\": {\n246 | \t\t\t\t\t\"type\": \"object\",\n247 | \t\t\t\t\t\"properties\": {\n248 | \t\t\t\t\t\t\"thoughts\": {\n249 | \t\t\t\t\t\t\t\"type\": \"string\",\n250 | \t\t\t\t\t\t\t\"description\": \"Your thoughts about the current task or problem. This should be a clear, structured explanation of your reasoning, analysis, or planning process.\"\n251 | \t\t\t\t\t\t}\n252 | \t\t\t\t\t},\n253 | \t\t\t\t\t\"required\": [\n254 | \t\t\t\t\t\t\"thoughts\"\n255 | \t\t\t\t\t]\n256 | \t\t\t\t}\n257 | \t\t\t},\n258 | \t\t\t{\n259 | \t\t\t\t\"name\": \"copilot_findFiles\",\n260 | \t\t\t\t\"toolReferenceName\": \"fileSearch\",\n261 | \t\t\t\t\"displayName\": \"%copilot.tools.findFiles.name%\",\n262 | \t\t\t\t\"modelDescription\": \"Search for files in the workspace by glob pattern. This only returns the paths of matching files. Use this tool when you know the exact filename pattern of the files you're searching for. Glob patterns match from the root of the workspace folder. Examples:\\n- **/*.{js,ts} to match all js/ts files in the workspace.\\n- src/** to match all files under the top-level src folder.\\n- **/foo/**/*.js to match all js files under any foo folder in the workspace.\",\n263 | \t\t\t\t\"tags\": [\n264 | \t\t\t\t\t\"vscode_codesearch\"\n265 | \t\t\t\t],\n266 | \t\t\t\t\"inputSchema\": {\n267 | \t\t\t\t\t\"type\": \"object\",\n268 | \t\t\t\t\t\"properties\": {\n269 | \t\t\t\t\t\t\"query\": {\n270 | \t\t\t\t\t\t\t\"type\": \"string\",\n271 | \t\t\t\t\t\t\t\"description\": \"Search for files with names or paths matching this glob pattern.\"\n272 | \t\t\t\t\t\t},\n273 | \t\t\t\t\t\t\"maxResults\": {\n274 | \t\t\t\t\t\t\t\"type\": \"number\",\n275 | \t\t\t\t\t\t\t\"description\": \"The maximum number of results to return. Do not use this unless necessary, it can slow things down. By default, only some matches are returned. If you use this and don't see what you're looking for, you can try again with a more specific query or a larger maxResults.\"\n276 | \t\t\t\t\t\t}\n277 | \t\t\t\t\t},\n278 | \t\t\t\t\t\"required\": [\n279 | \t\t\t\t\t\t\"query\"\n280 | \t\t\t\t\t]\n281 | \t\t\t\t}\n282 | \t\t\t},\n283 | \t\t\t{\n284 | \t\t\t\t\"name\": \"copilot_findTextInFiles\",\n285 | \t\t\t\t\"toolReferenceName\": \"textSearch\",\n286 | \t\t\t\t\"displayName\": \"%copilot.tools.findTextInFiles.name%\",\n287 | \t\t\t\t\"modelDescription\": \"Do a fast text search in the workspace. Use this tool when you want to search with an exact string or regex. If you are not sure what words will appear in the workspace, prefer using regex patterns with alternation (|) or character classes to search for multiple potential words at once instead of making separate searches. For example, use 'function|method|procedure' to look for all of those words at once. Use includePattern to search within files matching a specific pattern, or in a specific file, using a relative path. Use this tool when you want to see an overview of a particular file, instead of using read_file many times to look for code within a file.\",\n288 | \t\t\t\t\"tags\": [\n289 | \t\t\t\t\t\"vscode_codesearch\"\n290 | \t\t\t\t],\n291 | \t\t\t\t\"inputSchema\": {\n292 | \t\t\t\t\t\"type\": \"object\",\n293 | \t\t\t\t\t\"properties\": {\n294 | \t\t\t\t\t\t\"query\": {\n295 | \t\t\t\t\t\t\t\"type\": \"string\",\n296 | \t\t\t\t\t\t\t\"description\": \"The pattern to search for in files in the workspace. Use regex with alternation (e.g., 'word1|word2|word3') or character classes to find multiple potential words in a single search. Be sure to set the isRegexp property properly to declare whether it's a regex or plain text pattern. Is case-insensitive.\"\n297 | \t\t\t\t\t\t},\n298 | \t\t\t\t\t\t\"isRegexp\": {\n299 | \t\t\t\t\t\t\t\"type\": \"boolean\",\n300 | \t\t\t\t\t\t\t\"description\": \"Whether the pattern is a regex.\"\n301 | \t\t\t\t\t\t},\n302 | \t\t\t\t\t\t\"includePattern\": {\n303 | \t\t\t\t\t\t\t\"type\": \"string\",\n304 | \t\t\t\t\t\t\t\"description\": \"Search files matching this glob pattern. Will be applied to the relative path of files within the workspace. To search recursively inside a folder, use a proper glob pattern like \\\"src/folder/**\\\". Do not use | in includePattern.\"\n305 | \t\t\t\t\t\t},\n306 | \t\t\t\t\t\t\"maxResults\": {\n307 | \t\t\t\t\t\t\t\"type\": \"number\",\n308 | \t\t\t\t\t\t\t\"description\": \"The maximum number of results to return. Do not use this unless necessary, it can slow things down. By default, only some matches are returned. If you use this and don't see what you're looking for, you can try again with a more specific query or a larger maxResults.\"\n309 | \t\t\t\t\t\t}\n310 | \t\t\t\t\t},\n311 | \t\t\t\t\t\"required\": [\n312 | \t\t\t\t\t\t\"query\",\n313 | \t\t\t\t\t\t\"isRegexp\"\n314 | \t\t\t\t\t]\n315 | \t\t\t\t}\n316 | \t\t\t},\n317 | \t\t\t{\n318 | \t\t\t\t\"name\": \"copilot_applyPatch\",\n319 | \t\t\t\t\"displayName\": \"%copilot.tools.applyPatch.name%\",\n320 | \t\t\t\t\"toolReferenceName\": \"applyPatch\",\n321 | \t\t\t\t\"userDescription\": \"%copilot.tools.applyPatch.description%\",\n322 | \t\t\t\t\"modelDescription\": \"Edit text files. Do not use this tool to edit Jupyter notebooks. `apply_patch` allows you to execute a diff/patch against a text file, but the format of the diff specification is unique to this task, so pay careful attention to these instructions. To use the `apply_patch` command, you should pass a message of the following structure as \\\"input\\\":\\n\\n*** Begin Patch\\n[YOUR_PATCH]\\n*** End Patch\\n\\nWhere [YOUR_PATCH] is the actual content of your patch, specified in the following V4A diff format.\\n\\n*** [ACTION] File: [/absolute/path/to/file] -> ACTION can be one of Add, Update, or Delete.\\nAn example of a message that you might pass as \\\"input\\\" to this function, in order to apply a patch, is shown below.\\n\\n*** Begin Patch\\n*** Update File: /Users/<USER>/pygorithm/searching/binary_search.py\\n@@class BaseClass\\n@@    def search():\\n-        pass\\n+        raise NotImplementedError()\\n\\n@@class Subclass\\n@@    def search():\\n-        pass\\n+        raise NotImplementedError()\\n\\n*** End Patch\\nDo not use line numbers in this diff format.\",\n323 | \t\t\t\t\"inputSchema\": {\n324 | \t\t\t\t\t\"type\": \"object\",\n325 | \t\t\t\t\t\"properties\": {\n326 | \t\t\t\t\t\t\"input\": {\n327 | \t\t\t\t\t\t\t\"type\": \"string\",\n328 | \t\t\t\t\t\t\t\"description\": \"The edit patch to apply.\"\n329 | \t\t\t\t\t\t},\n330 | \t\t\t\t\t\t\"explanation\": {\n331 | \t\t\t\t\t\t\t\"type\": \"string\",\n332 | \t\t\t\t\t\t\t\"description\": \"A short description of what the tool call is aiming to achieve.\"\n333 | \t\t\t\t\t\t}\n334 | \t\t\t\t\t},\n335 | \t\t\t\t\t\"required\": [\n336 | \t\t\t\t\t\t\"input\",\n337 | \t\t\t\t\t\t\"explanation\"\n338 | \t\t\t\t\t]\n339 | \t\t\t\t}\n340 | \t\t\t},\n341 | \t\t\t{\n342 | \t\t\t\t\"name\": \"copilot_readFile\",\n343 | \t\t\t\t\"toolReferenceName\": \"readFile\",\n344 | \t\t\t\t\"displayName\": \"%copilot.tools.readFile.name%\",\n345 | \t\t\t\t\"modelDescription\": \"Read the contents of a file.\\n\\nYou must specify the line range you're interested in. Line numbers are 1-indexed. If the file contents returned are insufficient for your task, you may call this tool again to retrieve more content. Prefer reading larger ranges over doing many small reads.\",\n346 | \t\t\t\t\"tags\": [\n347 | \t\t\t\t\t\"vscode_codesearch\"\n348 | \t\t\t\t],\n349 | \t\t\t\t\"inputSchema\": {\n350 | \t\t\t\t\t\"type\": \"object\",\n351 | \t\t\t\t\t\"properties\": {\n352 | \t\t\t\t\t\t\"filePath\": {\n353 | \t\t\t\t\t\t\t\"description\": \"The absolute path of the file to read.\",\n354 | \t\t\t\t\t\t\t\"type\": \"string\"\n355 | \t\t\t\t\t\t},\n356 | \t\t\t\t\t\t\"startLine\": {\n357 | \t\t\t\t\t\t\t\"type\": \"number\",\n358 | \t\t\t\t\t\t\t\"description\": \"The line number to start reading from, 1-based.\"\n359 | \t\t\t\t\t\t},\n360 | \t\t\t\t\t\t\"endLine\": {\n361 | \t\t\t\t\t\t\t\"type\": \"number\",\n362 | \t\t\t\t\t\t\t\"description\": \"The inclusive line number to end reading at, 1-based.\"\n363 | \t\t\t\t\t\t}\n364 | \t\t\t\t\t},\n365 | \t\t\t\t\t\"required\": [\n366 | \t\t\t\t\t\t\"filePath\",\n367 | \t\t\t\t\t\t\"startLine\",\n368 | \t\t\t\t\t\t\"endLine\"\n369 | \t\t\t\t\t]\n370 | \t\t\t\t}\n371 | \t\t\t},\n372 | \t\t\t{\n373 | \t\t\t\t\"name\": \"copilot_listDirectory\",\n374 | \t\t\t\t\"toolReferenceName\": \"listDirectory\",\n375 | \t\t\t\t\"displayName\": \"%copilot.tools.listDirectory.name%\",\n376 | \t\t\t\t\"modelDescription\": \"List the contents of a directory. Result will have the name of the child. If the name ends in /, it's a folder, otherwise a file\",\n377 | \t\t\t\t\"tags\": [\n378 | \t\t\t\t\t\"vscode_codesearch\"\n379 | \t\t\t\t],\n380 | \t\t\t\t\"inputSchema\": {\n381 | \t\t\t\t\t\"type\": \"object\",\n382 | \t\t\t\t\t\"properties\": {\n383 | \t\t\t\t\t\t\"path\": {\n384 | \t\t\t\t\t\t\t\"type\": \"string\",\n385 | \t\t\t\t\t\t\t\"description\": \"The absolute path to the directory to list.\"\n386 | \t\t\t\t\t\t}\n387 | \t\t\t\t\t},\n388 | \t\t\t\t\t\"required\": [\n389 | \t\t\t\t\t\t\"path\"\n390 | \t\t\t\t\t]\n391 | \t\t\t\t}\n392 | \t\t\t},\n393 | \t\t\t{\n394 | \t\t\t\t\"name\": \"copilot_runInTerminal\",\n395 | \t\t\t\t\"toolReferenceName\": \"runInTerminal\",\n396 | \t\t\t\t\"displayName\": \"%copilot.tools.runInTerminal.name%\",\n397 | \t\t\t\t\"modelDescription\": \"This tool allows you to execute shell commands in a persistent terminal session, preserving environment variables, working directory, and other context across multiple commands.\\n\\nCommand Execution:\\n- Supports multi-line commands \\n\\nDirectory Management:\\n- Must use absolute paths to avoid navigation issues.\\n\\nProgram Execution:\\n- Supports Python, Node.js, and other executables.\\n- Install dependencies via pip, npm, etc.\\n\\nBackground Processes:\\n- For long-running tasks (e.g., servers), set isBackground=true.\\n- Returns a terminal ID for checking status and runtime later.\\n\\nOutput Management:\\n- Output is automatically truncated if longer than 60KB to prevent context overflow\\n- Use filters like 'head', 'tail', 'grep' to limit output size\\n- For pager commands, disable paging: use 'git --no-pager' or add '| cat'\\n\\nBest Practices:\\n- Be specific with commands to avoid excessive output\\n- Use targeted queries instead of broad scans\\n- Consider using 'wc -l' to count before listing many items\",\n398 | \t\t\t\t\"tags\": [],\n399 | \t\t\t\t\"inputSchema\": {\n400 | \t\t\t\t\t\"type\": \"object\",\n401 | \t\t\t\t\t\"properties\": {\n402 | \t\t\t\t\t\t\"command\": {\n403 | \t\t\t\t\t\t\t\"type\": \"string\",\n404 | \t\t\t\t\t\t\t\"description\": \"The command to run in the terminal.\"\n405 | \t\t\t\t\t\t},\n406 | \t\t\t\t\t\t\"explanation\": {\n407 | \t\t\t\t\t\t\t\"type\": \"string\",\n408 | \t\t\t\t\t\t\t\"description\": \"A one-sentence description of what the command does. This will be shown to the user before the command is run.\"\n409 | \t\t\t\t\t\t},\n410 | \t\t\t\t\t\t\"isBackground\": {\n411 | \t\t\t\t\t\t\t\"type\": \"boolean\",\n412 | \t\t\t\t\t\t\t\"description\": \"Whether the command starts a background process. If true, the command will run in the background and you will not see the output. If false, the tool call will block on the command finishing, and then you will get the output. Examples of background processes: building in watch mode, starting a server. You can check the output of a background process later on by using copilot_getTerminalOutput.\"\n413 | \t\t\t\t\t\t}\n414 | \t\t\t\t\t},\n415 | \t\t\t\t\t\"required\": [\n416 | \t\t\t\t\t\t\"command\",\n417 | \t\t\t\t\t\t\"explanation\",\n418 | \t\t\t\t\t\t\"isBackground\"\n419 | \t\t\t\t\t]\n420 | \t\t\t\t},\n421 | \t\t\t\t\"when\": \"!config.chat.agent.terminal.coreToolsEnabled\"\n422 | \t\t\t},\n423 | \t\t\t{\n424 | \t\t\t\t\"name\": \"copilot_getTerminalOutput\",\n425 | \t\t\t\t\"toolReferenceName\": \"getTerminalOutput\",\n426 | \t\t\t\t\"displayName\": \"%copilot.tools.getTerminalOutput.name%\",\n427 | \t\t\t\t\"modelDescription\": \"Get the output of a terminal command previously started with runInTerminal\",\n428 | \t\t\t\t\"tags\": [],\n429 | \t\t\t\t\"inputSchema\": {\n430 | \t\t\t\t\t\"type\": \"object\",\n431 | \t\t\t\t\t\"properties\": {\n432 | \t\t\t\t\t\t\"id\": {\n433 | \t\t\t\t\t\t\t\"type\": \"string\",\n434 | \t\t\t\t\t\t\t\"description\": \"The ID of the terminal command output to check.\"\n435 | \t\t\t\t\t\t}\n436 | \t\t\t\t\t},\n437 | \t\t\t\t\t\"required\": [\n438 | \t\t\t\t\t\t\"id\"\n439 | \t\t\t\t\t]\n440 | \t\t\t\t},\n441 | \t\t\t\t\"when\": \"!config.chat.agent.terminal.coreToolsEnabled\"\n442 | \t\t\t},\n443 | \t\t\t{\n444 | \t\t\t\t\"name\": \"copilot_getTaskOutput\",\n445 | \t\t\t\t\"toolReferenceName\": \"getTaskOutput\",\n446 | \t\t\t\t\"displayName\": \"%copilot.tools.getTaskOutput.name%\",\n447 | \t\t\t\t\"modelDescription\": \"Retrieves the output of a VS Code task.\\n- Use this tool when the user is trying to understand the current project state, debug issues, or analyze task-related errors, output, or status.\",\n448 | \t\t\t\t\"tags\": [],\n449 | \t\t\t\t\"inputSchema\": {\n450 | \t\t\t\t\t\"type\": \"object\",\n451 | \t\t\t\t\t\"properties\": {\n452 | \t\t\t\t\t\t\"workspaceFolder\": {\n453 | \t\t\t\t\t\t\t\"type\": \"string\",\n454 | \t\t\t\t\t\t\t\"description\": \"The workspace folder path containing the task\"\n455 | \t\t\t\t\t\t},\n456 | \t\t\t\t\t\t\"id\": {\n457 | \t\t\t\t\t\t\t\"type\": \"string\",\n458 | \t\t\t\t\t\t\t\"description\": \"The task ID to run.\"\n459 | \t\t\t\t\t\t},\n460 | \t\t\t\t\t\t\"maxCharsToRetrieve\": {\n461 | \t\t\t\t\t\t\t\"type\": \"number\",\n462 | \t\t\t\t\t\t\t\"description\": \"The maximum number of characters to retrieve from the terminal output.\"\n463 | \t\t\t\t\t\t}\n464 | \t\t\t\t\t},\n465 | \t\t\t\t\t\"required\": [\n466 | \t\t\t\t\t\t\"id\",\n467 | \t\t\t\t\t\t\"workspaceFolder\"\n468 | \t\t\t\t\t]\n469 | \t\t\t\t}\n470 | \t\t\t},\n471 | \t\t\t{\n472 | \t\t\t\t\"name\": \"copilot_getErrors\",\n473 | \t\t\t\t\"displayName\": \"%copilot.tools.getErrors.name%\",\n474 | \t\t\t\t\"toolReferenceName\": \"problems\",\n475 | \t\t\t\t\"canBeReferencedInPrompt\": true,\n476 | \t\t\t\t\"icon\": \"$(error)\",\n477 | \t\t\t\t\"userDescription\": \"%copilot.tools.errors.description%\",\n478 | \t\t\t\t\"modelDescription\": \"Get any compile or lint errors in a code file. If the user mentions errors or problems in a file, they may be referring to these. Use the tool to see the same errors that the user is seeing. Also use this tool after editing a file to validate the change.\",\n479 | \t\t\t\t\"tags\": [],\n480 | \t\t\t\t\"inputSchema\": {\n481 | \t\t\t\t\t\"type\": \"object\",\n482 | \t\t\t\t\t\"properties\": {\n483 | \t\t\t\t\t\t\"filePaths\": {\n484 | \t\t\t\t\t\t\t\"description\": \"The absolute paths to the files to check for errors.\",\n485 | \t\t\t\t\t\t\t\"type\": \"array\",\n486 | \t\t\t\t\t\t\t\"items\": {\n487 | \t\t\t\t\t\t\t\t\"type\": \"string\"\n488 | \t\t\t\t\t\t\t}\n489 | \t\t\t\t\t\t}\n490 | \t\t\t\t\t},\n491 | \t\t\t\t\t\"required\": [\n492 | \t\t\t\t\t\t\"filePaths\"\n493 | \t\t\t\t\t]\n494 | \t\t\t\t}\n495 | \t\t\t},\n496 | \t\t\t{\n497 | \t\t\t\t\"name\": \"copilot_readProjectStructure\",\n498 | \t\t\t\t\"displayName\": \"%copilot.tools.readProjectStructure.name%\",\n499 | \t\t\t\t\"modelDescription\": \"Get a file tree representation of the workspace.\",\n500 | \t\t\t\t\"tags\": []\n</content>\n<list_code_definition_names># package.json\n1--3853 | {\n14--17 | \t\"repository\": {\n24--28 | \t\"engines\": {\n29--34 | \t\"categories\": [\n35--57 | \t\"keywords\": [\n58--79 | \t\"badges\": [\n59--63 | \t\t{\n64--68 | \t\t{\n69--73 | \t\t{\n74--78 | \t\t{\n80--86 | \t\"activationEvents\": [\n89--135 | \t\"enabledApiProposals\": [\n136--3696 | \t\"contributes\": {\n137--1221 | \t\t\"languageModelTools\": [\n138--162 | \t\t\t{\n146--149 | \t\t\t\t\"tags\": [\n150--161 | \t\t\t\t\"inputSchema\": {\n152--157 | \t\t\t\t\t\"properties\": {\n153--156 | \t\t\t\t\t\t\"query\": {\n163--185 | \t\t\t{\n173--184 | \t\t\t\t\"inputSchema\": {\n175--180 | \t\t\t\t\t\"properties\": {\n176--179 | \t\t\t\t\t\t\"symbolName\": {\n186--216 | \t\t\t{\n197--215 | \t\t\t\t\"inputSchema\": {\n199--211 | \t\t\t\t\t\"properties\": {\n200--203 | \t\t\t\t\t\t\"symbolName\": {\n204--210 | \t\t\t\t\t\t\"filePaths\": {\n217--238 | \t\t\t{\n224--235 | \t\t\t\t\"inputSchema\": {\n226--231 | \t\t\t\t\t\"properties\": {\n227--230 | \t\t\t\t\t\t\"query\": {\n239--257 | \t\t\t{\n245--256 | \t\t\t\t\"inputSchema\": {\n247--252 | \t\t\t\t\t\"properties\": {\n248--251 | \t\t\t\t\t\t\"thoughts\": {\n258--282 | \t\t\t{\n266--281 | \t\t\t\t\"inputSchema\": {\n268--277 | \t\t\t\t\t\"properties\": {\n269--272 | \t\t\t\t\t\t\"query\": {\n273--276 | \t\t\t\t\t\t\"maxResults\": {\n283--316 | \t\t\t{\n291--315 | \t\t\t\t\"inputSchema\": {\n293--310 | \t\t\t\t\t\"properties\": {\n294--297 | \t\t\t\t\t\t\"query\": {\n298--301 | \t\t\t\t\t\t\"isRegexp\": {\n302--305 | \t\t\t\t\t\t\"includePattern\": {\n306--309 | \t\t\t\t\t\t\"maxResults\": {\n311--314 | \t\t\t\t\t\"required\": [\n317--340 | \t\t\t{\n323--339 | \t\t\t\t\"inputSchema\": {\n325--334 | \t\t\t\t\t\"properties\": {\n326--329 | \t\t\t\t\t\t\"input\": {\n330--333 | \t\t\t\t\t\t\"explanation\": {\n335--338 | \t\t\t\t\t\"required\": [\n341--371 | \t\t\t{\n349--370 | \t\t\t\t\"inputSchema\": {\n351--364 | \t\t\t\t\t\"properties\": {\n352--355 | \t\t\t\t\t\t\"filePath\": {\n356--359 | \t\t\t\t\t\t\"startLine\": {\n360--363 | \t\t\t\t\t\t\"endLine\": {\n365--369 | \t\t\t\t\t\"required\": [\n372--392 | \t\t\t{\n380--391 | \t\t\t\t\"inputSchema\": {\n382--387 | \t\t\t\t\t\"properties\": {\n383--386 | \t\t\t\t\t\t\"path\": {\n393--422 | \t\t\t{\n399--420 | \t\t\t\t\"inputSchema\": {\n401--414 | \t\t\t\t\t\"properties\": {\n402--405 | \t\t\t\t\t\t\"command\": {\n406--409 | \t\t\t\t\t\t\"explanation\": {\n410--413 | \t\t\t\t\t\t\"isBackground\": {\n415--419 | \t\t\t\t\t\"required\": [\n423--442 | \t\t\t{\n429--440 | \t\t\t\t\"inputSchema\": {\n431--436 | \t\t\t\t\t\"properties\": {\n432--435 | \t\t\t\t\t\t\"id\": {\n443--470 | \t\t\t{\n449--469 | \t\t\t\t\"inputSchema\": {\n451--464 | \t\t\t\t\t\"properties\": {\n452--455 | \t\t\t\t\t\t\"workspaceFolder\": {\n456--459 | \t\t\t\t\t\t\"id\": {\n460--463 | \t\t\t\t\t\t\"maxCharsToRetrieve\": {\n465--468 | \t\t\t\t\t\"required\": [\n471--495 | \t\t\t{\n480--494 | \t\t\t\t\"inputSchema\": {\n482--490 | \t\t\t\t\t\"properties\": {\n483--489 | \t\t\t\t\t\t\"filePaths\": {\n496--501 | \t\t\t{\n502--534 | \t\t\t{\n513--533 | \t\t\t\t\"inputSchema\": {\n515--532 | \t\t\t\t\t\"properties\": {\n516--519 | \t\t\t\t\t\t\"repositoryPath\": {\n520--531 | \t\t\t\t\t\t\"sourceControlState\": {\n522--529 | \t\t\t\t\t\t\t\"items\": {\n524--528 | \t\t\t\t\t\t\t\t\"enum\": [\n535--551 | \t\t\t{\n543--549 | \t\t\t\t\"tags\": [\n552--575 | \t\t\t{\n559--573 | \t\t\t\t\"inputSchema\": {\n561--569 | \t\t\t\t\t\"properties\": {\n562--568 | \t\t\t\t\t\t\"facts\": {\n576--601 | \t\t\t{\n582--593 | \t\t\t\t\"inputSchema\": {\n584--592 | \t\t\t\t\t\"properties\": {\n585--591 | \t\t\t\t\t\t\"files\": {\n594--600 | \t\t\t\t\"tags\": [\n602--610 | \t\t\t{\n611--619 | \t\t\t{\n620--644 | \t\t\t{\n628--639 | \t\t\t\t\"inputSchema\": {\n630--635 | \t\t\t\t\t\"properties\": {\n631--634 | \t\t\t\t\t\t\"query\": {\n640--643 | \t\t\t\t\"tags\": [\n645--718 | \t\t\t{\n654--714 | \t\t\t\t\"inputSchema\": {\n656--709 | \t\t\t\t\t\"properties\": {\n657--660 | \t\t\t\t\t\t\"workspaceFolder\": {\n661--708 | \t\t\t\t\t\t\"task\": {\n664--702 | \t\t\t\t\t\t\t\"properties\": {\n665--668 | \t\t\t\t\t\t\t\t\"label\": {\n669--675 | \t\t\t\t\t\t\t\t\"type\": {\n676--679 | \t\t\t\t\t\t\t\t\"command\": {\n680--686 | \t\t\t\t\t\t\t\t\"args\": {\n687--690 | \t\t\t\t\t\t\t\t\"isBackground\": {\n691--697 | \t\t\t\t\t\t\t\t\"problemMatcher\": {\n698--701 | \t\t\t\t\t\t\t\t\"group\": {\n703--707 | \t\t\t\t\t\t\t\"required\": [\n710--713 | \t\t\t\t\t\"required\": [\n719--742 | \t\t\t{\n725--740 | \t\t\t\t\"inputSchema\": {\n727--736 | \t\t\t\t\t\"properties\": {\n728--731 | \t\t\t\t\t\t\"projectType\": {\n732--735 | \t\t\t\t\t\t\"language\": {\n743--767 | \t\t\t{\n749--765 | \t\t\t\t\"inputSchema\": {\n751--760 | \t\t\t\t\t\"properties\": {\n752--755 | \t\t\t\t\t\t\"id\": {\n756--759 | \t\t\t\t\t\t\"name\": {\n761--764 | \t\t\t\t\t\"required\": [\n768--799 | \t\t\t{\n774--797 | \t\t\t\t\"inputSchema\": {\n776--792 | \t\t\t\t\t\"properties\": {\n777--780 | \t\t\t\t\t\t\"commandId\": {\n781--784 | \t\t\t\t\t\t\"name\": {\n785--791 | \t\t\t\t\t\t\"args\": {\n793--796 | \t\t\t\t\t\"required\": [\n800--820 | \t\t\t{\n807--818 | \t\t\t\t\"inputSchema\": {\n809--814 | \t\t\t\t\t\"properties\": {\n810--813 | \t\t\t\t\t\t\"query\": {\n821--843 | \t\t\t{\n826--842 | \t\t\t\t\"inputSchema\": {\n828--837 | \t\t\t\t\t\"properties\": {\n829--832 | \t\t\t\t\t\t\"workspaceFolder\": {\n833--836 | \t\t\t\t\t\t\"id\": {\n838--841 | \t\t\t\t\t\"required\": [\n844--872 | \t\t\t{\n850--871 | \t\t\t\t\"inputSchema\": {\n852--865 | \t\t\t\t\t\"properties\": {\n853--856 | \t\t\t\t\t\t\"explanation\": {\n857--860 | \t\t\t\t\t\t\"filePath\": {\n861--864 | \t\t\t\t\t\t\"code\": {\n866--870 | \t\t\t\t\t\"required\": [\n873--896 | \t\t\t{\n879--895 | \t\t\t\t\"inputSchema\": {\n881--890 | \t\t\t\t\t\"properties\": {\n882--885 | \t\t\t\t\t\t\"filePath\": {\n886--889 | \t\t\t\t\t\t\"content\": {\n891--894 | \t\t\t\t\t\"required\": [\n897--915 | \t\t\t{\n903--914 | \t\t\t\t\"inputSchema\": {\n905--910 | \t\t\t\t\t\"properties\": {\n906--909 | \t\t\t\t\t\t\"dirPath\": {\n916--936 | \t\t\t{\n924--935 | \t\t\t\t\"inputSchema\": {\n926--931 | \t\t\t\t\t\"properties\": {\n927--930 | \t\t\t\t\t\t\"url\": {\n937--965 | \t\t\t{\n943--964 | \t\t\t\t\"inputSchema\": {\n945--958 | \t\t\t\t\t\"properties\": {\n946--949 | \t\t\t\t\t\t\"filePath\": {\n950--953 | \t\t\t\t\t\t\"oldString\": {\n954--957 | \t\t\t\t\t\t\"newString\": {\n959--963 | \t\t\t\t\t\"required\": [\n966--1022 | \t\t\t{\n972--1021 | \t\t\t\t\"inputSchema\": {\n974--1015 | \t\t\t\t\t\"properties\": {\n975--978 | \t\t\t\t\t\t\"filePath\": {\n979--982 | \t\t\t\t\t\t\"explanation\": {\n983--986 | \t\t\t\t\t\t\"cellId\": {\n987--1001 | \t\t\t\t\t\t\"newCode\": {\n988--1000 | \t\t\t\t\t\t\t\"anyOf\": [\n989--992 | \t\t\t\t\t\t\t\t{\n993--999 | \t\t\t\t\t\t\t\t{\n995--998 | \t\t\t\t\t\t\t\t\t\"items\": {\n1002--1005 | \t\t\t\t\t\t\"language\": {\n1006--1014 | \t\t\t\t\t\t\"editType\": {\n1008--1012 | \t\t\t\t\t\t\t\"enum\": [\n1016--1020 | \t\t\t\t\t\"required\": [\n1023--1058 | \t\t\t{\n1033--1057 | \t\t\t\t\"inputSchema\": {\n1035--1052 | \t\t\t\t\t\"properties\": {\n1036--1039 | \t\t\t\t\t\t\"filePath\": {\n1040--1043 | \t\t\t\t\t\t\"reason\": {\n1044--1047 | \t\t\t\t\t\t\"cellId\": {\n1048--1051 | \t\t\t\t\t\t\"continueOnError\": {\n1053--1056 | \t\t\t\t\t\"required\": [\n1059--1077 | \t\t\t{\n1065--1076 | \t\t\t\t\"inputSchema\": {\n1067--1072 | \t\t\t\t\t\"properties\": {\n1068--1071 | \t\t\t\t\t\t\"filePath\": {\n1078--1105 | \t\t\t{\n1088--1104 | \t\t\t\t\"inputSchema\": {\n1090--1099 | \t\t\t\t\t\"properties\": {\n1091--1094 | \t\t\t\t\t\t\"filePath\": {\n1095--1098 | \t\t\t\t\t\t\"cellId\": {\n1100--1103 | \t\t\t\t\t\"required\": [\n1106--1136 | \t\t\t{\n1116--1135 | \t\t\t\t\"inputSchema\": {\n1118--1130 | \t\t\t\t\t\"properties\": {\n1119--1125 | \t\t\t\t\t\t\"urls\": {\n1126--1129 | \t\t\t\t\t\t\"query\": {\n1131--1134 | \t\t\t\t\t\"required\": [\n1137--1160 | \t\t\t{\n1146--1159 | \t\t\t\t\"inputSchema\": {\n1148--1155 | \t\t\t\t\t\"properties\": {\n1149--1154 | \t\t\t\t\t\t\"filePaths\": {\n1161--1185 | \t\t\t{\n1170--1184 | \t\t\t\t\"inputSchema\": {\n1172--1180 | \t\t\t\t\t\"properties\": {\n1173--1179 | \t\t\t\t\t\t\"filePaths\": {\n1186--1194 | \t\t\t{\n1195--1220 | \t\t\t{\n1203--1219 | \t\t\t\t\"inputSchema\": {\n1205--1214 | \t\t\t\t\t\"properties\": {\n1206--1209 | \t\t\t\t\t\t\"repo\": {\n1210--1213 | \t\t\t\t\t\t\"query\": {\n1215--1218 | \t\t\t\t\t\"required\": [\n1222--1288 | \t\t\"languageModelToolSets\": [\n1223--1236 | \t\t\t{\n1227--1235 | \t\t\t\t\"tools\": [\n1237--1246 | \t\t\t{\n1241--1245 | \t\t\t\t\"tools\": [\n1247--1255 | \t\t\t{\n1251--1254 | \t\t\t\t\"tools\": [\n1256--1267 | \t\t\t{\n1260--1266 | \t\t\t\t\"tools\": [\n1268--1278 | \t\t\t{\n1272--1277 | \t\t\t\t\"tools\": [\n1279--1287 | \t\t\t{\n1282--1286 | \t\t\t\t\"tools\": [\n1289--1761 | \t\t\"chatParticipants\": [\n1290--1325 | \t\t\t{\n1302--1324 | \t\t\t\t\"disambiguation\": [\n1303--1309 | \t\t\t\t\t{\n1310--1316 | \t\t\t\t\t{\n1317--1323 | \t\t\t\t\t{\n1326--1339 | \t\t\t{\n1340--1395 | \t\t\t{\n1347--1350 | \t\t\t\t\"locations\": [\n1351--1394 | \t\t\t\t\"commands\": [\n1352--1365 | \t\t\t\t\t{\n1356--1364 | \t\t\t\t\t\t\"disambiguation\": [\n1357--1363 | \t\t\t\t\t\t\t{\n1366--1379 | \t\t\t\t\t{\n1370--1378 | \t\t\t\t\t\t\"disambiguation\": [\n1371--1377 | \t\t\t\t\t\t\t{\n1380--1393 | \t\t\t\t\t{\n1384--1392 | \t\t\t\t\t\t\"disambiguation\": [\n1385--1391 | \t\t\t\t\t\t\t{\n1396--1409 | \t\t\t{\n1410--1434 | \t\t\t{\n1424--1433 | \t\t\t\t\"commands\": [\n1428--1432 | \t\t\t\t\t{\n1435--1539 | \t\t\t{\n1445--1453 | \t\t\t\t\"disambiguation\": [\n1446--1452 | \t\t\t\t\t{\n1454--1538 | \t\t\t\t\"commands\": [\n1455--1467 | \t\t\t\t\t{\n1458--1466 | \t\t\t\t\t\t\"disambiguation\": [\n1459--1465 | \t\t\t\t\t\t\t{\n1468--1480 | \t\t\t\t\t{\n1471--1479 | \t\t\t\t\t\t\"disambiguation\": [\n1472--1478 | \t\t\t\t\t\t\t{\n1481--1493 | \t\t\t\t\t{\n1484--1492 | \t\t\t\t\t\t\"disambiguation\": [\n1485--1491 | \t\t\t\t\t\t\t{\n1494--1506 | \t\t\t\t\t{\n1497--1505 | \t\t\t\t\t\t\"disambiguation\": [\n1498--1504 | \t\t\t\t\t\t\t{\n1507--1519 | \t\t\t\t\t{\n1510--1518 | \t\t\t\t\t\t\"disambiguation\": [\n1511--1517 | \t\t\t\t\t\t\t{\n1520--1524 | \t\t\t\t\t{\n1525--1537 | \t\t\t\t\t{\n1528--1536 | \t\t\t\t\t\t\"disambiguation\": [\n1529--1535 | \t\t\t\t\t\t\t{\n1540--1560 | \t\t\t{\n1550--1559 | \t\t\t\t\"commands\": [\n1551--1554 | \t\t\t\t\t{\n1555--1558 | \t\t\t\t\t{\n1561--1674 | \t\t\t{\n1570--1592 | \t\t\t\t\"disambiguation\": [\n1571--1577 | \t\t\t\t\t{\n1578--1584 | \t\t\t\t\t{\n1585--1591 | \t\t\t\t\t{\n1593--1673 | \t\t\t\t\"commands\": [\n1594--1597 | \t\t\t\t\t{\n1598--1602 | \t\t\t\t\t{\n1603--1615 | \t\t\t\t\t{\n1606--1614 | \t\t\t\t\t\t\"disambiguation\": [\n1607--1613 | \t\t\t\t\t\t\t{\n1616--1620 | \t\t\t\t\t{\n1621--1637 | \t\t\t\t\t{\n1626--1636 | \t\t\t\t\t\t\"disambiguation\": [\n1627--1635 | \t\t\t\t\t\t\t{\n1630--1634 | \t\t\t\t\t\t\t\t\"examples\": [\n1638--1651 | \t\t\t\t\t{\n1642--1650 | \t\t\t\t\t\t\"disambiguation\": [\n1643--1649 | \t\t\t\t\t\t\t{\n1652--1657 | \t\t\t\t\t{\n1658--1672 | \t\t\t\t\t{\n1663--1671 | \t\t\t\t\t\t\"disambiguation\": [\n1664--1670 | \t\t\t\t\t\t\t{\n1675--1716 | \t\t\t{\n1684--1702 | \t\t\t\t\"disambiguation\": [\n1685--1694 | \t\t\t\t\t{\n1688--1693 | \t\t\t\t\t\t\"examples\": [\n1695--1701 | \t\t\t\t\t{\n1703--1715 | \t\t\t\t\"commands\": [\n1704--1708 | \t\t\t\t\t{\n1709--1714 | \t\t\t\t\t{\n1717--1734 | \t\t\t{\n1727--1733 | \t\t\t\t\"commands\": [\n1728--1732 | \t\t\t\t\t{\n1735--1760 | \t\t\t{\n1744--1759 | \t\t\t\t\"commands\": [\n1745--1758 | \t\t\t\t\t{\n1749--1757 | \t\t\t\t\t\t\"disambiguation\": [\n1750--1756 | \t\t\t\t\t\t\t{\n1762--1769 | \t\t\"languageModels\": [\n1770--1777 | \t\t\"interactiveSession\": [\n1771--1776 | \t\t\t{\n1778--1784 | \t\t\"viewsWelcome\": [\n1779--1783 | \t\t\t{\n1785--1828 | \t\t\"chatViewsWelcome\": [\n1786--1791 | \t\t\t{\n1792--1797 | \t\t\t{\n1798--1803 | \t\t\t{\n1804--1809 | \t\t\t{\n1810--1815 | \t\t\t{\n1816--1821 | \t\t\t{\n1822--1827 | \t\t\t{\n1829--2194 | \t\t\"commands\": [\n1830--1835 | \t\t\t{\n1836--1841 | \t\t\t{\n1842--1847 | \t\t\t{\n1848--1854 | \t\t\t{\n1855--1861 | \t\t\t{\n1862--1867 | \t\t\t{\n1868--1873 | \t\t\t{\n1874--1879 | \t\t\t{\n1880--1886 | \t\t\t{\n1887--1893 | \t\t\t{\n1894--1900 | \t\t\t{\n1901--1906 | \t\t\t{\n1907--1912 | \t\t\t{\n1913--1918 | \t\t\t{\n1919--1924 | \t\t\t{\n1925--1930 | \t\t\t{\n1931--1937 | \t\t\t{\n1938--1943 | \t\t\t{\n1944--1950 | \t\t\t{\n1951--1957 | \t\t\t{\n1958--1963 | \t\t\t{\n1964--1969 | \t\t\t{\n1970--1975 | \t\t\t{\n1976--1982 | \t\t\t{\n1983--1987 | \t\t\t{\n1988--1992 | \t\t\t{\n1993--1998 | \t\t\t{\n1999--2003 | \t\t\t{\n2004--2008 | \t\t\t{\n2009--2013 | \t\t\t{\n2014--2018 | \t\t\t{\n2019--2025 | \t\t\t{\n2026--2030 | \t\t\t{\n2031--2036 | \t\t\t{\n2037--2041 | \t\t\t{\n2042--2047 | \t\t\t{\n2048--2052 | \t\t\t{\n2053--2058 | \t\t\t{\n2059--2067 | \t\t\t{\n2068--2072 | \t\t\t{\n2073--2078 | \t\t\t{\n2079--2084 | \t\t\t{\n2085--2090 | \t\t\t{\n2091--2095 | \t\t\t{\n2096--2100 | \t\t\t{\n2101--2104 | \t\t\t{\n2105--2109 | \t\t\t{\n2110--2115 | \t\t\t{\n2116--2121 | \t\t\t{\n2122--2127 | \t\t\t{\n2128--2134 | \t\t\t{\n2135--2138 | \t\t\t{\n2139--2142 | \t\t\t{\n2143--2146 | \t\t\t{\n2147--2150 | \t\t\t{\n2151--2155 | \t\t\t{\n2156--2160 | \t\t\t{\n2161--2165 | \t\t\t{\n2166--2169 | \t\t\t{\n2170--2173 | \t\t\t{\n2174--2177 | \t\t\t{\n2178--2181 | \t\t\t{\n2182--2187 | \t\t\t{\n2188--2193 | \t\t\t{\n2195--2906 | \t\t\"configuration\": [\n2196--2310 | \t\t\t{\n2199--2309 | \t\t\t\t\"properties\": {\n2200--2204 | \t\t\t\t\t\"github.copilot.chat.codeGeneration.useInstructionFiles\": {\n2205--2209 | \t\t\t\t\t\"github.copilot.editor.enableCodeActions\": {\n2210--2214 | \t\t\t\t\t\"github.copilot.renameSuggestions.triggerAutomatically\": {\n2215--2253 | \t\t\t\t\t\"github.copilot.chat.localeOverride\": {\n2217--2233 | \t\t\t\t\t\t\"enum\": [\n2234--2250 | \t\t\t\t\t\t\"enumDescriptions\": [\n2254--2268 | \t\t\t\t\t\"github.copilot.chat.terminalChatLocation\": {\n2258--2262 | \t\t\t\t\t\t\"markdownEnumDescriptions\": [\n2263--2267 | \t\t\t\t\t\t\"enum\": [\n2269--2273 | \t\t\t\t\t\"github.copilot.chat.scopeSelection\": {\n2274--2278 | \t\t\t\t\t\"github.copilot.chat.useProjectTemplates\": {\n2279--2283 | \t\t\t\t\t\"github.copilot.chat.agent.runTasks\": {\n2284--2293 | \t\t\t\t\t\"github.copilot.nextEditSuggestions.enabled\": {\n2287--2290 | \t\t\t\t\t\t\"tags\": [\n2294--2303 | \t\t\t\t\t\"github.copilot.nextEditSuggestions.fixes\": {\n2297--2300 | \t\t\t\t\t\t\"tags\": [\n2304--2308 | \t\t\t\t\t\"github.copilot.chat.agent.autoFix\": {\n2311--2435 | \t\t\t{\n2314--2434 | \t\t\t\t\"properties\": {\n2315--2322 | \t\t\t\t\t\"github.copilot.chat.startDebugging.enabled\": {\n2323--2330 | \t\t\t\t\t\"github.copilot.chat.reviewSelection.enabled\": {\n2331--2401 | \t\t\t\t\t\"github.copilot.chat.reviewSelection.instructions\": {\n2333--2385 | \t\t\t\t\t\t\"items\": {\n2334--2384 | \t\t\t\t\t\t\t\"oneOf\": [\n2335--2357 | \t\t\t\t\t\t\t\t{\n2338--2348 | \t\t\t\t\t\t\t\t\t\"properties\": {\n2339--2344 | \t\t\t\t\t\t\t\t\t\t\"file\": {\n2349--2353 | \t\t\t\t\t\t\t\t\t\"examples\": [\n2358--2383 | \t\t\t\t\t\t\t\t{\n2361--2371 | \t\t\t\t\t\t\t\t\t\"properties\": {\n2362--2367 | \t\t\t\t\t\t\t\t\t\t\"text\": {\n2375--2382 | \t\t\t\t\t\t\t\t\t\"examples\": [\n2388--2397 | \t\t\t\t\t\t\"examples\": [\n2389--2396 | \t\t\t\t\t\t\t[\n2402--2409 | \t\t\t\t\t\"github.copilot.chat.copilotDebugCommand.enabled\": {\n2410--2417 | \t\t\t\t\t\"github.copilot.chat.codesearch.enabled\": {\n2418--2425 | \t\t\t\t\t\"github.copilot.chat.edits.codesearch.enabled\": {\n2426--2433 | \t\t\t\t\t\"github.copilot.chat.byok.ollamaEndpoint\": {\n2436--2905 | \t\t\t{\n2439--2904 | \t\t\t\t\"properties\": {\n2440--2447 | \t\t\t\t\t\"github.copilot.chat.agent.thinkingTool\": {\n2448--2455 | \t\t\t\t\t\"github.copilot.chat.edits.suggestRelatedFilesFromGitHistory\": {\n2456--2463 | \t\t\t\t\t\"github.copilot.chat.edits.suggestRelatedFilesForTests\": {\n2464--2535 | \t\t\t\t\t\"github.copilot.chat.codeGeneration.instructions\": {\n2467--2519 | \t\t\t\t\t\t\"items\": {\n2468--2518 | \t\t\t\t\t\t\t\"oneOf\": [\n2469--2491 | \t\t\t\t\t\t\t\t{\n2472--2482 | \t\t\t\t\t\t\t\t\t\"properties\": {\n2473--2478 | \t\t\t\t\t\t\t\t\t\t\"file\": {\n2483--2487 | \t\t\t\t\t\t\t\t\t\"examples\": [\n2492--2517 | \t\t\t\t\t\t\t\t{\n2495--2505 | \t\t\t\t\t\t\t\t\t\"properties\": {\n2496--2501 | \t\t\t\t\t\t\t\t\t\t\"text\": {\n2509--2516 | \t\t\t\t\t\t\t\t\t\"examples\": [\n2522--2531 | \t\t\t\t\t\t\"examples\": [\n2523--2530 | \t\t\t\t\t\t\t[\n2536--2604 | \t\t\t\t\t\"github.copilot.chat.testGeneration.instructions\": {\n2539--2588 | \t\t\t\t\t\t\"items\": {\n2540--2587 | \t\t\t\t\t\t\t\"oneOf\": [\n2541--2563 | \t\t\t\t\t\t\t\t{\n2544--2554 | \t\t\t\t\t\t\t\t\t\"properties\": {\n2545--2550 | \t\t\t\t\t\t\t\t\t\t\"file\": {\n2555--2559 | \t\t\t\t\t\t\t\t\t\"examples\": [\n2564--2586 | \t\t\t\t\t\t\t\t{\n2567--2577 | \t\t\t\t\t\t\t\t\t\"properties\": {\n2568--2573 | \t\t\t\t\t\t\t\t\t\t\"text\": {\n2581--2585 | \t\t\t\t\t\t\t\t\t\"examples\": [\n2591--2600 | \t\t\t\t\t\t\"examples\": [\n2592--2599 | \t\t\t\t\t\t\t[\n2605--2666 | \t\t\t\t\t\"github.copilot.chat.commitMessageGeneration.instructions\": {\n2607--2650 | \t\t\t\t\t\t\"items\": {\n2608--2649 | \t\t\t\t\t\t\t\"oneOf\": [\n2609--2628 | \t\t\t\t\t\t\t\t{\n2612--2619 | \t\t\t\t\t\t\t\t\t\"properties\": {\n2613--2618 | \t\t\t\t\t\t\t\t\t\t\"file\": {\n2620--2624 | \t\t\t\t\t\t\t\t\t\"examples\": [\n2629--2648 | \t\t\t\t\t\t\t\t{\n2632--2639 | \t\t\t\t\t\t\t\t\t\"properties\": {\n2633--2638 | \t\t\t\t\t\t\t\t\t\t\"text\": {\n2643--2647 | \t\t\t\t\t\t\t\t\t\"examples\": [\n2653--2662 | \t\t\t\t\t\t\"examples\": [\n2654--2661 | \t\t\t\t\t\t\t[\n2667--2728 | \t\t\t\t\t\"github.copilot.chat.pullRequestDescriptionGeneration.instructions\": {\n2669--2712 | \t\t\t\t\t\t\"items\": {\n2670--2711 | \t\t\t\t\t\t\t\"oneOf\": [\n2671--2690 | \t\t\t\t\t\t\t\t{\n2674--2681 | \t\t\t\t\t\t\t\t\t\"properties\": {\n2675--2680 | \t\t\t\t\t\t\t\t\t\t\"file\": {\n2682--2686 | \t\t\t\t\t\t\t\t\t\"examples\": [\n2691--2710 | \t\t\t\t\t\t\t\t{\n2694--2701 | \t\t\t\t\t\t\t\t\t\"properties\": {\n2695--2700 | \t\t\t\t\t\t\t\t\t\t\"text\": {\n2705--2709 | \t\t\t\t\t\t\t\t\t\"examples\": [\n2715--2724 | \t\t\t\t\t\t\"examples\": [\n2716--2723 | \t\t\t\t\t\t\t[\n2729--2736 | \t\t\t\t\t\"github.copilot.chat.generateTests.codeLens\": {\n2737--2745 | \t\t\t\t\t\"github.copilot.chat.edits.temporalContext.enabled\": {\n2741--2744 | \t\t\t\t\t\t\"tags\": [\n2746--2754 | \t\t\t\t\t\"github.copilot.chat.editor.temporalContext.enabled\": {\n2750--2753 | \t\t\t\t\t\t\"tags\": [\n2755--2762 | \t\t\t\t\t\"github.copilot.chat.setupTests.enabled\": {\n2763--2772 | \t\t\t\t\t\"github.copilot.chat.languageContext.typescript.enabled\": {\n2767--2770 | \t\t\t\t\t\t\"tags\": [\n2773--2782 | \t\t\t\t\t\"github.copilot.chat.languageContext.typescript.cacheTimeout\": {\n2777--2780 | \t\t\t\t\t\t\"tags\": [\n2783--2792 | \t\t\t\t\t\"github.copilot.chat.languageContext.fix.typescript.enabled\": {\n2787--2790 | \t\t\t\t\t\t\"tags\": [\n2793--2802 | \t\t\t\t\t\"github.copilot.chat.languageContext.inline.typescript.enabled\": {\n2797--2800 | \t\t\t\t\t\t\"tags\": [\n2803--2810 | \t\t\t\t\t\"github.copilot.chat.newWorkspaceCreation.enabled\": {\n2811--2818 | \t\t\t\t\t\"github.copilot.chat.agent.currentEditorContext.enabled\": {\n2819--2826 | \t\t\t\t\t\"github.copilot.chat.customInstructionsInSystemMessage\": {\n2827--2846 | \t\t\t\t\t\"github.copilot.chat.agent.terminal.allowList\": {\n2830--2841 | \t\t\t\t\t\t\"additionalProperties\": {\n2832--2835 | \t\t\t\t\t\t\t\"enum\": [\n2836--2839 | \t\t\t\t\t\t\t\"enumDescriptions\": [\n2847--2877 | \t\t\t\t\t\"github.copilot.chat.agent.terminal.denyList\": {\n2849--2860 | \t\t\t\t\t\t\"default\": {\n2861--2872 | \t\t\t\t\t\t\"additionalProperties\": {\n2863--2866 | \t\t\t\t\t\t\t\"enum\": [\n2867--2870 | \t\t\t\t\t\t\t\"enumDescriptions\": [\n2878--2886 | \t\t\t\t\t\"github.copilot.chat.edits.newNotebook.enabled\": {\n2881--2884 | \t\t\t\t\t\t\"tags\": [\n2887--2894 | \t\t\t\t\t\"github.copilot.chat.notebook.followCellExecution.enabled\": {\n2895--2903 | \t\t\t\t\t\"github.copilot.chat.summarizeAgentConversationHistory.enabled\": {\n2898--2901 | \t\t\t\t\t\t\"tags\": [\n2907--2925 | \t\t\"submenus\": [\n2908--2911 | \t\t\t{\n2912--2915 | \t\t\t{\n2916--2919 | \t\t\t{\n2920--2924 | \t\t\t{\n2926--3379 | \t\t\"menus\": {\n2927--2932 | \t\t\t\"chat/modelPicker\": [\n2928--2931 | \t\t\t\t{\n2933--2948 | \t\t\t\"editor/title\": [\n2934--2937 | \t\t\t\t{\n2938--2942 | \t\t\t\t{\n2943--2947 | \t\t\t\t{\n2949--2975 | \t\t\t\"editor/context/chat\": [\n2950--2954 | \t\t\t\t{\n2955--2959 | \t\t\t\t{\n2960--2964 | \t\t\t\t{\n2965--2969 | \t\t\t\t{\n2970--2974 | \t\t\t\t{\n2976--2985 | \t\t\t\"terminal/context/chat\": [\n2977--2980 | \t\t\t\t{\n2981--2984 | \t\t\t\t{\n2986--2992 | \t\t\t\"testing/item/result\": [\n2987--2991 | \t\t\t\t{\n2993--2999 | \t\t\t\"testing/item/context\": [\n2994--2998 | \t\t\t\t{\n3000--3153 | \t\t\t\"commandPalette\": [\n3001--3004 | \t\t\t\t{\n3005--3008 | \t\t\t\t{\n3009--3012 | \t\t\t\t{\n3013--3016 | \t\t\t\t{\n3017--3020 | \t\t\t\t{\n3021--3024 | \t\t\t\t{\n3025--3028 | \t\t\t\t{\n3029--3032 | \t\t\t\t{\n3033--3036 | \t\t\t\t{\n3037--3040 | \t\t\t\t{\n3041--3044 | \t\t\t\t{\n3045--3048 | \t\t\t\t{\n3049--3052 | \t\t\t\t{\n3053--3056 | \t\t\t\t{\n3057--3060 | \t\t\t\t{\n3061--3064 | \t\t\t\t{\n3065--3068 | \t\t\t\t{\n3069--3072 | \t\t\t\t{\n3073--3076 | \t\t\t\t{\n3077--3080 | \t\t\t\t{\n3081--3084 | \t\t\t\t{\n3085--3088 | \t\t\t\t{\n3089--3092 | \t\t\t\t{\n3093--3096 | \t\t\t\t{\n3097--3100 | \t\t\t\t{\n3101--3104 | \t\t\t\t{\n3105--3108 | \t\t\t\t{\n3109--3112 | \t\t\t\t{\n3113--3116 | \t\t\t\t{\n3117--3120 | \t\t\t\t{\n3121--3124 | \t\t\t\t{\n3125--3128 | \t\t\t\t{\n3129--3132 | \t\t\t\t{\n3133--3136 | \t\t\t\t{\n3137--3140 | \t\t\t\t{\n3141--3144 | \t\t\t\t{\n3145--3148 | \t\t\t\t{\n3149--3152 | \t\t\t\t{\n3154--3164 | \t\t\t\"view/title\": [\n3155--3159 | \t\t\t\t{\n3160--3163 | \t\t\t\t{\n3165--3176 | \t\t\t\"view/item/context\": [\n3166--3170 | \t\t\t\t{\n3171--3175 | \t\t\t\t{\n3177--3193 | \t\t\t\"searchPanel/aiResults/commands\": [\n3178--3182 | \t\t\t\t{\n3183--3187 | \t\t\t\t{\n3188--3192 | \t\t\t\t{\n3194--3205 | \t\t\t\"comments/comment/title\": [\n3195--3199 | \t\t\t\t{\n3200--3204 | \t\t\t\t{\n3206--3222 | \t\t\t\"commentsView/commentThread/context\": [\n3207--3211 | \t\t\t\t{\n3212--3216 | \t\t\t\t{\n3217--3221 | \t\t\t\t{\n3223--3244 | \t\t\t\"comments/commentThread/additionalActions\": [\n3224--3228 | \t\t\t\t{\n3229--3233 | \t\t\t\t{\n3234--3238 | \t\t\t\t{\n3239--3243 | \t\t\t\t{\n3245--3256 | \t\t\t\"copilot/reviewComment/additionalActions/applyAndNext\": [\n3246--3250 | \t\t\t\t{\n3251--3255 | \t\t\t\t{\n3257--3273 | \t\t\t\"copilot/reviewComment/additionalActions/discardAndNext\": [\n3258--3262 | \t\t\t\t{\n3263--3267 | \t\t\t\t{\n3268--3272 | \t\t\t\t{\n3274--3285 | \t\t\t\"copilot/reviewComment/additionalActions/discard\": [\n3275--3279 | \t\t\t\t{\n3280--3284 | \t\t\t\t{\n3286--3307 | \t\t\t\"comments/commentThread/title\": [\n3287--3291 | \t\t\t\t{\n3292--3296 | \t\t\t\t{\n3297--3301 | \t\t\t\t{\n3302--3306 | \t\t\t\t{\n3308--3319 | \t\t\t\"scm/title\": [\n3309--3313 | \t\t\t\t{\n3314--3318 | \t\t\t\t{\n3320--3325 | \t\t\t\"scm/inputBox\": [\n3321--3324 | \t\t\t\t{\n3326--3332 | \t\t\t\"testing/message/context\": [\n3327--3331 | \t\t\t\t{\n3333--3339 | \t\t\t\"debug/createConfiguration\": [\n3334--3338 | \t\t\t\t{\n3340--3344 | \t\t\t\"issue/reporter\": [\n3345--3366 | \t\t\t\"github.copilot.chat.debug.filter\": [\n3346--3350 | \t\t\t\t{\n3351--3355 | \t\t\t\t{\n3356--3360 | \t\t\t\t{\n3361--3365 | \t\t\t\t{\n3367--3378 | \t\t\t\"notebook/toolbar\": [\n3368--3372 | \t\t\t\t{\n3373--3377 | \t\t\t\t{\n3380--3402 | \t\t\"icons\": {\n3381--3387 | \t\t\t\"copilot-logo\": {\n3383--3386 | \t\t\t\t\"default\": {\n3388--3394 | \t\t\t\"copilot-warning\": {\n3390--3393 | \t\t\t\t\"default\": {\n3395--3401 | \t\t\t\"copilot-notconnected\": {\n3397--3400 | \t\t\t\t\"default\": {\n3403--3413 | \t\t\"iconFonts\": [\n3404--3412 | \t\t\t{\n3406--3411 | \t\t\t\t\"src\": [\n3407--3410 | \t\t\t\t\t{\n3414--3463 | \t\t\"terminalQuickFixes\": [\n3415--3426 | \t\t\t{\n3419--3424 | \t\t\t\t\"outputMatcher\": {\n3427--3438 | \t\t\t{\n3432--3437 | \t\t\t\t\"outputMatcher\": {\n3439--3450 | \t\t\t{\n3444--3449 | \t\t\t\t\"outputMatcher\": {\n3451--3462 | \t\t\t{\n3456--3461 | \t\t\t\t\"outputMatcher\": {\n3464--3478 | \t\t\"languages\": [\n3465--3471 | \t\t\t{\n3472--3477 | \t\t\t{\n3479--3496 | \t\t\"views\": {\n3480--3487 | \t\t\t\"copilot-chat\": [\n3481--3486 | \t\t\t\t{\n3488--3495 | \t\t\t\"context-inspector\": [\n3489--3494 | \t\t\t\t{\n3497--3510 | \t\t\"viewsContainers\": {\n3498--3509 | \t\t\t\"activitybar\": [\n3499--3503 | \t\t\t\t{\n3504--3508 | \t\t\t\t{\n3511--3515 | \t\t\"configurationDefaults\": {\n3516--3523 | \t\t\"keybindings\": [\n3517--3522 | \t\t\t{\n3524--3683 | \t\t\"walkthroughs\": [\n3525--3682 | \t\t\t{\n3530--3681 | \t\t\t\t\"steps\": [\n3531--3545 | \t\t\t\t\t{\n3536--3544 | \t\t\t\t\t\t\"media\": {\n3537--3542 | \t\t\t\t\t\t\t\"video\": {\n3546--3560 | \t\t\t\t\t{\n3551--3559 | \t\t\t\t\t\t\"media\": {\n3552--3557 | \t\t\t\t\t\t\t\"video\": {\n3561--3575 | \t\t\t\t\t{\n3566--3574 | \t\t\t\t\t\t\"media\": {\n3567--3572 | \t\t\t\t\t\t\t\"video\": {\n3576--3590 | \t\t\t\t\t{\n3581--3589 | \t\t\t\t\t\t\"media\": {\n3582--3587 | \t\t\t\t\t\t\t\"video\": {\n3591--3605 | \t\t\t\t\t{\n3596--3604 | \t\t\t\t\t\t\"media\": {\n3597--3602 | \t\t\t\t\t\t\t\"video\": {\n3606--3620 | \t\t\t\t\t{\n3611--3619 | \t\t\t\t\t\t\"media\": {\n3612--3617 | \t\t\t\t\t\t\t\"video\": {\n3621--3635 | \t\t\t\t\t{\n3626--3634 | \t\t\t\t\t\t\"media\": {\n3627--3632 | \t\t\t\t\t\t\t\"video\": {\n3636--3650 | \t\t\t\t\t{\n3641--3649 | \t\t\t\t\t\t\"media\": {\n3642--3647 | \t\t\t\t\t\t\t\"video\": {\n3651--3665 | \t\t\t\t\t{\n3656--3664 | \t\t\t\t\t\t\"media\": {\n3657--3662 | \t\t\t\t\t\t\t\"video\": {\n3666--3680 | \t\t\t\t\t{\n3671--3679 | \t\t\t\t\t\t\"media\": {\n3672--3677 | \t\t\t\t\t\t\t\"video\": {\n3684--3689 | \t\t\"jsonValidation\": [\n3685--3688 | \t\t\t{\n3690--3695 | \t\t\"typescriptServerPlugins\": [\n3691--3694 | \t\t\t{\n3700--3704 | \t\"prettier\": {\n3705--3742 | \t\"scripts\": {\n3743--3823 | \t\"devDependencies\": {\n3824--3847 | \t\"dependencies\": {\n3848--3852 | \t\"overrides\": {\n</list_code_definition_names>\n<notice>Showing only 500 of 3853 total lines. Use line_range if you need to read more lines</notice>\n</file>\n</files>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n\n\n# VSCode Open Tabs\n\n\n# Current Time\n2025/7/18 下午2:27:13 (Asia/Shanghai, UTC+8:00)\n\n# Current Cost\n$0.00\n\n# Current Mode\n<slug>ask</slug>\n<name>❓ Ask</name>\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\nYou have not created a todo list yet. Create one with `update_todo_list` if your task is complicated or involves multiple steps.\n</environment_details>"}], "ts": 1752820033263}, {"role": "assistant", "content": [{"type": "text", "text": "<thinking>\n从package.json文件中我可以看到该插件的激活事件和配置信息。让我总结一下VSCode插件的加载机制并完成回答。\n</thinking>\n\n<attempt_completion>\n<result>\nVSCode插件在安装后会自动加载，具体的加载机制是由插件的[`package.json`](package.json:80)中的`activationEvents`配置决定的。从这个项目的配置可以看到以下激活时机：\n\n```json\n\"activationEvents\": [\n    \"onStartupFinished\",\n    \"onLanguageModelChat:copilot\",\n    \"onUri\",\n    \"onFileSystem:ccreq\",\n    \"onFileSystem:ccsettings\"\n]\n```\n\n主要的加载方式有：\n\n1. 启动时加载：\n- [`onStartupFinished`](package.json:81) - VSCode启动完成后加载\n- `*` - VSCode启动时立即加载(本插件未使用此配置)\n\n2. 按需加载：\n- [`onLanguageModelChat:copilot`](package.json:82) - 当使用Copilot聊天功能时加载\n- [`onUri`](package.json:83) - 当访问特定URI时加载\n- [`onFileSystem`](package.json:84) - 当访问特定文件系统时加载\n\n3. 插件入口：\n- [`main`](package.json:87) 字段指定了插件的主入口文件 \"./dist/extension\"\n\n这种机制确保了：\n- 插件可以在合适的时机自动加载\n- 通过合理配置`activationEvents`可以优化VSCode的启动性能\n- 不同类型的插件可以选择最适合的加载时机\n</result>\n</attempt_completion>"}], "ts": 1752820054470}]