[{"role": "user", "content": [{"type": "text", "text": "<task>\n帮我看下天气预报\n</task>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/0,../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/1,../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/0,../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/1,packages/agent-driven/web-agent/src/adaptor/components/ChatTextAreaAdaptor.tsx\n\n# VSCode Open Tabs\nsrc/extension.ts,packages/version/src/syncFeConfigToJoyCoder.ts,packages/agent-driven/src/core/webview/JoycoderProvider.ts,packages/agent-driven/web-agent/src/adaptor/components/ChatTextAreaAdaptor.tsx,packages/agent-driven/src/core/config/CustomModesManager.ts\n\n# Current Time\n2025/6/7 下午9:47:48 (Asia/Shanghai, UTC+8:00)\n\n# Current Context Size (Tokens)\n(Not available)\n\n# Current Cost\n$0.00\n\n# Current Mode\n<slug>test-01</slug>\n<name>💻 Code</name>\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\n\n\nNOTE: You are currently in 'test-01' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as '💻 Code' mode.\n\n# Current Workspace Directory (/Users/<USER>/Documents/work/JoyCoder-Plugin) Files\n.commitlintrc.js\n.editorconfig\n.eslintrc.json\n.gitignore\n.npmrc\n.prettierignore\n.prettierrc\n.vscodeignore\nCHANGELOG.md\nCONTRIBUTING.md\nesbuild.js\nLICENSE.md\nmodel.json\npackage.json\npnpm-lock.yaml\npnpm-workspace.yaml\nREADME.md\nassets/\nassets/activitybar.svg\nassets/joycode-light-talk.svg\nassets/logo-close.svg\nassets/logo-new.png\ndoc/\ndoc/rules.md\ndoc/testing.md\npackages/\npackages/agent-driven/src/__mocks__/vscode.js\npackages/agent-driven/src/__mocks__/vscode.min.js\npackages/agent-driven/src/adaptor/api/index.ts\npackages/agent-driven/src/adaptor/api/providers/anthropic.ts\npackages/agent-driven/src/adaptor/api/providers/openai.ts\npackages/agent-driven/src/adaptor/api/transform/o1-format.ts\npackages/agent-driven/src/adaptor/api/transform/openai-format.ts\npackages/agent-driven/src/adaptor/api/transform/stream.ts\npackages/agent-driven/src/adaptor/contextMenu/index.ts\npackages/agent-driven/src/adaptor/translate/message.ts\npackages/agent-driven/src/core/Joycoder.ts\npackages/agent-driven/src/core/types.ts\npackages/agent-driven/src/core/assistant-message/diff.ts\npackages/agent-driven/src/core/assistant-message/index.ts\npackages/agent-driven/src/core/assistant-message/parse-assistant-message.ts\npackages/agent-driven/src/core/context-tracking/FileContextTracker.test.ts\npackages/agent-driven/src/core/context-tracking/FileContextTracker.ts\npackages/agent-driven/src/core/context-tracking/FileContextTrackerTypes.ts\npackages/agent-driven/src/core/diff/insert-groups.ts\npackages/agent-driven/src/core/diff/strategies/multi-search-replace.ts\npackages/agent-driven/src/core/ignore/JoycoderIgnoreController.test.ts\npackages/agent-driven/src/core/ignore/JoycoderIgnoreController.ts\npackages/agent-driven/src/core/JoyCoder/getEnvironmentDetails.ts\npackages/agent-driven/src/core/JoyCoder/presentAssistantMessage.ts\npackages/agent-driven/src/core/JoyCoder/recursivelyMakeJoyCoderRequests.ts\npackages/agent-driven/src/core/JoyCoder/restoreCheckpoint.ts\npackages/agent-driven/src/core/mentions/index.ts\npackages/agent-driven/src/core/prompts/chat.ts\npackages/agent-driven/src/core/prompts/commands.ts\npackages/agent-driven/src/core/prompts/getUseBrowserTool.ts\npackages/agent-driven/src/core/prompts/loadMcpDocumentation.ts\npackages/agent-driven/src/core/prompts/responses.ts\npackages/agent-driven/src/core/prompts/system.ts\npackages/agent-driven/src/core/prompts/__tests__/responses.test.ts\npackages/agent-driven/src/core/prompts/__tests__/sections.test.ts\npackages/agent-driven/src/core/prompts/__tests__/system.test.ts\npackages/agent-driven/src/core/prompts/__tests__/tools.test.ts\npackages/agent-driven/src/core/prompts/__tests__/utils.ts\npackages/agent-driven/src/core/prompts/instructions/create-mcp-server.ts\npackages/agent-driven/src/core/prompts/sections/capabilities.ts\npackages/agent-driven/src/core/prompts/sections/custom-instructions.ts\npackages/agent-driven/src/core/prompts/sections/index.ts\npackages/agent-driven/src/core/prompts/sections/mcp-servers.ts\npackages/agent-driven/src/core/prompts/sections/memory-module.ts\npackages/agent-driven/src/core/prompts/sections/modes.ts\npackages/agent-driven/src/core/prompts/sections/objective.ts\npackages/agent-driven/src/core/prompts/sections/rules.ts\npackages/agent-driven/src/core/prompts/sections/tool-use-examples.ts\npackages/agent-driven/src/core/prompts/sections/tool-use-guidelines.ts\npackages/agent-driven/src/core/prompts/sections/tool-use.ts\npackages/agent-driven/src/core/slash-commands/index.ts\npackages/agent-driven/src/core/storage/disk.ts\npackages/agent-driven/src/core/webview/getNonce.ts\npackages/agent-driven/src/core/webview/getUri.ts\npackages/agent-driven/src/core/webview/handleWebviewMessage.ts\npackages/agent-driven/src/core/webview/JoycoderProvider.ts\npackages/agent-driven/src/core/webview/mode-validator.ts\npackages/agent-driven/src/exports/index.ts\npackages/agent-driven/src/exports/JoyCoderAPI.ts\npackages/agent-driven/src/exports/README.md\npackages/agent-driven/src/integrations/debug/DebugConsoleManager.ts\npackages/agent-driven/src/integrations/diagnostics/DiagnosticsMonitor.ts\npackages/agent-driven/src/integrations/diagnostics/index.ts\npackages/agent-driven/src/integrations/editor/DecorationController.ts\npackages/agent-driven/src/integrations/editor/detect-omission.ts\npackages/agent-driven/src/integrations/editor/DiffViewProvider.ts\npackages/agent-driven/src/integrations/notifications/index.ts\npackages/agent-driven/src/integrations/theme/getTheme.ts\npackages/agent-driven/src/integrations/theme/default-themes/dark_modern.json\npackages/agent-driven/src/integrations/theme/default-themes/dark_plus.json\npackages/agent-driven/src/integrations/theme/default-themes/dark_vs.json\npackages/agent-driven/src/integrations/theme/default-themes/hc_black.json\npackages/agent-driven/src/integrations/theme/default-themes/hc_light.json\npackages/agent-driven/src/integrations/theme/default-themes/light_modern.json\npackages/agent-driven/src/integrations/theme/default-themes/light_plus.json\npackages/agent-driven/src/integrations/theme/default-themes/light_vs.json\npackages/agent-driven/src/schemas/index.ts\npackages/agent-driven/src/schemas/ipc.ts\npackages/agent-driven/src/shared/api.ts\npackages/agent-driven/src/shared/array.test.ts\npackages/agent-driven/src/shared/array.ts\npackages/agent-driven/src/shared/AutoApprovalSettings.ts\npackages/agent-driven/src/shared/BrowserSettings.ts\npackages/agent-driven/src/shared/ChatSettings.ts\npackages/agent-driven/src/shared/combineApiRequests.ts\npackages/agent-driven/src/shared/combineCommandSequences.ts\npackages/agent-driven/src/shared/context-mentions.ts\npackages/agent-driven/src/shared/experiments.ts\npackages/agent-driven/src/shared/ExtensionMessage.ts\npackages/agent-driven/src/shared/getApiMetrics.ts\npackages/agent-driven/src/shared/HistoryItem.ts\npackages/agent-driven/src/shared/language.ts\npackages/agent-driven/src/shared/mcp.ts\npackages/agent-driven/src/shared/modes.ts\npackages/agent-driven/src/shared/tools.ts\npackages/agent-driven/src/shared/types.ts\npackages/agent-driven/src/shared/vsCodeSelectorUtils.ts\npackages/agent-driven/src/shared/WebviewMessage.ts\npackages/agent-driven/src/test/extension.test.ts\npackages/agent-driven/src/test/shell.test.ts\npackages/agent-driven/src/test/suite/extension.test.js\npackages/agent-driven/src/test/suite/index.js\npackages/agent-driven/src/utils/cost.test.ts\npackages/agent-driven/src/utils/cost.ts\npackages/agent-driven/src/utils/FileSystemHelper.ts\npackages/agent-driven/src/utils/fs.test.ts\npackages/agent-driven/src/utils/fs.ts\npackages/agent-driven/src/utils/git.ts\npackages/agent-driven/src/utils/path.test.ts\npackages/agent-driven/src/utils/path.ts\npackages/agent-driven/src/utils/pathUtils.ts\npackages/agent-driven/src/utils/remoteOSName.ts\npackages/agent-driven/src/utils/shell.ts\npackages/agent-driven/src/utils/single-completion-handler.ts\npackages/agent-driven/src/utils/storage.ts\npackages/agent-driven/src/utils/string.ts\npackages/agent-driven/src/utils/text-normalization.ts\npackages/agent-driven/src/utils/time.ts\npackages/agent-driven/src/utils/type-fu.ts\npackages/agent-driven/src/utils/xml.ts\npackages/plugin-base-ai-d2c/src/index.ts\npackages/plugin-base-ai-d2c/src/interface.ts\npackages/plugin-base-ai-d2c/src/functions/base.ts\npackages/plugin-base-ai-d2c/src/utils/ast.ts\npackages/plugin-base-ai-d2c/src/utils/chatToFiles.ts\npackages/plugin-base-ai-d2c/src/utils/logs.ts\npackages/plugin-base-app-manager/tsconfig.json\npackages/plugin-base-app-manager/src/dealJxappPages.ts\npackages/plugin-base-app-manager/src/index.ts\npackages/plugin-base-app-manager/src/template.ts\npackages/plugin-base-browser/src/index.ts\npackages/plugin-base-browser/src/browser/BrowserClient.ts\npackages/plugin-base-browser/src/browser/BrowserPage.ts\npackages/plugin-base-browser/src/provider/ContentProvider.ts\npackages/plugin-base-browser/src/shared/constant.ts\npackages/plugin-base-browser/src/shared/interface.ts\npackages/plugin-base-browser/src/shared/utils/CommonTool.ts\npackages/plugin-base-browser/src/webview-panel/WebviewPanel.ts\npackages/plugin-base-browser/src/webview-panel/WebviewPanelManager.ts\npackages/plugin-base-tree-view/package.json\npackages/plugin-base-tree-view/README.md\npackages/plugin-base-tree-view/tsconfig.json\npackages/plugin-base-tree-view/src/commands.ts\npackages/plugin-base-tree-view/src/index.ts\npackages/plugin-base-tree-view/src/viewProvider.ts\npackages/plugin-base-upload-image/src/index.ts\npackages/plugin-base-upload-image/src/pasteImage/index.ts\npackages/plugin-base-upload-image/src/uploadImage/index.ts\npackages/vscode-business/.vscodeignore\npackages/vscode-business/CHANGELOG.md\npackages/vscode-business/LICENSE.md\npackages/vscode-IDE/src/extension.ts\npackages/vscode-plugin-demo/.vscodeignore\npackages/vscode-plugin-demo/webpack.config.js\npackages/vscode-plugin-demo/assets/logo.png\npackages/vscode-plugin-demo/src/extension.ts\nremote_binary/\nremote_binary/better-sqlite3/download.js\nremote_binary/stat/statCode-darwin-arm_1.0.0.zip\nremote_binary/stat/statCode-darwin-x64_1.0.0.zip\nremote_binary/stat/statCode-linux-x64_1.0.0.zip\nremote_binary/stat/statCode-win32-x64_1.0.0.zip\nremote_binary/stat/statCodeDarwinAmd\nremote_binary/stat/statCodeDarwinArm\nremote_binary/stat/statCodeLinuxAmd\nremote_binary/stat/statCodeWindows.exe\nscripts/\nscripts/build/utils.js\nscripts/os/linux.sh\nscripts/os/mac.applescript\nscripts/os/pc.ps1\nsnippets/\nsrc/\ntypings/\n\n(File list truncated. Use list_files on specific subdirectories if you need to explore further.)\n</environment_details>"}], "ts": 1749304068657}, {"role": "assistant", "content": [{"type": "text", "text": "<thinking>\n分析用户需求：用户想要查看天气预报。我们可以通过以下几种方式获取天气信息：\n\n1. 使用已连接的 MCP 服务器中的天气相关工具（如果有）\n2. 使用 browser_action 工具访问天气预报网站\n\n检查可用的 MCP 服务器工具：\n- 目前只有 context7 服务器，它只提供了库文档相关的工具，没有天气相关的功能\n\n因此，我们将使用 browser_action 工具访问天气预报网站来完成这个任务。我会选择访问中国天气网，因为它提供准确的天气信息。\n</thinking>\n\n<browser_action>\n<action\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]"}], "ts": 1749304077065}, {"role": "user", "content": [{"type": "text", "text": "[browser_action for 'undefined'] Result:"}, {"type": "text", "text": "The tool execution failed with the following error:\n<error>\nTool \"browser_action\" is not allowed in test-01 mode.\n</error>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/0,../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/1,../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/0,../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/1,packages/agent-driven/web-agent/src/adaptor/components/ChatTextAreaAdaptor.tsx\n\n# VSCode Open Tabs\nsrc/extension.ts,packages/version/src/syncFeConfigToJoyCoder.ts,packages/agent-driven/src/core/webview/JoycoderProvider.ts,packages/agent-driven/web-agent/src/adaptor/components/ChatTextAreaAdaptor.tsx,packages/agent-driven/src/core/config/CustomModesManager.ts\n\n# Current Time\n2025/6/7 下午9:47:57 (Asia/Shanghai, UTC+8:00)\n\n# Current Context Size (Tokens)\n(Not available)\n\n# Current Cost\n$0.00\n\n# Current Mode\n<slug>test-01</slug>\n<name>💻 Code</name>\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\n\n\nNOTE: You are currently in 'test-01' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as '💻 Code' mode.\n</environment_details>"}], "ts": 1749304077079}, {"role": "assistant", "content": [{"type": "text", "text": "<thinking>\n我注意到当前处于 test-01 模式，该模式不允许使用 browser_action 工具。为了完成查看天气预报的任务，我们需要切换到支持使用 browser_action 工具的模式，比如 Code 模式。\n</thinking>\n\n<switch_mode>\n<mode_slug>code</mode_slug>\n<reason>需要使用 browser_action 工具来访问天气预报网站</reason>\n</switch_mode>\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]"}], "ts": 1749304083737}, {"role": "user", "content": [{"type": "text", "text": "[switch_mode to 'code' because: 需要使用 browser_action 工具来访问天气预报网站] Result:"}, {"type": "text", "text": "Successfully switched from test-01 mode to 💻 Code mode because: 需要使用 browser_action 工具来访问天气预报网站."}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/0,../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/1,../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/0,../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/1,packages/agent-driven/web-agent/src/adaptor/components/ChatTextAreaAdaptor.tsx\n\n# VSCode Open Tabs\nsrc/extension.ts,packages/version/src/syncFeConfigToJoyCoder.ts,packages/agent-driven/src/core/webview/JoycoderProvider.ts,packages/agent-driven/web-agent/src/adaptor/components/ChatTextAreaAdaptor.tsx,packages/agent-driven/src/core/config/CustomModesManager.ts\n\n# Current Time\n2025/6/7 下午9:48:03 (Asia/Shanghai, UTC+8:00)\n\n# Current Context Size (Tokens)\n16,916 (13%)\n\n# Current Cost\n$0.00\n\n# Current Mode\n<slug>code</slug>\n<name>💻 Code</name>\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\n</environment_details>"}], "ts": 1749304083918}, {"role": "assistant", "content": [{"type": "text", "text": "<thinking>\n现在已经切换到 Code 模式，我可以使用 browser_action 工具来访问天气预报网站。我将选择访问中国天气网，因为它提供准确的天气信息。\n</thinking>\n\n<browser_action>\n<action>launch</action>\n<url>http://www.weather.com.cn/</url>\n</browser_action>"}], "ts": 1749304090593}, {"role": "user", "content": [{"type": "text", "text": "[browser_action for 'launch'] Result:"}, {"type": "text", "text": "The browser action has been executed. The console logs and screenshot have been captured for your analysis.\n\nConsole logs:\n[warn] Error with Permissions-Policy header: Invalid allowlist item(none) for feature geolocation. Allowlist item must be *, self or quoted url.\n[warn] Error with Permissions-Policy header: Invalid allowlist item(none) for feature fullscreen. Allowlist item must be *, self or quoted url.\n[warn] Error with Permissions-Policy header: Invalid allowlist item(none) for feature microphone. Allowlist item must be *, self or quoted url.\n[warn] Error with Permissions-Policy header: Invalid allowlist item(none) for feature camera. Allowlist item must be *, self or quoted url.\n[warn] Error with Permissions-Policy header: Invalid allowlist item(none) for feature clipboard-read. Allowlist item must be *, self or quoted url.\n[error] The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pi.weather.com.cn/i//product/pic/l/sevp_nsmc_wxbl_fy4b_etcc_achn_lno_py_20250607124500000.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2025/06/06/435A2BB238E672FEF1C2FF4A9044866E.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2025/06/07/4A836A2B521FB231E849D7F73A3DB3CC.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2025/06/06/2B6B64D4ACCFFAC3E84C23B491320FAE.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pi.weather.com.cn/i//product/pic/m/sevp_nmc_stfc_sfer_er24_achn_l88_p9_20250607120002400.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/video/lssj/2025/06/07/A5E65701682DC561769385732A12DF14_m.png'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/video/lssj/2025/06/07/7152A7E360C0DE099AF82F58767804D5_m.png'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/index/2025/06/04/EF8A0D38191E8CB19EFA4DE247D556DF.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://www.weather.com.cn/2025/06/07/DBECF791AB44810359627BA6E0E55E5B.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://www.weather.com.cn/2025/05/29/63AB153AB8AAF034AFF4E69936A6E1C5.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://www.weather.com.cn/2025/06/07/924C052867A572225550CBC70141A5A0.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/index/2024/03/18/202403181336470A7F03D1DA389C5AC74DDB7AC0745065.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/life/shrd/2025/06/04/FB6448A41A170DE06656CB8E3D819137.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/life/shrd/2025/05/30/E463DCBDC0C92037BCC18587B1C5107C.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/life/shrd/2025/06/03/9B9A47213B48298C9B786852302B1067.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/life/shrd/2025/06/04/6C81A455E79D95F74FC09D23795927D6.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/life/shrd/2025/05/19/F2235AE5EA98DE24BFA22350D09CCD6A.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/life/shrd/2025/05/20/9D51E88FEAAD62636738498FCE36ABCB.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/sjztj/2020/07/20/20200720142523B5F07D41B4AC4336613DA93425B35B5E_xm.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2019/10/28/20191028144048D58023A73C43EC6EEB61610B0AB0AD74_xm.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2025/06/05/2C6C63DD7CC3F21999063A34C7D0A336.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2025/06/07/4A836A2B521FB231E849D7F73A3DB3CC.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2025/06/06/56FC2BC77613113F1886946E0C197779.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2025/06/05/6FD06EF0A6E470724EC99A938D65403C.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2025/06/06/435A2BB238E672FEF1C2FF4A9044866E.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/news/2021/05/14/20210514192548638D53A47159C5D97689A921C03B6546.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/news/2021/03/26/20210326150416454FB344B92EC8BD897FA50DF6AD15E8.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/news/2021/03/19/20210319155915527B7F1E763A8D9C65581C8A4873D2B0.png'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/science/2020/07/28/202007281001285C97A5D6CAD3BC4DD74F98B5EA5187BF.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pi.weather.com.cn/i//product/pic/m/sevp_nmc_stfc_sfer_er24_achn_l88_p9_20250607120002400.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pi.weather.com.cn/i//product/pic/m/sevp_ncc_zki_shlsk_eci_achn_l88_pb_20250607000000000.png'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pi.weather.com.cn/i//product/pic/m/z_rada_c_babj_20250607133102_p_dor_achn_cref_20250607_132400.png'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pi.weather.com.cn/i//product/pic/m/sevp_nsmc_wxbl_fy4b_etcc_achn_lno_py_20250607124500000.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.tq121.com.cn/i/weather2017/cx_new.png'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] A parser-blocking, cross site (i.e. different eTLD+1) script, https://i.tq121.com.cn/j/version.js?0.10429273235670211, is invoked via document.write. The network request for this script MAY be blocked by the browser in this or a future page load due to poor network connectivity. If blocked in this page load, it will be confirmed in a subsequent console message. See https://www.chromestatus.com/feature/5718547946799104 for more details.\n[warn] A parser-blocking, cross site (i.e. different eTLD+1) script, https://i.tq121.com.cn/j/version.js?0.10429273235670211, is invoked via document.write. The network request for this script MAY be blocked by the browser in this or a future page load due to poor network connectivity. If blocked in this page load, it will be confirmed in a subsequent console message. See https://www.chromestatus.com/feature/5718547946799104 for more details.\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pi.weather.com.cn/i//product/pic/l/sevp_nsmc_wxbl_fy4b_etcc_achn_lno_py_20250607124500000.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2025/06/06/435A2BB238E672FEF1C2FF4A9044866E.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2025/06/07/4A836A2B521FB231E849D7F73A3DB3CC.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2025/06/06/2B6B64D4ACCFFAC3E84C23B491320FAE.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pi.weather.com.cn/i//product/pic/m/sevp_nmc_stfc_sfer_er24_achn_l88_p9_20250607120002400.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/video/lssj/2025/06/07/A5E65701682DC561769385732A12DF14_m.png'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/video/lssj/2025/06/07/7152A7E360C0DE099AF82F58767804D5_m.png'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/index/2025/06/04/EF8A0D38191E8CB19EFA4DE247D556DF.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://www.weather.com.cn/2025/06/07/DBECF791AB44810359627BA6E0E55E5B.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://www.weather.com.cn/2025/05/29/63AB153AB8AAF034AFF4E69936A6E1C5.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://www.weather.com.cn/2025/06/07/924C052867A572225550CBC70141A5A0.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/index/2024/03/18/202403181336470A7F03D1DA389C5AC74DDB7AC0745065.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/life/shrd/2025/06/04/FB6448A41A170DE06656CB8E3D819137.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/life/shrd/2025/05/30/E463DCBDC0C92037BCC18587B1C5107C.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/life/shrd/2025/06/03/9B9A47213B48298C9B786852302B1067.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/life/shrd/2025/06/04/6C81A455E79D95F74FC09D23795927D6.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/life/shrd/2025/05/19/F2235AE5EA98DE24BFA22350D09CCD6A.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/life/shrd/2025/05/20/9D51E88FEAAD62636738498FCE36ABCB.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/sjztj/2020/07/20/20200720142523B5F07D41B4AC4336613DA93425B35B5E_xm.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2019/10/28/20191028144048D58023A73C43EC6EEB61610B0AB0AD74_xm.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2025/06/05/2C6C63DD7CC3F21999063A34C7D0A336.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2025/06/07/4A836A2B521FB231E849D7F73A3DB3CC.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2025/06/06/56FC2BC77613113F1886946E0C197779.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2025/06/05/6FD06EF0A6E470724EC99A938D65403C.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pic.weather.com.cn/images/cn/photo/2025/06/06/435A2BB238E672FEF1C2FF4A9044866E.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/news/2021/05/14/20210514192548638D53A47159C5D97689A921C03B6546.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/news/2021/03/26/20210326150416454FB344B92EC8BD897FA50DF6AD15E8.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/news/2021/03/19/20210319155915527B7F1E763A8D9C65581C8A4873D2B0.png'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.weather.com.cn/images/cn/science/2020/07/28/202007281001285C97A5D6CAD3BC4DD74F98B5EA5187BF.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pi.weather.com.cn/i//product/pic/m/sevp_nmc_stfc_sfer_er24_achn_l88_p9_20250607120002400.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pi.weather.com.cn/i//product/pic/m/sevp_ncc_zki_shlsk_eci_achn_l88_pb_20250607000000000.png'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pi.weather.com.cn/i//product/pic/m/z_rada_c_babj_20250607133102_p_dor_achn_cref_20250607_132400.png'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://pi.weather.com.cn/i//product/pic/m/sevp_nsmc_wxbl_fy4b_etcc_achn_lno_py_20250607124500000.jpg'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] Mixed Content: The page at 'https://www.weather.com.cn/' was loaded over HTTPS, but requested an insecure element 'http://i.tq121.com.cn/i/weather2017/cx_new.png'. This request was automatically upgraded to HTTPS, For more information see https://blog.chromium.org/2019/10/no-more-mixed-messages-about-https.html\n[warn] A parser-blocking, cross site (i.e. different eTLD+1) script, https://hm.baidu.com/h.js?080dabacb001ad3dc8b9b9049b36d43b, is invoked via document.write. The network request for this script MAY be blocked by the browser in this or a future page load due to poor network connectivity. If blocked in this page load, it will be confirmed in a subsequent console message. See https://www.chromestatus.com/feature/5718547946799104 for more details.\n[warn] A parser-blocking, cross site (i.e. different eTLD+1) script, https://hm.baidu.com/h.js?080dabacb001ad3dc8b9b9049b36d43b, is invoked via document.write. The network request for this script MAY be blocked by the browser in this or a future page load due to poor network connectivity. If blocked in this page load, it will be confirmed in a subsequent console message. See https://www.chromestatus.com/feature/5718547946799104 for more details.\n\n(REMEMBER: if you need to proceed to using non-`browser_action` tools or launch a new browser, you MUST first close cline browser. For example, if after analyzing the logs and screenshot you need to edit a file, you must first close the browser before you can use the write_to_file tool.)"}, {"type": "image", "source": {"type": "base64", "media_type": "image/webp", "data": "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"}}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/0,../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/1,../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/0,../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/1,packages/agent-driven/web-agent/src/adaptor/components/ChatTextAreaAdaptor.tsx\n\n# VSCode Open Tabs\nsrc/extension.ts,packages/version/src/syncFeConfigToJoyCoder.ts,packages/agent-driven/src/core/webview/JoycoderProvider.ts,packages/agent-driven/web-agent/src/adaptor/components/ChatTextAreaAdaptor.tsx,packages/agent-driven/src/core/config/CustomModesManager.ts\n\n# Current Time\n2025/6/7 下午9:48:49 (Asia/Shanghai, UTC+8:00)\n\n# Current Context Size (Tokens)\n17,516 (14%)\n\n# Current Cost\n$0.00\n\n# Current Mode\n<slug>code</slug>\n<name>💻 Code</name>\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\n</environment_details>"}], "ts": 1749304129922}, {"role": "assistant", "content": [{"type": "text", "text": "<thinking>\n根据网页内容，我可以看到当前全国天气的主要信息。让我整理一下关键信息后关闭浏览器。\n</thinking>\n\n<browser_action>\n<action>close</action>\n</browser_action>\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]"}], "ts": 1749304139718}, {"role": "user", "content": [{"type": "text", "text": "[browser_action for 'close'] Result:"}, {"type": "text", "text": "The browser has been closed. You may now proceed to using other tools."}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/0,../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/1,../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/0,../../../../../response_32f7b084-b4ac-4e7b-9d10-c7f1efef451f/1,packages/agent-driven/web-agent/src/adaptor/components/ChatTextAreaAdaptor.tsx\n\n# VSCode Open Tabs\nsrc/extension.ts,packages/version/src/syncFeConfigToJoyCoder.ts,packages/agent-driven/src/core/webview/JoycoderProvider.ts,packages/agent-driven/web-agent/src/adaptor/components/ChatTextAreaAdaptor.tsx,packages/agent-driven/src/core/config/CustomModesManager.ts\n\n# Current Time\n2025/6/7 下午9:48:59 (Asia/Shanghai, UTC+8:00)\n\n# Current Context Size (Tokens)\n28,003 (22%)\n\n# Current Cost\n$0.00\n\n# Current Mode\n<slug>code</slug>\n<name>💻 Code</name>\n<model>anthropic.claude-3-5-sonnet-20241022-v2:0</model>\n</environment_details>"}], "ts": 1749304139739}, {"role": "assistant", "content": [{"type": "text", "text": "<attempt_completion>\n<result>\n根据中国天气网的最新天气预报：\n\n1. 长江沿线地区有强降雨，南北多地高温持续\n2. 主要天气特征：山洪、暴雨、地质灾害、大暑、强对流等\n3. 中国气象局已启动四级应急响应，降雨增多且湿度有窗口期\n4. 高考期间江浙沪等地需防暴雨影响\n5. 高温来袭需注意防暑降温，建议采取相应防护措施\n\n请注意关注当地天气预报，做好防暑降温和防雨准备。\n</result>\n</attempt_completion>"}], "ts": 1749304156643}]