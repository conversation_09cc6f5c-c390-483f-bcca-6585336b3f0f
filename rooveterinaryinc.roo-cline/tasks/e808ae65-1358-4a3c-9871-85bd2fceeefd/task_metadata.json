{"files_in_context": [{"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "stale", "record_source": "read_tool", "roo_read_date": 1752741032861, "roo_edit_date": null, "user_edit_date": null}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "read_tool", "roo_read_date": 1752741041187, "roo_edit_date": null, "user_edit_date": null}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1752741032861, "roo_edit_date": null, "user_edit_date": 1752741169536}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1752741032861, "roo_edit_date": null, "user_edit_date": 1752741172515}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1752741032861, "roo_edit_date": null, "user_edit_date": 1752741382421}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1752741032861, "roo_edit_date": null, "user_edit_date": 1752741492873}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "stale", "record_source": "read_tool", "roo_read_date": 1752741809281, "roo_edit_date": null, "user_edit_date": 1752741492873}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1752741809281, "roo_edit_date": null, "user_edit_date": 1752741909879}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1752741809281, "roo_edit_date": null, "user_edit_date": 1752742006875}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1752741041187, "roo_edit_date": null, "user_edit_date": 1752742067040}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1752741809281, "roo_edit_date": null, "user_edit_date": 1752743732070}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1752741809281, "roo_edit_date": null, "user_edit_date": 1752743757745}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1752741809281, "roo_edit_date": null, "user_edit_date": 1752743763674}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1752741809281, "roo_edit_date": null, "user_edit_date": 1752743768582}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1752741041187, "roo_edit_date": null, "user_edit_date": 1752743770673}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.tsx", "record_state": "active", "record_source": "user_edited", "roo_read_date": 1752741041187, "roo_edit_date": null, "user_edit_date": 1752753737841}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1752741809281, "roo_edit_date": null, "user_edit_date": 1752753754132}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1752741809281, "roo_edit_date": null, "user_edit_date": 1752754317926}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1752741809281, "roo_edit_date": null, "user_edit_date": 1752754320388}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1752741809281, "roo_edit_date": null, "user_edit_date": 1752754427785}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "stale", "record_source": "user_edited", "roo_read_date": 1752741809281, "roo_edit_date": null, "user_edit_date": 1752754611163}, {"path": "packages/agent-setting/src/components/mcp/McpMarketView.scss", "record_state": "active", "record_source": "user_edited", "roo_read_date": 1752741809281, "roo_edit_date": null, "user_edit_date": 1752755179159}]}