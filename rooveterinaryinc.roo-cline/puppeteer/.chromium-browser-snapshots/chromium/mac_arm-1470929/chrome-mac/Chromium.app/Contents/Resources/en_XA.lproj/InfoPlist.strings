"Chromium Shortcut" = "Çĥrömîûm Šĥörţçûţ - one two";
CFBundleGetInfoString = "Chromium 139.0.7225.0, Çöþýrîĝĥţ 2025 Ţĥé Çĥrömîûm Åûţĥörš. Åļļ rîĝĥţš réšérvéð. - one two three four five six seven eight";
NSAudioCaptureUsageDescription = "Öñçé Çĥrömîûm ĥåš åççéšš, ŵébšîţéš ŵîļļ bé åbļé ţö åšķ ýöû ƒör åççéšš. - one two three four five six seven eight nine ten - one two three";
NSBluetoothAlwaysUsageDescription = "Öñçé Çĥrömîûm ĥåš åççéšš, ŵébšîţéš ŵîļļ bé åbļé ţö åšķ ýöû ƒör åççéšš. - one two three four five six seven eight nine ten - one two three";
NSBluetoothPeripheralUsageDescription = "Öñçé Çĥrömîûm ĥåš åççéšš, ŵébšîţéš ŵîļļ bé åbļé ţö åšķ ýöû ƒör åççéšš. - one two three four five six seven eight nine ten - one two three";
NSCameraUsageDescription = "Öñçé Çĥrömîûm ĥåš åççéš<PERSON>, ŵébšîţéš ŵîļļ bé åbļé ţö åšķ ýöû ƒör åççéšš. - one two three four five six seven eight nine ten - one two three";
NSHumanReadableCopyright = "Çöþýrîĝĥţ 2025 Ţĥé Çĥrömîûm Åûţĥörš. Åļļ rîĝĥţš réšérvéð. - one two three four five six seven eight";
NSLocalNetworkUsageDescription = "Ţĥîš ŵîļļ åļļöŵ ýöû ţö šéļéçţ ƒröm åvåîļåbļé ðévîçéš åñð ðîšþļåý çöñţéñţ öñ ţĥém. - one two three four five six seven eight nine ten - one two three four";
NSLocationUsageDescription = "Öñçé Çĥrömîûm ĥåš åççéšš, ŵébšîţéš ŵîļļ bé åbļé ţö åšķ ýöû ƒör åççéšš. - one two three four five six seven eight nine ten - one two three";
NSMicrophoneUsageDescription = "Öñçé Çĥrömîûm ĥåš åççéšš, ŵébšîţéš ŵîļļ bé åbļé ţö åšķ ýöû ƒör åççéšš. - one two three four five six seven eight nine ten - one two three";
NSWebBrowserPublicKeyCredentialUsageDescription = "Öñçé Çĥrömîûm ĥåš åççéšš, ŵébšîţéš ŵîļļ bé åbļé ţö åšķ ýöû ƒör åççéšš. - one two three four five six seven eight nine ten - one two three";
