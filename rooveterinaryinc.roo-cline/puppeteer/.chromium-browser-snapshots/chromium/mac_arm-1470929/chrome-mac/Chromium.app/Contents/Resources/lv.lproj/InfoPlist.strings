"Chromium Shortcut" = "Chromium saīsne";
CFBundleGetInfoString = "Chromium 139.0.7225.0, Autortiesības 2025 Chromium autori. Visas tiesības paturētas.";
NSAudioCaptureUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļuve, vietnes varēs lūgt jums piekļuvi.";
NSBluetoothAlwaysUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļ<PERSON>, vietnes varēs lūgt jums piekļuvi.";
NSBluetoothPeripheralUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļuve, vietnes varēs lūgt jums piekļuvi.";
NSCameraUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļ<PERSON>, vietnes varēs lūgt jums piekļuvi.";
NSHumanReadableCopyright = "Autortiesības 2025 Chromium autori. Visas tiesības paturētas.";
NSLocalNetworkUsageDescription = "Tādējādi varēsiet atlasīt kādu no pieejamām ierīcēm un parādīt tajā saturu.";
NSLocationUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļuve, vietnes varēs lūgt jums piekļuvi.";
NSMicrophoneUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļuve, vietnes varēs lūgt jums piekļuvi.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļuve, vietnes varēs lūgt jums piekļuvi.";
