[{"ts": 1749295186033, "type": "say", "say": "text", "text": "帮我解析devtools的这个位置，分别代表什么", "images": ["data:image/png;base64,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"], "conversationHistoryIndex": -1}, {"ts": 1749295186045, "type": "say", "say": "checkpoint_created", "conversationHistoryIndex": -1, "lastCheckpointHash": "4079913698e15c993a173e9f5dec800a248b5d16", "isCheckpointCheckedOut": false}, {"ts": 1749295186052, "type": "say", "say": "api_req_started", "text": "{\"request\":\"<task>\\n帮我解析devtools的这个位置，分别代表什么\\n</task>\\n\\n[Image]\\n\\n<environment_details>\\n# VSCode Visible Files\\n../../.joycode/joycoder-mcp.json\\n\\n# VSCode Open Tabs\\n../../.joycode/joycoder-mcp.json\\n\\n# Current Time\\n2025/6/7 下午7:19:46 (Asia/Shanghai, UTC+8:00)\\n\\nNOTE: You are currently in 'mode-test-01' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as '聊天' mode.\\n\\n# Current Working Directory (/Users/<USER>/Desktop/aaa_64) Files\\n.DS_Store\\nvue2-ts-project/\\nvue2-ts-project/.gitignore\\nvue2-ts-project/package-lock.json\\nvue2-ts-project/package.json\\nvue2-ts-project/tsconfig.json\\nvue2-ts-project/src/\\nvue2-ts-project/src/App.vue\\nvue2-ts-project/src/main.ts\\nvue2-ts-project/src/shims-vue.d.ts\\nvue2-ts-project/src/router/\\nvue2-ts-project/src/router/index.ts\\nvue2-ts-project/src/utils/\\nvue2-ts-project/src/utils/CommonUtils.ts\\nvue2-ts-project/src/views/\\nvue2-ts-project/src/views/Login.vue\\nvue2-ts-project/src/views/NotFound.vue\\nvue2-ts-project/src/views/Welcome.vue\\n\\n# Current Mode\\n<agentId>mode-test-01</agentId>\\n<name>聊天</name>\\n<model>Claude-3.5-sonnet</model>\\n<role>You are JoyCode, a professional intelligent programming assistant. You excel at solving various programming problems across multiple languages (including but not limited to Python, JavaScript, Java, C++, Go, SQL, etc.).\\n\\n\\n### Core Capabilities\\n1. **Code Understanding and Analysis**: Deeply understand user-provided code, accurately grasp its functionality and intent\\n2. **Problem Diagnosis and Resolution**: Precisely identify errors, performance bottlenecks, and logical issues in code\\n3. **Best Practice Recommendations**: Provide code optimization and architectural improvement suggestions that conform to industry standards\\n4. **Knowledge Answers**: Answer concept-related programming questions with clear and easy-to-understand explanations\\n5. **General Problem Solving**: Solve non-programming questions raised by users, providing accurate and useful information\\n\\n\\n### Response Guidelines\\n- **Respond directly** to user queries without standard greetings, maintaining a professional yet approachable tone\\n- **Match your communication style** to the complexity and context of the user's question\\n- **Deliver immediate value** by addressing the core query first, avoiding templated introductions or unnecessary preamble\\n- **Analyze and address specific user needs** directly upon receiving a question, focusing on their exact requirements\\n- **MANDATORY: Use exactly one tool per response** - Every message must include tool usage; responses without tool utilization are not acceptable\\n\\n\\n### Response Process\\n1. **Understanding Requirements**: Carefully analyze user questions to determine core requirement points\\n2. **Code Analysis** (if applicable): Check code logic, syntax, and potential issues\\n3. **Building Answers**:\\n   - For programming problems: Provide clear explanations, executable code examples, and solutions\\n   - For conceptual questions: Give concise explanations, supplemented with examples\\n   - For general questions: Provide accurate, objective information and advice\\n4. **Solution Verification**: Ensure correctness and effectiveness before providing code\\n5. **Summary and Recommendations**: Provide additional improvement suggestions or learning resources (if applicable)\\n\\n\\n### Output Format\\n- Use clear headings and structured paragraphs\\n- Use appropriate code block formatting for code examples\\n- Highlight important concepts or key points using bold text or lists\\n- Present complex solutions step by step\\n\\n\\n### Code of Conduct\\n- Comply with Chinese laws and regulations, uphold socialist core values\\n- Provide objective, neutral, and beneficial information\\n- When uncertain about answers, honestly acknowledge and provide possible reference directions\\n- Respect users, use professional but friendly tone\\n- Avoid generating harmful, misleading, or inappropriate content\\n\\n\\nIMPORTANT: Begin your responses by directly addressing the user's query without using standard greeting templates. Adapt your communication style to the specific question being asked.</role>\\n<custom_instructions>\\n====\\n\\nUSER'S CUSTOM INSTRUCTIONS\\n\\nThe following additional instructions are provided by the user, and should be followed to the best of your ability without interfering with the TOOL USE guidelines.\\n\\n====\\nMode-specific Instructions:\\n\\n***You should answer user questions directly. Please ensure your responses are concise and clear.***\\n\\n</custom_instructions>\\n\\n# Context Window Usage\\n0 / 128K tokens used (0%)\\n\\n# Current Style\\n</environment_details>\",\"tokensIn\":0,\"tokensOut\":0,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0,\"cancelReason\":\"streaming_failed\",\"streamingFailedMessage\":\"No mode found for agentId: mode-test-01\"}", "conversationHistoryIndex": -1}, {"ts": 1749295191107, "type": "ask", "ask": "resume_task", "conversationHistoryIndex": 1}, {"ts": 1749295193916, "type": "say", "say": "user_feedback", "text": "帮我解析devtools的这个位置，分别代表什么", "images": [], "conversationHistoryIndex": 1}, {"ts": 1749295193930, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[TASK RESUMPTION] This task was interrupted just now. It may or may not be complete, so please reassess the task context. Be aware that the project state may have changed since then. The current working directory is now '/Users/<USER>/Desktop/aaa_64'. If the task has not been completed, retry the last step before interruption and proceed with completing the task.\\n\\nNote: If you previously attempted a tool use that the user did not provide a result for, you should assume the tool use was not successful and assess whether you should retry. If the last tool was a use_browser, the browser has been closed and you must launch a new browser if needed.\\n\\nIMPORTANT: If the last tool use was a use_replace_file or use_write_file that was interrupted, the file was reverted back to its original state before the interrupted edit, and you do NOT need to re-read the file as you already have its up-to-date contents.\\n\\nNew instructions for task continuation:\\n<user_message>\\n帮我解析devtools的这个位置，分别代表什么\\n</user_message>\\n\\n<environment_details>\\n# VSCode Visible Files\\n../../.joycode/joycoder-mcp.json\\n\\n# VSCode Open Tabs\\n../../.joycode/joycoder-mcp.json\\n\\n# Current Time\\n2025/6/7 下午7:19:53 (Asia/Shanghai, UTC+8:00)\\n\\nNOTE: You are currently in '聊天' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as '聊天' mode.\\n\\n# Current Working Directory (/Users/<USER>/Desktop/aaa_64) Files\\n.DS_Store\\nvue2-ts-project/\\nvue2-ts-project/.gitignore\\nvue2-ts-project/package-lock.json\\nvue2-ts-project/package.json\\nvue2-ts-project/tsconfig.json\\nvue2-ts-project/src/\\nvue2-ts-project/src/App.vue\\nvue2-ts-project/src/main.ts\\nvue2-ts-project/src/shims-vue.d.ts\\nvue2-ts-project/src/router/\\nvue2-ts-project/src/router/index.ts\\nvue2-ts-project/src/utils/\\nvue2-ts-project/src/utils/CommonUtils.ts\\nvue2-ts-project/src/views/\\nvue2-ts-project/src/views/Login.vue\\nvue2-ts-project/src/views/NotFound.vue\\nvue2-ts-project/src/views/Welcome.vue\\n\\n# Current Mode\\n<agentId>chat</agentId>\\n<name>聊天</name>\\n<model>Claude-3.5-sonnet</model>\\n<role>You are JoyCode, a professional intelligent programming assistant. You excel at solving various programming problems across multiple languages (including but not limited to Python, JavaScript, Java, C++, Go, SQL, etc.).\\n\\n\\n### Core Capabilities\\n1. **Code Understanding and Analysis**: Deeply understand user-provided code, accurately grasp its functionality and intent\\n2. **Problem Diagnosis and Resolution**: Precisely identify errors, performance bottlenecks, and logical issues in code\\n3. **Best Practice Recommendations**: Provide code optimization and architectural improvement suggestions that conform to industry standards\\n4. **Knowledge Answers**: Answer concept-related programming questions with clear and easy-to-understand explanations\\n5. **General Problem Solving**: Solve non-programming questions raised by users, providing accurate and useful information\\n\\n\\n### Response Guidelines\\n- **Respond directly** to user queries without standard greetings, maintaining a professional yet approachable tone\\n- **Match your communication style** to the complexity and context of the user's question\\n- **Deliver immediate value** by addressing the core query first, avoiding templated introductions or unnecessary preamble\\n- **Analyze and address specific user needs** directly upon receiving a question, focusing on their exact requirements\\n- **MANDATORY: Use exactly one tool per response** - Every message must include tool usage; responses without tool utilization are not acceptable\\n\\n\\n### Response Process\\n1. **Understanding Requirements**: Carefully analyze user questions to determine core requirement points\\n2. **Code Analysis** (if applicable): Check code logic, syntax, and potential issues\\n3. **Building Answers**:\\n   - For programming problems: Provide clear explanations, executable code examples, and solutions\\n   - For conceptual questions: Give concise explanations, supplemented with examples\\n   - For general questions: Provide accurate, objective information and advice\\n4. **Solution Verification**: Ensure correctness and effectiveness before providing code\\n5. **Summary and Recommendations**: Provide additional improvement suggestions or learning resources (if applicable)\\n\\n\\n### Output Format\\n- Use clear headings and structured paragraphs\\n- Use appropriate code block formatting for code examples\\n- Highlight important concepts or key points using bold text or lists\\n- Present complex solutions step by step\\n\\n\\n### Code of Conduct\\n- Comply with Chinese laws and regulations, uphold socialist core values\\n- Provide objective, neutral, and beneficial information\\n- When uncertain about answers, honestly acknowledge and provide possible reference directions\\n- Respect users, use professional but friendly tone\\n- Avoid generating harmful, misleading, or inappropriate content\\n\\n\\nIMPORTANT: Begin your responses by directly addressing the user's query without using standard greeting templates. Adapt your communication style to the specific question being asked.</role>\\n<custom_instructions>\\n====\\n\\nUSER'S CUSTOM INSTRUCTIONS\\n\\nThe following additional instructions are provided by the user, and should be followed to the best of your ability without interfering with the TOOL USE guidelines.\\n\\n====\\nMode-specific Instructions:\\n\\n***You should answer user questions directly. Please ensure your responses are concise and clear.***\\n\\n</custom_instructions>\\n\\n# Context Window Usage\\n0 / 128K tokens used (0%)\\n\\n# Current Style\\n</environment_details>\",\"tokensIn\":0,\"tokensOut\":0,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}", "conversationHistoryIndex": 1}, {"ts": 1749295198765, "type": "say", "say": "text", "text": "{\"text\":\"让我来解析图中 VSCode DevTools 的各个部分：\",\"conversationId\":\"task-40625e4f-e7bc-4f8a-8960-a481f88b2d1f_session-49a63f04\",\"taskId\":\"task-40625e4f-e7bc-4f8a-8960-a481f88b2d1f\",\"sessionId\":\"session-49a63f04\"}", "partial": false, "conversationHistoryIndex": 2}, {"ts": 1749295199187, "type": "say", "say": "tool", "text": "{\"tool\":\"webSearch\",\"query\":\"vscode devtools electron isolated context performance metrics\"}", "partial": false, "conversationHistoryIndex": 2}, {"ts": 1749295204998, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[use_web_search for 'vscode devtools electron isolated context performance metrics'] Result:\\n\\n[use_web_search for 'vscode devtools electron isolated context performance metrics'] Result:\\n\\n(use_web_search tool does not return anything)\\n\\n<environment_details>\\n# VSCode Visible Files\\n../../.joycode/joycoder-mcp.json\\n\\n# VSCode Open Tabs\\n../../.joycode/joycoder-mcp.json\\n\\n# Current Time\\n2025/6/7 下午7:20:05 (Asia/Shanghai, UTC+8:00)\\n\\nNOTE: You are currently in '聊天' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as '聊天' mode.\\n\\n# Current Mode\\n<agentId>chat</agentId>\\n<name>聊天</name>\\n<model>Claude-3.5-sonnet</model>\\n<role>You are <PERSON><PERSON><PERSON>, a professional intelligent programming assistant. You excel at solving various programming problems across multiple languages (including but not limited to Python, JavaScript, Java, C++, Go, SQL, etc.).\\n\\n\\n### Core Capabilities\\n1. **Code Understanding and Analysis**: Deeply understand user-provided code, accurately grasp its functionality and intent\\n2. **Problem Diagnosis and Resolution**: Precisely identify errors, performance bottlenecks, and logical issues in code\\n3. **Best Practice Recommendations**: Provide code optimization and architectural improvement suggestions that conform to industry standards\\n4. **Knowledge Answers**: Answer concept-related programming questions with clear and easy-to-understand explanations\\n5. **General Problem Solving**: Solve non-programming questions raised by users, providing accurate and useful information\\n\\n\\n### Response Guidelines\\n- **Respond directly** to user queries without standard greetings, maintaining a professional yet approachable tone\\n- **Match your communication style** to the complexity and context of the user's question\\n- **Deliver immediate value** by addressing the core query first, avoiding templated introductions or unnecessary preamble\\n- **Analyze and address specific user needs** directly upon receiving a question, focusing on their exact requirements\\n- **MANDATORY: Use exactly one tool per response** - Every message must include tool usage; responses without tool utilization are not acceptable\\n\\n\\n### Response Process\\n1. **Understanding Requirements**: Carefully analyze user questions to determine core requirement points\\n2. **Code Analysis** (if applicable): Check code logic, syntax, and potential issues\\n3. **Building Answers**:\\n   - For programming problems: Provide clear explanations, executable code examples, and solutions\\n   - For conceptual questions: Give concise explanations, supplemented with examples\\n   - For general questions: Provide accurate, objective information and advice\\n4. **Solution Verification**: Ensure correctness and effectiveness before providing code\\n5. **Summary and Recommendations**: Provide additional improvement suggestions or learning resources (if applicable)\\n\\n\\n### Output Format\\n- Use clear headings and structured paragraphs\\n- Use appropriate code block formatting for code examples\\n- Highlight important concepts or key points using bold text or lists\\n- Present complex solutions step by step\\n\\n\\n### Code of Conduct\\n- Comply with Chinese laws and regulations, uphold socialist core values\\n- Provide objective, neutral, and beneficial information\\n- When uncertain about answers, honestly acknowledge and provide possible reference directions\\n- Respect users, use professional but friendly tone\\n- Avoid generating harmful, misleading, or inappropriate content\\n\\n\\nIMPORTANT: Begin your responses by directly addressing the user's query without using standard greeting templates. Adapt your communication style to the specific question being asked.</role>\\n<custom_instructions>\\n====\\n\\nUSER'S CUSTOM INSTRUCTIONS\\n\\nThe following additional instructions are provided by the user, and should be followed to the best of your ability without interfering with the TOOL USE guidelines.\\n\\n====\\nMode-specific Instructions:\\n\\n***You should answer user questions directly. Please ensure your responses are concise and clear.***\\n\\n</custom_instructions>\\n\\n# Context Window Usage\\n0 / 128K tokens used (0%)\\n\\n# Current Style\\n</environment_details>\",\"tokensIn\":13920,\"tokensOut\":457,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}", "conversationHistoryIndex": 3}, {"ts": 1749295209330, "type": "say", "say": "text", "text": "{\"text\":\"让我详细解析图中 VSCode DevTools 显示的各个层级结构：\",\"conversationId\":\"task-40625e4f-e7bc-4f8a-8960-a481f88b2d1f_session-49a63f04\",\"taskId\":\"task-40625e4f-e7bc-4f8a-8960-a481f88b2d1f\",\"sessionId\":\"session-49a63f04\"}", "partial": false, "conversationHistoryIndex": 4}, {"ts": 1749295209780, "type": "say", "say": "completion_result", "text": "{\"text\":\"DevTools 中显示的层级结构代表了 VSCode 的运行时组件层次：\\n\\n1. **top / vscode-app**\\n- 最顶层容器，代表整个 VSCode 应用窗口\\n- 作为所有子组件的根容器\\n\\n2. **DevTools Performance Metrics**\\n- Chrome DevTools 的性能监控面板\\n- 用于追踪和分析应用性能指标\\n\\n3. **Electron Isolated Context**\\n- Electron 的沙箱隔离环境\\n- 提供安全的渲染进程运行环境\\n- 将网页内容与系统原生功能隔离\\n\\n4. **6106c724... (index.html)**\\n- 一个带唯一标识符的页面实例\\n- 通常是 VSCode 的主要工作区页面\\n\\n5. **pending-frame (index.html)**\\n- 正在加载或等待处理的页面帧\\n- 显示页面加载状态\\n\\n6. **service-worker #2010 (activated)**\\n- VSCode 的活跃服务工作线程\\n- 处理离线缓存、后台同步等功能\\n\\n7. **TextEditorWorker**\\n- 文本编辑器的专用工作线程\\n- 处理代码编辑、语法高亮等功能\\n\\n这种层级结构反映了 VSCode 基于 Electron 的架构设计，展示了从应用容器到具体功能组件的完整层次关系。\",\"conversationId\":\"task-40625e4f-e7bc-4f8a-8960-a481f88b2d1f_session-49a63f04\",\"taskId\":\"task-40625e4f-e7bc-4f8a-8960-a481f88b2d1f\",\"sessionId\":\"session-49a63f04\"}", "partial": false, "conversationHistoryIndex": 4, "lastCheckpointHash": "5b6c037efe5afbb146db3adc8bce8ce82a019c92"}, {"ts": 1749295218694, "type": "ask", "ask": "completion_result", "text": "", "conversationHistoryIndex": 4}]