{"environment": {"executable": "/opt/homebrew/opt/python@3.12/bin/python3.12", "prefix": "/opt/homebrew/opt/python@3.12/Frameworks/Python.framework/Versions/3.12", "version": "3.12.9.final.0", "is64Bit": true, "symlinks": ["/opt/homebrew/bin/python3.12", "/opt/homebrew/opt/python@3.12/bin/python3.12"]}, "symlinks": [["/opt/homebrew/bin/python3.12", {"secs_since_epoch": 1742780386, "nanos_since_epoch": 668380218}, {"secs_since_epoch": 1742780386, "nanos_since_epoch": 644532216}], ["/opt/homebrew/opt/python@3.12/bin/python3.12", {"secs_since_epoch": 1742780386, "nanos_since_epoch": 668380218}, {"secs_since_epoch": 1742780386, "nanos_since_epoch": 644532216}]]}