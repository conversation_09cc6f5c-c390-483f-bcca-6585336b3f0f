"use strict";var be=Object.create;var ee=Object.defineProperty;var ve=Object.getOwnPropertyDescriptor;var we=Object.getOwnPropertyNames;var ye=Object.getPrototypeOf,Se=Object.prototype.hasOwnProperty;var Pe=(e,t,n,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of we(t))!Se.call(e,o)&&o!==n&&ee(e,o,{get:()=>t[o],enumerable:!(i=ve(t,o))||i.enumerable});return e};var Le=(e,t,n)=>(n=e!=null?be(ye(e)):{},Pe(t||!e||!e.__esModule?ee(n,"default",{value:e,enumerable:!0}):n,e));var me=require("crypto"),xe=require("node:net"),Ce=require("os"),J=Le(require("readline"));function D(){return globalThis._VSCODE_NLS_LANGUAGE}var st=D()==="pseudo"||typeof document<"u"&&document.location&&typeof document.location.hash=="string"&&document.location.hash.indexOf("pseudo=true")>=0;var k="en",V=!1,H=!1,j=!1,_e=!1,$e=!1,ne=!1,Ne=!1,Re=!1,Oe=!1,ke=!1,z,U=k,te=k,Me,y,S=globalThis,b;typeof S.vscode<"u"&&typeof S.vscode.process<"u"?b=S.vscode.process:typeof process<"u"&&typeof process?.versions?.node=="string"&&(b=process);var re=typeof b?.versions?.electron=="string",De=re&&b?.type==="renderer";if(typeof b=="object"){V=b.platform==="win32",H=b.platform==="darwin",j=b.platform==="linux",_e=j&&!!b.env.SNAP&&!!b.env.SNAP_REVISION,Ne=re,Oe=!!b.env.CI||!!b.env.BUILD_ARTIFACTSTAGINGDIRECTORY,z=k,U=k;let e=b.env.VSCODE_NLS_CONFIG;if(e)try{let t=JSON.parse(e);z=t.userLocale,te=t.osLocale,U=t.resolvedLanguage||k,Me=t.languagePack?.translationsConfigFile}catch{}$e=!0}else typeof navigator=="object"&&!De?(y=navigator.userAgent,V=y.indexOf("Windows")>=0,H=y.indexOf("Macintosh")>=0,Re=(y.indexOf("Macintosh")>=0||y.indexOf("iPad")>=0||y.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,j=y.indexOf("Linux")>=0,ke=y?.indexOf("Mobi")>=0,ne=!0,U=D()||k,z=navigator.language.toLowerCase(),te=z):console.error("Unable to resolve platform.");var Q=0;H?Q=1:V?Q=3:j&&(Q=2);var se=V,ie=H;var We=ne&&typeof S.importScripts=="function",ot=We?S.origin:void 0;var w=y,_=U,Fe;(i=>{function e(){return _}i.value=e;function t(){return _.length===2?_==="en":_.length>=3?_[0]==="e"&&_[1]==="n"&&_[2]==="-":!1}i.isDefaultVariant=t;function n(){return _==="en"}i.isDefault=n})(Fe||={});var Te=typeof S.postMessage=="function"&&!S.importScripts,lt=(()=>{if(Te){let e=[];S.addEventListener("message",n=>{if(n.data&&n.data.vscodeScheduleAsyncWork)for(let i=0,o=e.length;i<o;i++){let r=e[i];if(r.id===n.data.vscodeScheduleAsyncWork){e.splice(i,1),r.callback();return}}});let t=0;return n=>{let i=++t;e.push({id:i,callback:n}),S.postMessage({vscodeScheduleAsyncWork:i},"*")}}return e=>setTimeout(e)})();var ze=!!(w&&w.indexOf("Chrome")>=0),at=!!(w&&w.indexOf("Firefox")>=0),ct=!!(!ze&&w&&w.indexOf("Safari")>=0),ft=!!(w&&w.indexOf("Edg/")>=0),dt=!!(w&&w.indexOf("Android")>=0);var N,Z=globalThis.vscode;if(typeof Z<"u"&&typeof Z.process<"u"){let e=Z.process;N={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else typeof process<"u"&&typeof process?.versions?.node=="string"?N={get platform(){return process.platform},get arch(){return process.arch},get env(){return process.env},cwd(){return process.env.VSCODE_CWD||process.cwd()}}:N={get platform(){return se?"win32":ie?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};var W=N.cwd,oe=N.env,le=N.platform,pt=N.arch;var Ue=65,Ve=97,He=90,qe=122,R=46,x=47,C=92,P=58,Ge=63,q=class extends Error{constructor(t,n,i){let o;typeof n=="string"&&n.indexOf("not ")===0?(o="must not be",n=n.replace(/^not /,"")):o="must be";let r=t.indexOf(".")!==-1?"property":"argument",l=`The "${t}" ${r} ${o} of type ${n}`;l+=`. Received type ${typeof i}`,super(l),this.code="ERR_INVALID_ARG_TYPE"}};function Be(e,t){if(e===null||typeof e!="object")throw new q(t,"Object",e)}function h(e,t){if(typeof e!="string")throw new q(t,"string",e)}var A=le==="win32";function d(e){return e===x||e===C}function X(e){return e===x}function L(e){return e>=Ue&&e<=He||e>=Ve&&e<=qe}function G(e,t,n,i){let o="",r=0,l=-1,s=0,a=0;for(let c=0;c<=e.length;++c){if(c<e.length)a=e.charCodeAt(c);else{if(i(a))break;a=x}if(i(a)){if(!(l===c-1||s===1))if(s===2){if(o.length<2||r!==2||o.charCodeAt(o.length-1)!==R||o.charCodeAt(o.length-2)!==R){if(o.length>2){let u=o.lastIndexOf(n);u===-1?(o="",r=0):(o=o.slice(0,u),r=o.length-1-o.lastIndexOf(n)),l=c,s=0;continue}else if(o.length!==0){o="",r=0,l=c,s=0;continue}}t&&(o+=o.length>0?`${n}..`:"..",r=2)}else o.length>0?o+=`${n}${e.slice(l+1,c)}`:o=e.slice(l+1,c),r=c-l-1;l=c,s=0}else a===R&&s!==-1?++s:s=-1}return o}function Ke(e){return e?`${e[0]==="."?"":"."}${e}`:""}function ae(e,t){Be(t,"pathObject");let n=t.dir||t.root,i=t.base||`${t.name||""}${Ke(t.ext)}`;return n?n===t.root?`${n}${i}`:`${n}${e}${i}`:i}var p={resolve(...e){let t="",n="",i=!1;for(let o=e.length-1;o>=-1;o--){let r;if(o>=0){if(r=e[o],h(r,`paths[${o}]`),r.length===0)continue}else t.length===0?r=W():(r=oe[`=${t}`]||W(),(r===void 0||r.slice(0,2).toLowerCase()!==t.toLowerCase()&&r.charCodeAt(2)===C)&&(r=`${t}\\`));let l=r.length,s=0,a="",c=!1,u=r.charCodeAt(0);if(l===1)d(u)&&(s=1,c=!0);else if(d(u))if(c=!0,d(r.charCodeAt(1))){let f=2,g=f;for(;f<l&&!d(r.charCodeAt(f));)f++;if(f<l&&f!==g){let O=r.slice(g,f);for(g=f;f<l&&d(r.charCodeAt(f));)f++;if(f<l&&f!==g){for(g=f;f<l&&!d(r.charCodeAt(f));)f++;(f===l||f!==g)&&(a=`\\\\${O}\\${r.slice(g,f)}`,s=f)}}}else s=1;else L(u)&&r.charCodeAt(1)===P&&(a=r.slice(0,2),s=2,l>2&&d(r.charCodeAt(2))&&(c=!0,s=3));if(a.length>0)if(t.length>0){if(a.toLowerCase()!==t.toLowerCase())continue}else t=a;if(i){if(t.length>0)break}else if(n=`${r.slice(s)}\\${n}`,i=c,c&&t.length>0)break}return n=G(n,!i,"\\",d),i?`${t}\\${n}`:`${t}${n}`||"."},normalize(e){h(e,"path");let t=e.length;if(t===0)return".";let n=0,i,o=!1,r=e.charCodeAt(0);if(t===1)return X(r)?"\\":e;if(d(r))if(o=!0,d(e.charCodeAt(1))){let s=2,a=s;for(;s<t&&!d(e.charCodeAt(s));)s++;if(s<t&&s!==a){let c=e.slice(a,s);for(a=s;s<t&&d(e.charCodeAt(s));)s++;if(s<t&&s!==a){for(a=s;s<t&&!d(e.charCodeAt(s));)s++;if(s===t)return`\\\\${c}\\${e.slice(a)}\\`;s!==a&&(i=`\\\\${c}\\${e.slice(a,s)}`,n=s)}}}else n=1;else L(r)&&e.charCodeAt(1)===P&&(i=e.slice(0,2),n=2,t>2&&d(e.charCodeAt(2))&&(o=!0,n=3));let l=n<t?G(e.slice(n),!o,"\\",d):"";if(l.length===0&&!o&&(l="."),l.length>0&&d(e.charCodeAt(t-1))&&(l+="\\"),!o&&i===void 0&&e.includes(":")){if(l.length>=2&&L(l.charCodeAt(0))&&l.charCodeAt(1)===P)return`.\\${l}`;let s=e.indexOf(":");do if(s===t-1||d(e.charCodeAt(s+1)))return`.\\${l}`;while((s=e.indexOf(":",s+1))!==-1)}return i===void 0?o?`\\${l}`:l:o?`${i}\\${l}`:`${i}${l}`},isAbsolute(e){h(e,"path");let t=e.length;if(t===0)return!1;let n=e.charCodeAt(0);return d(n)||t>2&&L(n)&&e.charCodeAt(1)===P&&d(e.charCodeAt(2))},join(...e){if(e.length===0)return".";let t,n;for(let r=0;r<e.length;++r){let l=e[r];h(l,"path"),l.length>0&&(t===void 0?t=n=l:t+=`\\${l}`)}if(t===void 0)return".";let i=!0,o=0;if(typeof n=="string"&&d(n.charCodeAt(0))){++o;let r=n.length;r>1&&d(n.charCodeAt(1))&&(++o,r>2&&(d(n.charCodeAt(2))?++o:i=!1))}if(i){for(;o<t.length&&d(t.charCodeAt(o));)o++;o>=2&&(t=`\\${t.slice(o)}`)}return p.normalize(t)},relative(e,t){if(h(e,"from"),h(t,"to"),e===t)return"";let n=p.resolve(e),i=p.resolve(t);if(n===i||(e=n.toLowerCase(),t=i.toLowerCase(),e===t))return"";if(n.length!==e.length||i.length!==t.length){let E=n.split("\\"),$=i.split("\\");E[E.length-1]===""&&E.pop(),$[$.length-1]===""&&$.pop();let M=E.length,Y=$.length,T=M<Y?M:Y,v;for(v=0;v<T&&E[v].toLowerCase()===$[v].toLowerCase();v++);return v===0?i:v===T?Y>T?$.slice(v).join("\\"):M>T?"..\\".repeat(M-1-v)+"..":"":"..\\".repeat(M-v)+$.slice(v).join("\\")}let o=0;for(;o<e.length&&e.charCodeAt(o)===C;)o++;let r=e.length;for(;r-1>o&&e.charCodeAt(r-1)===C;)r--;let l=r-o,s=0;for(;s<t.length&&t.charCodeAt(s)===C;)s++;let a=t.length;for(;a-1>s&&t.charCodeAt(a-1)===C;)a--;let c=a-s,u=l<c?l:c,f=-1,g=0;for(;g<u;g++){let E=e.charCodeAt(o+g);if(E!==t.charCodeAt(s+g))break;E===C&&(f=g)}if(g!==u){if(f===-1)return i}else{if(c>u){if(t.charCodeAt(s+g)===C)return i.slice(s+g+1);if(g===2)return i.slice(s+g)}l>u&&(e.charCodeAt(o+g)===C?f=g:g===2&&(f=3)),f===-1&&(f=0)}let O="";for(g=o+f+1;g<=r;++g)(g===r||e.charCodeAt(g)===C)&&(O+=O.length===0?"..":"\\..");return s+=f,O.length>0?`${O}${i.slice(s,a)}`:(i.charCodeAt(s)===C&&++s,i.slice(s,a))},toNamespacedPath(e){if(typeof e!="string"||e.length===0)return e;let t=p.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===C){if(t.charCodeAt(1)===C){let n=t.charCodeAt(2);if(n!==Ge&&n!==R)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(L(t.charCodeAt(0))&&t.charCodeAt(1)===P&&t.charCodeAt(2)===C)return`\\\\?\\${t}`;return t},dirname(e){h(e,"path");let t=e.length;if(t===0)return".";let n=-1,i=0,o=e.charCodeAt(0);if(t===1)return d(o)?e:".";if(d(o)){if(n=i=1,d(e.charCodeAt(1))){let s=2,a=s;for(;s<t&&!d(e.charCodeAt(s));)s++;if(s<t&&s!==a){for(a=s;s<t&&d(e.charCodeAt(s));)s++;if(s<t&&s!==a){for(a=s;s<t&&!d(e.charCodeAt(s));)s++;if(s===t)return e;s!==a&&(n=i=s+1)}}}}else L(o)&&e.charCodeAt(1)===P&&(n=t>2&&d(e.charCodeAt(2))?3:2,i=n);let r=-1,l=!0;for(let s=t-1;s>=i;--s)if(d(e.charCodeAt(s))){if(!l){r=s;break}}else l=!1;if(r===-1){if(n===-1)return".";r=n}return e.slice(0,r)},basename(e,t){t!==void 0&&h(t,"suffix"),h(e,"path");let n=0,i=-1,o=!0,r;if(e.length>=2&&L(e.charCodeAt(0))&&e.charCodeAt(1)===P&&(n=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let l=t.length-1,s=-1;for(r=e.length-1;r>=n;--r){let a=e.charCodeAt(r);if(d(a)){if(!o){n=r+1;break}}else s===-1&&(o=!1,s=r+1),l>=0&&(a===t.charCodeAt(l)?--l===-1&&(i=r):(l=-1,i=s))}return n===i?i=s:i===-1&&(i=e.length),e.slice(n,i)}for(r=e.length-1;r>=n;--r)if(d(e.charCodeAt(r))){if(!o){n=r+1;break}}else i===-1&&(o=!1,i=r+1);return i===-1?"":e.slice(n,i)},extname(e){h(e,"path");let t=0,n=-1,i=0,o=-1,r=!0,l=0;e.length>=2&&e.charCodeAt(1)===P&&L(e.charCodeAt(0))&&(t=i=2);for(let s=e.length-1;s>=t;--s){let a=e.charCodeAt(s);if(d(a)){if(!r){i=s+1;break}continue}o===-1&&(r=!1,o=s+1),a===R?n===-1?n=s:l!==1&&(l=1):n!==-1&&(l=-1)}return n===-1||o===-1||l===0||l===1&&n===o-1&&n===i+1?"":e.slice(n,o)},format:ae.bind(null,"\\"),parse(e){h(e,"path");let t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;let n=e.length,i=0,o=e.charCodeAt(0);if(n===1)return d(o)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(d(o)){if(i=1,d(e.charCodeAt(1))){let f=2,g=f;for(;f<n&&!d(e.charCodeAt(f));)f++;if(f<n&&f!==g){for(g=f;f<n&&d(e.charCodeAt(f));)f++;if(f<n&&f!==g){for(g=f;f<n&&!d(e.charCodeAt(f));)f++;f===n?i=f:f!==g&&(i=f+1)}}}}else if(L(o)&&e.charCodeAt(1)===P){if(n<=2)return t.root=t.dir=e,t;if(i=2,d(e.charCodeAt(2))){if(n===3)return t.root=t.dir=e,t;i=3}}i>0&&(t.root=e.slice(0,i));let r=-1,l=i,s=-1,a=!0,c=e.length-1,u=0;for(;c>=i;--c){if(o=e.charCodeAt(c),d(o)){if(!a){l=c+1;break}continue}s===-1&&(a=!1,s=c+1),o===R?r===-1?r=c:u!==1&&(u=1):r!==-1&&(u=-1)}return s!==-1&&(r===-1||u===0||u===1&&r===s-1&&r===l+1?t.base=t.name=e.slice(l,s):(t.name=e.slice(l,r),t.base=e.slice(l,s),t.ext=e.slice(r,s))),l>0&&l!==i?t.dir=e.slice(0,l-1):t.dir=t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},Je=(()=>{if(A){let e=/\\/g;return()=>{let t=W().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>W()})(),m={resolve(...e){let t="",n=!1;for(let i=e.length-1;i>=0&&!n;i--){let o=e[i];h(o,`paths[${i}]`),o.length!==0&&(t=`${o}/${t}`,n=o.charCodeAt(0)===x)}if(!n){let i=Je();t=`${i}/${t}`,n=i.charCodeAt(0)===x}return t=G(t,!n,"/",X),n?`/${t}`:t.length>0?t:"."},normalize(e){if(h(e,"path"),e.length===0)return".";let t=e.charCodeAt(0)===x,n=e.charCodeAt(e.length-1)===x;return e=G(e,!t,"/",X),e.length===0?t?"/":n?"./":".":(n&&(e+="/"),t?`/${e}`:e)},isAbsolute(e){return h(e,"path"),e.length>0&&e.charCodeAt(0)===x},join(...e){if(e.length===0)return".";let t=[];for(let n=0;n<e.length;++n){let i=e[n];h(i,"path"),i.length>0&&t.push(i)}return t.length===0?".":m.normalize(t.join("/"))},relative(e,t){if(h(e,"from"),h(t,"to"),e===t||(e=m.resolve(e),t=m.resolve(t),e===t))return"";let n=1,i=e.length,o=i-n,r=1,l=t.length-r,s=o<l?o:l,a=-1,c=0;for(;c<s;c++){let f=e.charCodeAt(n+c);if(f!==t.charCodeAt(r+c))break;f===x&&(a=c)}if(c===s)if(l>s){if(t.charCodeAt(r+c)===x)return t.slice(r+c+1);if(c===0)return t.slice(r+c)}else o>s&&(e.charCodeAt(n+c)===x?a=c:c===0&&(a=0));let u="";for(c=n+a+1;c<=i;++c)(c===i||e.charCodeAt(c)===x)&&(u+=u.length===0?"..":"/..");return`${u}${t.slice(r+a)}`},toNamespacedPath(e){return e},dirname(e){if(h(e,"path"),e.length===0)return".";let t=e.charCodeAt(0)===x,n=-1,i=!0;for(let o=e.length-1;o>=1;--o)if(e.charCodeAt(o)===x){if(!i){n=o;break}}else i=!1;return n===-1?t?"/":".":t&&n===1?"//":e.slice(0,n)},basename(e,t){t!==void 0&&h(t,"suffix"),h(e,"path");let n=0,i=-1,o=!0,r;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let l=t.length-1,s=-1;for(r=e.length-1;r>=0;--r){let a=e.charCodeAt(r);if(a===x){if(!o){n=r+1;break}}else s===-1&&(o=!1,s=r+1),l>=0&&(a===t.charCodeAt(l)?--l===-1&&(i=r):(l=-1,i=s))}return n===i?i=s:i===-1&&(i=e.length),e.slice(n,i)}for(r=e.length-1;r>=0;--r)if(e.charCodeAt(r)===x){if(!o){n=r+1;break}}else i===-1&&(o=!1,i=r+1);return i===-1?"":e.slice(n,i)},extname(e){h(e,"path");let t=-1,n=0,i=-1,o=!0,r=0;for(let l=e.length-1;l>=0;--l){let s=e[l];if(s==="/"){if(!o){n=l+1;break}continue}i===-1&&(o=!1,i=l+1),s==="."?t===-1?t=l:r!==1&&(r=1):t!==-1&&(r=-1)}return t===-1||i===-1||r===0||r===1&&t===i-1&&t===n+1?"":e.slice(t,i)},format:ae.bind(null,"/"),parse(e){h(e,"path");let t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;let n=e.charCodeAt(0)===x,i;n?(t.root="/",i=1):i=0;let o=-1,r=0,l=-1,s=!0,a=e.length-1,c=0;for(;a>=i;--a){let u=e.charCodeAt(a);if(u===x){if(!s){r=a+1;break}continue}l===-1&&(s=!1,l=a+1),u===R?o===-1?o=a:c!==1&&(c=1):o!==-1&&(c=-1)}if(l!==-1){let u=r===0&&n?1:r;o===-1||c===0||c===1&&o===l-1&&o===r+1?t.base=t.name=e.slice(u,l):(t.name=e.slice(u,o),t.base=e.slice(u,l),t.ext=e.slice(o,l))}return r>0?t.dir=e.slice(0,r-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};m.win32=p.win32=p;m.posix=p.posix=m;var mt=A?p.normalize:m.normalize,xt=A?p.isAbsolute:m.isAbsolute,ce=A?p.join:m.join,Ct=A?p.resolve:m.resolve,At=A?p.relative:m.relative,bt=A?p.dirname:m.dirname,vt=A?p.basename:m.basename,wt=A?p.extname:m.extname,yt=A?p.format:m.format,St=A?p.parse:m.parse,Pt=A?p.toNamespacedPath:m.toNamespacedPath,Lt=A?p.sep:m.sep,It=A?p.delimiter:m.delimiter;var fe=require("child_process"),de=(e,t)=>{let n,i=!1,o=[t];if(e){let[r,...l]=e.split(" ");n=r,o=[...l,t]}else switch(process.platform){case"win32":n="cmd",i=!0,o=["/c","start",'""',`"${t}"`];break;case"darwin":n="open";break;case"linux":default:n="xdg-open";break}return new Promise((r,l)=>{let s="",a=(0,fe.spawn)(n,o,{stdio:"pipe",shell:i,env:{...process.env,ELECTRON_RUN_AS_NODE:void 0}});a.stdout.setEncoding("utf8").on("data",c=>s+=c),a.stderr.setEncoding("utf8").on("data",c=>s+=c),a.on("error",l),a.on("exit",c=>{c!==0?l(new Error(`Failed to open: ${s}`)):r()})})};var ge=require("stream"),B=class extends ge.Transform{constructor(n){super();this.prefix=[];this.splitSuffix=Buffer.alloc(0);if(typeof n=="string"&&n.length===1)this.splitter=n.charCodeAt(0);else if(typeof n=="number")this.splitter=n;else throw new Error("not implemented here")}_transform(n,i,o){let r=0;for(;r<n.length;){let l=n.indexOf(this.splitter,r);if(l===-1)break;let s=n.subarray(r,l),a=this.prefix.length||this.splitSuffix.length?Buffer.concat([...this.prefix,s,this.splitSuffix]):s;this.push(a),this.prefix.length=0,r=l+1}r<n.length&&this.prefix.push(n.subarray(r)),o()}_flush(n){this.prefix.length&&this.push(Buffer.concat([...this.prefix,this.splitSuffix])),n()}};var ue=process.platform==="win32"?`\r
`:`
`,K=class{constructor(t){this.stream=t;this.methods=new Map;this.pendingRequests=new Map;this.idCounter=0,this.stream.pipe(new B(`
`)).on("data",n=>this.handleData(n)),this.ended=new Promise(n=>this.stream.on("end",()=>{this.didEnd=!0,n()}))}registerMethod(t,n){this.methods.set(t,n)}async callMethod(t,n){let i=this.idCounter++,o={id:i,method:t,params:n},r=new Promise((l,s)=>{this.pendingRequests.set(i,{resolve:l,reject:s})});return this.stream.write(JSON.stringify(o)+ue),Promise.race([r,this.ended])}dispose(){this.didEnd=!0,this.stream.end();for(let{reject:t}of this.pendingRequests.values())t(new Error("RPC connection closed"));this.pendingRequests.clear()}async handleData(t){let n=JSON.parse(t.toString());if("method"in n){let{id:i,method:o,params:r}=n,l={id:i};try{if(this.methods.has(o)){let s=await this.methods.get(o)(r);l.result=s}else throw new Error(`Method not found: ${o}`)}catch(s){l.error={code:-1,message:String(s.stack||s)}}this.didEnd||this.stream.write(JSON.stringify(l)+ue)}else{let{id:i,result:o,error:r}=n,l=this.pendingRequests.get(i);this.pendingRequests.delete(i),r!==void 0?l?.reject(new Error(r.message)):l?.resolve(o)}}};var[kt,Mt,Qe,Ze,...F]=process.argv;var I={"--print":!1,"--no-cache":!1,"--help":!1,"--save":!1,"--once":!1};for(;F.length&&I.hasOwnProperty(F[0]);)I[F.shift()]=!0;(!F.length||I["--help"])&&(console.log(`Usage: copilot-debug [${Object.keys(I).join("] [")}] <command> <args...>`),console.log(""),console.log("Options:"),console.log("  --print     Print the generated configuration without running it"),console.log("  --no-cache  Generate a new configuration without checking the cache."),console.log("  --save      Save the configuration to your launch.json."),console.log("  --once      Exit after the debug session ends."),console.log("  --help      Print this help."),process.exit(I["--help"]?0:1));var he=J.createInterface({input:process.stdin,output:process.stdout});J.emitKeypressEvents(process.stdin);process.stdin.setRawMode(!0);var Xe=(0,xe.createServer)(e=>{clearInterval(et);let t=new K(e);t.registerMethod("output",({category:r,output:l})=>(r==="stderr"?process.stderr.write(l):r==="stdout"?process.stdout.write(l):r!=="telemetry"&&l&&console.log(l),Promise.resolve())),t.registerMethod("exit",async({code:r,error:l})=>{l&&!n&&console.error(l),await Promise.all([new Promise(s=>process.stdout.end(s)),new Promise(s=>process.stderr.end(s))]).then(()=>process.exit(r))});let n=!1;function i(){n?process.exit(1):(n=!0,e.end(()=>{process.exit(1)}))}process.on("SIGINT",i),process.stdin.on("keypress",(r,l)=>{(l.sequence===""||l.name==="c"&&(l.ctrl||l.meta))&&i()}),t.registerMethod("question",r=>new Promise(l=>{if(r.singleKey){console.log(r.message);let s=a=>{a&&(process.stdout.write("\b"),process.stdin.off("keypress",s),l(a===`
`||a==="\r"?"Enter":a?.toUpperCase()||""))};process.stdin.on("keypress",s)}else he.question(`${r.message} [${r.defaultValue}] `,l)})),t.registerMethod("confirm",r=>new Promise(l=>{he.question(`${r.message} [${r.defaultValue?"Y/n":"y/N"}] `,s=>{l(s===""?r.defaultValue:s.toLowerCase()[0]==="y")})}));let o={cwd:process.cwd(),args:F,forceNew:I["--no-cache"],printOnly:I["--print"],save:I["--save"],once:I["--once"]};t.callMethod("start",o)}),et=setInterval(()=>{console.log("> Waiting for VS Code to connect...")},2e3),Ae=`copilot-dbg.${process.pid}-${(0,me.randomBytes)(4).toString("hex")}.sock`,pe=ce(process.platform==="win32"?"\\\\.\\pipe\\":(0,Ce.tmpdir)(),Ae);Xe.listen(pe,()=>{de(Ze,Qe+(process.platform==="win32"?`/${Ae}`:pe)).then(()=>{},e=>{console.error("Failed to open the activation URI:",e),process.exit(1)})});
//!!! DO NOT modify, this file was COPIED from 'microsoft/vscode'
